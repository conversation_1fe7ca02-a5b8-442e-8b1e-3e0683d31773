import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:fringle_app/helpers/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CustomDropdownNew extends StatelessWidget {
  final List<String> items;
  final String? selectedValue;
  final ValueChanged<String> onChanged;
  final String labelText;
  final String hintText;
  final bool isEnable;
  final FormFieldValidator<String>? validator;

  const CustomDropdownNew({super.key, 
    required this.items,
    required this.selectedValue,
    required this.onChanged,
    required this.labelText,
    required this.hintText,
    this.isEnable = true,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            labelText,
            style: const TextStyle(
              fontFamily: AppConstants.fontStyleName,
              color: Color(0xff6A6A6A),
              fontSize: 12,
              fontWeight: FontWeight.w400,
              // Additional InputDecoration properties...
            ),
          ),
          const SizedBox(
            height: 6,
          ),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.all(Radius.circular(12)),
              border: Border.all(color: const Color(0xFFB5B4B4), width: 1),
            ),
            padding: const EdgeInsets.fromLTRB(5, 5, 15, 5),
            width: double.infinity,
            child: DropdownButton2<String>(
                iconStyleData: IconStyleData(
                  icon: SvgPicture.asset(
                    AppConstants.dropdownIcon,
                    height: 24,
                    width: 24,
                  ),
                ),
                // customButton: SvgPicture.asset(
                //   AppConstants.polygon, alignment: Alignment.centerRight,
                //   // height: 15,
                // ),

                value: selectedValue,
                isExpanded: true,
                hint: Text(
                  hintText,
                  style: const TextStyle(
                    fontFamily: AppConstants.fontStyleName,
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    color: Colors.black,
                  ),
                  textAlign: TextAlign.start,
                ),
                items: items
                    .map((String item) => DropdownMenuItem<String>(
                          value: item,
                          child: Text(
                            item,
                            style: const TextStyle(
                              fontSize: 14,
                            ),
                          ),
                        ))
                    .toList(),
                onChanged: isEnable ? (String? value) {
                  onChanged(value ?? "");
                }: null),
          ),
        ],
      ),
    );
  }
}

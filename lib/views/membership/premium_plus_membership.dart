// import 'dart:io';

// import 'package:fringle_app/views/membership/premium_chip_widget.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_easyloading/flutter_easyloading.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:purchases_flutter/purchases_flutter.dart';
// import 'package:simple_gradient_text/simple_gradient_text.dart';

// import '../../helpers/constants.dart';
// import '../../providers/subscriptions/offerings_provider.dart';
// import '../custom/custom_app_loader.dart';
// import '../custom/new_custom_app_button.dart';
// import 'custom_feature_row_widget.dart';
// import 'membership_screen.dart';

// class PremiumPlusMembership extends StatefulWidget {
//   final bool isFirstTime;
//   final Function(bool isPurchase, int selectedOption) isPurchase;
//   final bool isPremiumUser;
//   final bool isPremiumPlusUser;

//   const PremiumPlusMembership({
//     super.key,
//     Key? key,
//     this.isFirstTime = false,
//     required this.isPurchase,
//     required this.isPremiumUser,
//     required this.isPremiumPlusUser,
//   });

//   @override
//   State<PremiumPlusMembership> createState() => _PremiumPlusMembershipState();
// }

// class _PremiumPlusMembershipState extends State<PremiumPlusMembership> {
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: SingleChildScrollView(
//         scrollDirection: Axis.vertical,
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             const SizedBox(
//               height: 24,
//             ),
//             Padding(
//               padding: EdgeInsets.symmetric(horizontal: 14),
//               child: Container(
//                 decoration: BoxDecoration(
//                   borderRadius: BorderRadius.circular(14),
//                   color: const Color(0xffF2F2F2),
//                   gradient: const LinearGradient(
//                     colors: [
//                       // Colors.yellow,Colors.red,
//                       // Colors.black,
//                       Color(0xFF421B92),
//                       Color(0xFF6332C6),
//                     ],
//                     begin: Alignment.bottomCenter,
//                     end: Alignment.topCenter,
//                     //  tileMode: TileMode.clamp,
//                     //  transform: GradientRotation(0),
//                   ),
//                 ),
//                 child: Column(
//                   children: [
//                     if (widget.isPremiumPlusUser)
//                       Container(
//                         padding:
//                             EdgeInsets.symmetric(horizontal: 20, vertical: 5),
//                         width: double.infinity,
//                         decoration: BoxDecoration(
//                             color: Colors.white.withOpacity(.4),
//                             borderRadius: BorderRadius.only(
//                                 topRight: Radius.circular(14),
//                                 topLeft: Radius.circular(14))),
//                         child: const Text(
//                           "CURRENT PLAN",
//                           style: TextStyle(
//                               color: Colors.white,
//                               fontSize: 10,
//                               fontWeight: FontWeight.w700),
//                         ),
//                       ),
//                     Padding(
//                       padding: const EdgeInsets.symmetric(
//                           horizontal: 17, vertical: 20),
//                       child: Column(
//                         children: [
//                           Column(
//                             children: [
//                               Row(
//                                 mainAxisAlignment:
//                                     MainAxisAlignment.spaceBetween,
//                                 children: [
//                                   Column(
//                                     crossAxisAlignment:
//                                         CrossAxisAlignment.start,
//                                     children: [
//                                       Text(
//                                         "Premium +",
//                                         style: TextStyle(
//                                           color: Colors.white,
//                                           fontSize: 24,
//                                           fontWeight: FontWeight.w700,
//                                         ),
//                                       ),
//                                       SizedBox(
//                                         height: 10,
//                                       ),
//                                       Text(
//                                         "For Streamers & Content Creators",
//                                         style: TextStyle(
//                                           color: Colors.white,
//                                           fontSize: 14,
//                                           fontWeight: FontWeight.w400,
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                                   SvgPicture.asset(
//                                     AppConstants.premiumPlusIcon,
//                                     // height: 15,
//                                   ),
//                                 ],
//                               ),
//                               const SizedBox(
//                                 height: 18,
//                               ),
//                               Text(
//                                 "Get access to all, Plus access to meet and collaborate with others for your platform",
//                                 style: TextStyle(
//                                   color: Colors.white.withOpacity(.7),
//                                   fontSize: 12,
//                                   fontWeight: FontWeight.w400,
//                                 ),
//                               ),
//                               const SizedBox(
//                                 height: 20,
//                               ),
//                               PremiumChipWidget(
//                                 chipList: [
//                                   "Content Creators",
//                                   "Spicy Creators",
//                                   "Influencers",
//                                 ],
//                               ),
//                               SizedBox(
//                                 height: 20,
//                               ),
//                               const CustomFeatureRow(
//                                   color: Colors.white,
//                                   name: "Swipe",
//                                   iconPath:
//                                       AppConstants.premiumPlusWhiteCheckIcon),
//                               SizedBox(
//                                 height: 2,
//                               ),
//                               const CustomFeatureRow(
//                                   color: Colors.white,
//                                   name: "Match",
//                                   iconPath:
//                                       AppConstants.premiumPlusWhiteCheckIcon),
//                               SizedBox(
//                                 height: 2,
//                               ),
//                               const CustomFeatureRow(
//                                   color: Colors.white,
//                                   name: "Message",
//                                   iconPath:
//                                       AppConstants.premiumPlusWhiteCheckIcon),
//                               SizedBox(
//                                 height: 2,
//                               ),
//                               const CustomFeatureRow(
//                                   color: Colors.white,
//                                   name: "Like",
//                                   iconPath:
//                                       AppConstants.premiumPlusWhiteCheckIcon),
//                               SizedBox(
//                                 height: 2,
//                               ),
//                               const CustomFeatureRow(
//                                   color: Colors.white,
//                                   name: "Unlimited Scroll to New Profiles",
//                                   iconPath:
//                                       AppConstants.premiumPlusWhiteCheckIcon),
//                               SizedBox(
//                                 height: 2,
//                               ),
//                               const CustomFeatureRow(
//                                   color: Colors.white,
//                                   name: "Send 8 sec Introductory Video",
//                                   iconPath:
//                                       AppConstants.premiumPlusWhiteCheckIcon),
//                               SizedBox(
//                                 height: 2,
//                               ),
//                               const CustomFeatureRow(
//                                   color: Colors.white,
//                                   name: "Create a Couple Account",
//                                   iconPath:
//                                       AppConstants.premiumPlusWhiteCheckIcon),
//                               SizedBox(
//                                 height: 2,
//                               ),
//                               const CustomFeatureRow(
//                                   color: Colors.white,
//                                   name: "Get Featured",
//                                   iconPath:
//                                       AppConstants.premiumPlusWhiteCheckIcon),
//                             ],
//                           ),

//                           // Spacer(),
//                           SizedBox(
//                             height: 35,
//                           ),
//                           if (!widget.isPremiumPlusUser)
//                             SizedBox(
//                               height: 52,
//                               child: NewCustomAppButton(
//                                   onTap: () {
//                                     showModalBottomSheet(
//                                       shape: const RoundedRectangleBorder(
//                                         // <-- SEE HERE
//                                         borderRadius: BorderRadius.vertical(
//                                           top: Radius.circular(25.0),
//                                         ),
//                                       ),
//                                       context: context,
//                                       builder: (BuildContext context) =>
//                                           PremiumPlusBottomSheet(
//                                         isPremiumUser: widget.isPremiumUser,
//                                         isPremiumPlusUser:
//                                             widget.isPremiumPlusUser,
//                                         isFirstTime: widget.isFirstTime,
//                                         isPurchase:
//                                             (isPurchase, selectedOption) {
//                                           widget.isPurchase(
//                                               isPurchase, selectedOption);
//                                         },
//                                       ),
//                                     );
//                                   },
//                                   isGradient: false,
//                                   borderRadius: 12,
//                                   textColor: Colors.black,
//                                   buttonTxt: "Buy now"),
//                             ),
//                           // SizedBox(height: 20,)
//                         ],
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//             const SizedBox(
//               height: 24,
//             ),
//             // const SizedBox(
//             //   height: 100,
//             // )
//           ],
//         ),
//       ),
//       // bottomSheet: Padding(
//       //   padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 29),
//       //   child: SizedBox(
//       //       height: 52,
//       //       child: NewCustomAppButton(
//       //           onTap: () {
//       //             showModalBottomSheet(
//       //               backgroundColor: Colors.black,
//       //               shape: const RoundedRectangleBorder(
//       //                 // <-- SEE HERE
//       //                 borderRadius: BorderRadius.vertical(
//       //                   top: Radius.circular(25.0),
//       //                 ),
//       //               ),
//       //               context: context,
//       //               builder: (BuildContext context) => PremiumPlusBottomSheet(
//       //                 isFirstTime: widget.isFirstTime,
//       //                 isPurchase: (isPurchase, selectedOption) {
//       //                   widget.isPurchase(isPurchase, selectedOption);
//       //                 },
//       //               ),
//       //             );
//       //           },
//       //           isGradient: true,
//       //           borderRadius: 12,
//       //           textColor: Colors.white,
//       //           buttonTxt: "Buy now")),
//       // ),
//     );
//   }
// }

// class PremiumPlusBottomSheet extends ConsumerStatefulWidget {
//   final bool isFirstTime;
//   final Function(bool isPurchase, int selectedOption) isPurchase;
//   final bool isPremiumUser;
//   final bool isPremiumPlusUser;

//   const PremiumPlusBottomSheet({
//     super.key,
//     Key? key,
//     this.isFirstTime = false,
//     required this.isPurchase,
//     required this.isPremiumUser,
//     required this.isPremiumPlusUser,
//   });

//   @override
//   ConsumerState<PremiumPlusBottomSheet> createState() =>
//       _PremiumPlusBottomSheetState();
// }

// class _PremiumPlusBottomSheetState
//     extends ConsumerState<PremiumPlusBottomSheet> {
//   int selectedOption = 3;
//   var selectedData;
//   var planData;

//   @override
//   Widget build(BuildContext context) {
//     final offeringsRef = ref.watch(subscriptionOfferingsProvider);

//     return Container(
//       height: 600,
//       padding: const EdgeInsets.all(16.0),
//       decoration: const BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.vertical(
//           top: Radius.circular(16.0),
//         ),
//       ),
//       child: SingleChildScrollView(
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 const Text(
//                   'Choose your plan',
//                   style: TextStyle(
//                     fontSize: 18.0,
//                     fontWeight: FontWeight.w600,
//                   ),
//                 ),
//                 GestureDetector(
//                   onTap: () {
//                     Navigator.pop(context);
//                   },
//                   child: const Icon(
//                     Icons.close,
//                     size: 25,
//                   ),
//                 )
//               ],
//             ),
//             const SizedBox(height: 16.0),
//             offeringsRef.when(
//               data: (data) {
//                 selectedData = data[3];
//                 // oldPurchaseDetails = data[widget.planType];
//                 setState(() {});
//                 return SizedBox(
//                   child: ListView.builder(
//                     shrinkWrap: true,
//                     physics: const NeverScrollableScrollPhysics(),
//                     itemCount: 3,
//                     itemBuilder: (context, index) {
//                       final element = data[index + 3];
//                       return Column(
//                         children: [
//                           Container(
//                             decoration: BoxDecoration(
//                               gradient: selectedOption == (index + 3)
//                                   ? const LinearGradient(
//                                       begin: Alignment.topCenter,
//                                       end: Alignment.bottomCenter,
//                                       colors: [
//                                         Color(0xFFC70973),
//                                         Color(0xFF46239F)
//                                       ],
//                                     )
//                                   : null,
//                               border: selectedOption == (index + 3)
//                                   ? null
//                                   : Border.all(
//                                       color: const Color(0xFFA1A1A1),
//                                       width: selectedOption == (index + 3)
//                                           ? 2.0
//                                           : 1,
//                                     ),
//                               borderRadius: BorderRadius.circular(14),
//                             ),
//                             child: Padding(
//                               padding: const EdgeInsets.all(2.0),
//                               child: InkWell(
//                                 onTap: () {
//                                   setState(() {
//                                     selectedOption = (index + 3);
//                                     selectedData = data[selectedOption];
//                                     planData = data[selectedOption];
//                                   });
//                                 },
//                                 child: Container(
//                                   decoration: BoxDecoration(
//                                     color: Colors.white,
//                                     border: Border.all(color: Colors.white),
//                                     borderRadius: BorderRadius.circular(12),
//                                   ),
//                                   child: Column(
//                                     crossAxisAlignment:
//                                         CrossAxisAlignment.start,
//                                     children: [
//                                       Padding(
//                                         padding: const EdgeInsets.only(
//                                             left: 16,
//                                             right: 16,
//                                             top: 10,
//                                             bottom: 6),
//                                         child: Row(
//                                           mainAxisAlignment:
//                                               MainAxisAlignment.spaceBetween,
//                                           children: [
//                                             Text(
//                                               getPlanName(element.identifier),
//                                               style: const TextStyle(
//                                                 color: Color(0xff626262),
//                                                 fontWeight: FontWeight.w600,
//                                                 fontSize: 18,
//                                               ),
//                                             ),
//                                             SvgPicture.asset(
//                                               selectedOption == (index + 3)
//                                                   ? AppConstants.selectedRadio
//                                                   : AppConstants
//                                                       .unselectedRadio,
//                                               height: 24,
//                                               width: 24,
//                                             )
//                                           ],
//                                         ),
//                                       ),
//                                       SizedBox(height: 5),
//                                       Padding(
//                                         padding: EdgeInsets.symmetric(
//                                             horizontal: 16),
//                                         child: GradientText(
//                                           gradientDirection:
//                                               GradientDirection.ttb,
//                                           colors: const [
//                                             Color(0xFFC70973),
//                                             Color(0xFF46239F)
//                                           ],
//                                           element.storeProduct.priceString,
//                                           //  +
//                                           //     "/" +
//                                           //     getPlanType(
//                                           //         element.packageType.name),
//                                           style: TextStyle(
//                                             color: AppConstants.primaryColor,
//                                             fontWeight: FontWeight.w700,
//                                             fontSize: 18,
//                                           ),
//                                         ),
//                                       ),
//                                       SizedBox(height: 16),
//                                     ],
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ),
//                           if (index + 1 != data.length)
//                             SizedBox(
//                               height: 18,
//                             )
//                         ],
//                       );
//                     },
//                   ),
//                 );
//               },
//               error: (error, stackTrace) => CupertinoActionSheetAction(
//                 onPressed: () {},
//                 child: const Text("Error loading offers!"),
//               ),
//               loading: () => CircularProgressIndicator(),
//             ),
//             const SizedBox(
//               height: 4,
//             ),
//             NewCustomAppButton(
//                 onTap: () async {
//                   if (planData != null || selectedData != null) {
//                     Navigator.pop(context);
//                     CustomAppLoader.showCustomLoader("Processing...");
//                     // Offerings offerings = await Purchases.getOfferings();
//                     // debugPrint(offerings.toString());
//                     // CustomerInfo customerInfo = await Purchases.getCustomerInfo();
//                     // debugPrint(
//                     //     customerInfo.toString() + selectedOption.toString());
//                     final CustomerInfo customerInfo =
//                         await Purchases.getCustomerInfo();

//                     if (Platform.isAndroid &&
//                         customerInfo.activeSubscriptions.isNotEmpty &&
//                         (widget.isPremiumUser)) {
//                       print("planData: $planData");
//                       print("selectedData: $selectedData");
//                       print(
//                           "activeSubscriptions: ${customerInfo.activeSubscriptions}");
//                       print(
//                           "Selected Subscription: ${customerInfo.activeSubscriptions.reversed.first}");

//                       await Purchases.purchasePackage(planData ?? selectedData,
//                           upgradeInfo: UpgradeInfo(
//                             customerInfo.activeSubscriptions.reversed.first,
//                             prorationMode:
//                                 ProrationMode.immediateAndChargeFullPrice,
//                           )).then((value) {
//                         widget.isPurchase(true, selectedOption);
//                       }).onError((error, stackTrace) {
//                         EasyLoading.dismiss();
//                         EasyLoading.showInfo("Purchase Failed!");
//                         // widget.isPurchase(false);
//                       });
//                     } else {
//                       await Purchases.purchasePackage(
//                         planData ?? selectedData,
//                       ).then((value) {
//                         widget.isPurchase(true, selectedOption);
//                       }).onError((error, stackTrace) {
//                         EasyLoading.dismiss();
//                         EasyLoading.showInfo("Purchase Failed!");
//                         // widget.isPurchase(false);
//                       });
//                     }
//                   } else {
//                     Navigator.of(context).pop();
//                   }
//                 },
//                 isGradient: true,
//                 textColor: Colors.white,
//                 borderRadius: 12,
//                 buttonTxt: "Continue")
//           ],
//         ),
//       ),
//     );
//   }

//   String getPlanName(String name) {
//     print("this is our package name $name");
//     if (name.toLowerCase().contains("monthly")) {
//       return "Monthly";
//     } else if (name.toLowerCase().contains("yearly")) {
//       return "Annually";
//     } else if (name.toLowerCase().contains("3month")) {
//       return "3 Months";
//     } else {
//       return name;
//     }
//   }

//   String getPlanType(String name) {
//     if (name.contains("month")) {
//       return "month";
//     } else if (name.contains("annual")) {
//       return "year";
//     } else {
//       return name;
//     }
//   }
// }

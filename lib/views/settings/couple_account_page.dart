import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/views/custom/custom_dropdown_new.dart';
import 'package:fringle_app/views/custom/custom_two_button_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../helpers/validators.dart';
import '../../models/user_profile_model.dart';
import '../../providers/auth_providers.dart';
import '../../providers/user_profile_provider.dart';
import '../custom/custom_app_loader.dart';
import '../custom/custom_button.dart';
import '../custom/custom_new_app_bar.dart';
import '../custom/custom_textfield.dart';

class CoupleAccountPage extends ConsumerStatefulWidget {
  const CoupleAccountPage({super.key});

  @override
  ConsumerState<CoupleAccountPage> createState() => _CoupleAccountPageState();
}

class _CoupleAccountPageState extends ConsumerState<CoupleAccountPage> {
  TextEditingController nameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  String? selectedIamFirst;
  String? selectedSeekingFirst;
  String? selectedIamSecond;
  String? selectedSeekingSecond;
  bool isFirst = true;

  @override
  void initState() {
    super.initState();
  }

  Future<void> createCoupleAccount(WidgetRef ref, String userPassword) async {
    CustomAppLoader.showCustomLoader("Loading...");

    User? user = FirebaseAuth.instance.currentUser;
    var userEmail = user?.email ?? "";
    await ref
        .read(authProvider)
        .signInWithEmailAndPassword(userEmail, userPassword)
        .then((value) async {
      if (value != null) {
        await ref
            .read(authProvider)
            .createNewAccount(
                emailController.text, user?.uid ?? "Tasjdasn@j34r4nn3434")
            .then((value) async {
          if (value.isNotEmpty) {
            await FirebaseAuth.instance
                .sendPasswordResetEmail(email: emailController.text.trim());

            final userCollection = FirebaseFirestore.instance
                .collection(FirebaseConstants.coupleAccount);
            await userCollection.doc(user?.uid).set({
              "coupleId": user?.uid,
              "userId": value,
              "email": emailController.text,
              "couplename": nameController.text,
              "isActive": true,
            }).then((value) async {
              await FirebaseFirestore.instance
                  .collection(FirebaseConstants.userProfileCollection)
                  .where("userId", isEqualTo: user?.uid)
                  .get()
                  .then((value) async {
                if (value.docs.isNotEmpty) {
                  Map<String, dynamic>? selectedPreferences = {};
                  selectedPreferences["couplePrimery"] = selectedIamFirst;
                  selectedPreferences["coupleSecondary"] = selectedIamSecond;
                  selectedPreferences["seekingHer"] = selectedSeekingFirst;
                  selectedPreferences["seekingHim"] = selectedSeekingSecond;
                  await ref
                      .read(userProfileNotifier)
                      .updateUserProfile(
                          UserProfileModel.fromMap(value.docs.first.data())
                              .copyWith(
                                  preferences: {},
                                  couplePrefrenceSecond: selectedPreferences,
                                  couplePrefrencePrimary: selectedPreferences,
                                  isCoupleAccount: true))
                      .then((value) {
                    if (value) {
                      // initPlatformStateForPurchases(user?.uid);
                      EasyLoading.showToast(
                          "Converted to couple account successfully.");
                      Navigator.pop(context);
                      ref.invalidate(userProfileFutureProvider);
                    } else {
                      EasyLoading.showToast("Something want wrong.");
                    }
                  });
                }
              });
            });
          } else {
            EasyLoading.showToast("User already exits.");
          }
        });
      } else {
        EasyLoading.showToast("Incorrect password.");
        return;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (isFirst) {
          return true;
        } else {
          setState(() {
            isFirst = true;
          });
          return false;
        }
      },
      child: Scaffold(
          backgroundColor: const Color(0xFFFFFCF5),
          appBar: GradientAppBar(
            title: "Create Couple account".toUpperCase(),
          ),
          body: Column(
            children: [
              isFirst
                  ? CoupleUserDetails(
                      formKey: _formKey,
                      emailController: emailController,
                      nameController: nameController,
                    )
                  : CouplePrefrencesWidget(
                      listWhoIs: listWhoIs,
                      selectedIamFirst: selectedIamFirst,
                      selectedSeekingFirst: selectedSeekingFirst,
                      selectedIamSecond: selectedIamSecond,
                      selectedSeekingSecond: selectedSeekingSecond,
                      onChangedIamFirst: (newValue) {
                        setState(() {
                          selectedIamFirst = newValue;
                        });
                      },
                      onChangedSeekingFirst: (newValue) {
                        setState(() {
                          selectedSeekingFirst = newValue;
                        });
                      },
                      onChangedIamSecond: (newValue) {
                        setState(() {
                          selectedIamSecond = newValue;
                        });
                      },
                      onChangedSeekingSecond: (newValue) {
                        setState(() {
                          selectedSeekingSecond = newValue;
                        });
                      },
                    ),
              Padding(
                padding: const EdgeInsets.all(24),
                child: SizedBox(
                  height: 52,
                  child: CustomButton(
                    onPressed: () {
                      if (isFirst) {
                        if (_formKey.currentState!.validate()) {
                          setState(() {
                            isFirst = false;
                          });
                        }
                      } else {
                        if ((selectedIamFirst == null) ||
                            (selectedIamSecond == null) ||
                            (selectedSeekingFirst == null) ||
                            (selectedSeekingSecond == null) ||
                            (selectedIamFirst?.isEmpty == true) ||
                            (selectedIamSecond?.isEmpty == true) ||
                            (selectedSeekingFirst?.isEmpty == true) ||
                            (selectedSeekingSecond?.isEmpty == true)) {
                          EasyLoading.showToast(
                              "Please fill in all the required fields.");
                          return;
                        }
                        showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return AccountAuthDialog(
                                  onAuthenticate: (password) {
                                    createCoupleAccount(ref, password);
                                  },
                                  title:
                                      "Please enter your password to proceed.");
                            });
                      }
                    },
                    text: isFirst ? "Create" : "Apply",
                  ),
                ),
              ),
            ],
          )),
    );
  }
}

class CoupleUserDetails extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final TextEditingController emailController;
  final TextEditingController nameController;

  const CoupleUserDetails({
    super.key,
    required this.formKey,
    required this.emailController,
    required this.nameController,
  });

  @override
  _CoupleUserDetailsState createState() => _CoupleUserDetailsState();
}

class _CoupleUserDetailsState extends State<CoupleUserDetails> {
  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Form(
        key: widget.formKey,
        child: Container(
          padding: const EdgeInsets.fromLTRB(20, 34, 20, 20),
          width: double.infinity,
          child: Column(children: [
            CustomTextField2(
                controller: widget.emailController,
                labelText: "Email *",
                hintText: "Email",
                validator: Validators.validateCoupleEmail,
                inputFormatters: [
                  FilteringTextInputFormatter.deny(RegExp(r'\s')),
                ]),
            const SizedBox(
              height: 20,
            ),
            CustomTextField2(
              controller: widget.nameController,
              labelText: "Username *",
              hintText: "Username",
              validator: Validators.validateCoupleName,
            ),
          ]),
        ),
      ),
    );
  }
}

class CouplePrefrencesWidget extends StatefulWidget {
  final List<String> listWhoIs;
  final Function(String?) onChangedIamFirst;
  final Function(String?) onChangedSeekingFirst;
  final Function(String?) onChangedIamSecond;
  final Function(String?) onChangedSeekingSecond;
  final String? selectedIamFirst;
  final String? selectedSeekingFirst;
  final String? selectedIamSecond;
  final String? selectedSeekingSecond;

  const CouplePrefrencesWidget({
    super.key,
    required this.listWhoIs,
    required this.onChangedIamFirst,
    required this.onChangedSeekingFirst,
    required this.onChangedIamSecond,
    required this.onChangedSeekingSecond,
    required this.selectedIamFirst,
    required this.selectedSeekingFirst,
    required this.selectedIamSecond,
    required this.selectedSeekingSecond,
  });

  @override
  _CouplePrefrencesWidgetState createState() => _CouplePrefrencesWidgetState();
}

class _CouplePrefrencesWidgetState extends State<CouplePrefrencesWidget> {
  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.fromLTRB(20, 20, 20, 20),
          width: double.infinity,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                "Couple",
                style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black),
              ),
              const SizedBox(
                height: 15,
              ),
              CustomDropdownNew(
                items: widget.listWhoIs,
                selectedValue: widget.selectedIamFirst,
                onChanged: widget.onChangedIamFirst,
                labelText: 'Primary *',
                hintText: 'Select',
              ),
              const SizedBox(
                height: 20,
              ),
              CustomDropdownNew(
                items: widget.listWhoIs,
                selectedValue: widget.selectedIamSecond,
                onChanged: widget.onChangedIamSecond,
                labelText: 'Secondary *',
                hintText: 'Select',
              ),
              const SizedBox(
                height: 40,
              ),
              const Text(
                "Seeking",
                style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black),
              ),
              const SizedBox(
                height: 24,
              ),
              CustomDropdownNew(
                items: List.from(widget.listWhoIs)
                  ..add('Any')
                  ..add('None'),
                selectedValue: widget.selectedSeekingFirst,
                onChanged: widget.onChangedSeekingFirst,
                labelText: 'Her *',
                hintText: 'Select',
              ),
              const SizedBox(
                height: 20,
              ),
              CustomDropdownNew(
                items: List.from(widget.listWhoIs)
                  ..add('Any')
                  ..add('None'),
                selectedValue: widget.selectedSeekingSecond,
                onChanged: widget.onChangedSeekingSecond,
                labelText: 'Him *',
                hintText: 'Select',
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'dart:io';
import 'package:custom_pop_up_menu/custom_pop_up_menu.dart';
import 'package:emoji_picker_flutter/emoji_picker_flutter.dart';
import 'package:fringle_app/views/custom/custom_confirmation_dialog.dart';
import 'package:fringle_app/views/others/chat_video_player.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fringle_app/config/config.dart';
import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/helpers/date_formater.dart';
import 'package:fringle_app/helpers/encrypt_helper.dart';
import 'package:fringle_app/helpers/media_picker_helper.dart';
import 'package:fringle_app/models/chat_item_model.dart';
import 'package:fringle_app/models/user_profile_model.dart';
import 'package:fringle_app/providers/auth_providers.dart';
import 'package:fringle_app/providers/chat_provider.dart';
import 'package:fringle_app/providers/other_users_provider.dart';
import 'package:fringle_app/views/others/user_card_widget.dart';
import 'package:fringle_app/views/others/user_details_page.dart';
import 'package:fringle_app/views/others/video_player_page.dart';
import 'package:fringle_app/views/tabs/home/<USER>';
import 'package:fringle_app/views/tabs/messages/components/chat_page_background.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:nsfw_detector_flutter/nsfw_detector_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:social_media_recorder/audio_encoder_type.dart';
import 'package:social_media_recorder/screen/social_media_recorder.dart';
import 'package:video_player/video_player.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

import '../../../../providers/notifiaction_provider.dart';
import '../../../../providers/user_profile_provider.dart';
import '../../../custom/custom_app_loader.dart';

class ChatPage extends ConsumerStatefulWidget {
  final String otherUserId;
  final String matchId;
  String? introVideo;
  ChatPage({
    super.key,
    required this.otherUserId,
    required this.matchId,
    this.introVideo,
  });

  @override
  ConsumerState<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends ConsumerState<ChatPage> {
  final _chatController = TextEditingController();
  UserProfileModel userProfileDataModel = UserProfileModel.defaultConstructor();
  bool emojiShowing = false;
  String? _imagePath;
  String? _videoPath;
  String? _thumbnail;
  String? _audioPath;
  String? _filePath;
  String? _searchQuery;
  String? _videoDuration;
  int messageCount = 1;

  void _onSendMessage() async {
    final chatData = ref.read(chatProvider);

    if (_chatController.text.isNotEmpty ||
        _imagePath != null ||
        _videoPath != null ||
        _audioPath != null ||
        _filePath != null) {
      final currentTime = DateTime.now();

      String? imageUrl;
      String? videoUrl;
      String? thumbnail;
      String? audioUrl;
      String? fileUrl;

      if (_imagePath != null && _imagePath?.isNotEmpty == true) {
        CustomAppLoader.showCustomLoader("Uploading image...");
        imageUrl = await chatData.uploadFile(
            file: File(_imagePath ?? ""), matchId: widget.matchId);
        EasyLoading.dismiss();
      }

      if (_videoPath != null && _videoPath?.isNotEmpty == true) {
        CustomAppLoader.showCustomLoader("Uploading video...");
        videoUrl = await chatData.uploadFile(
            file: File(_videoPath ?? ""), matchId: widget.matchId);
        thumbnail = await chatData.uploadFile(
            file: File(_thumbnail ?? ""), matchId: widget.matchId);
        EasyLoading.dismiss();
      }

      if (_audioPath != null && _audioPath?.isNotEmpty == true) {
        CustomAppLoader.showCustomLoader("Uploading audio...");
        audioUrl = await chatData.uploadFile(
            file: File(_audioPath ?? ""), matchId: widget.matchId);
        EasyLoading.dismiss();
      }

      if (_filePath != null && _filePath?.isNotEmpty == true) {
        CustomAppLoader.showCustomLoader("Uploading file...");
        fileUrl = await chatData.uploadFile(
            file: File(_filePath ?? ""), matchId: widget.matchId);
        EasyLoading.dismiss();
      }

      final String? message = _chatController.text.isEmpty
          ? null
          : encryptText(_chatController.text);

      ChatItemModel chatItem = ChatItemModel(
        message: message,
        createdAt: currentTime,
        id: currentTime.millisecondsSinceEpoch.toString(),
        userId: ref.watch(currentUserStateProvider)?.uid,
        matchId: widget.matchId,
        isRead: false,
        image: imageUrl,
        video: videoUrl,
        thumbnail: thumbnail,
        videoDuration: _videoDuration,
        audio: audioUrl,
        file: fileUrl,
      );

      chatData.createChatItem(widget.matchId, chatItem);
      var title = "${userProfileDataModel.firstName}";
      var body = _chatController.text.isEmpty
          ? "Send new file"
          : _chatController.text.toString();
      sendPushNotification(
          title,
          body,
          widget.otherUserId,
          "message",
          userProfileDataModel.userId,
          widget.matchId);
      _chatController.clear();
      setState(() {
        _imagePath = null;
        _videoPath = null;
        _thumbnail = null;
        _audioPath = null;
        _filePath = null;
      });
    }
  }

  _onEmojiSelected(Emoji emoji) {
    setState(() {
      _chatController
        ..text += emoji.emoji
        ..selection = TextSelection.fromPosition(
            TextPosition(offset: _chatController.text.length));
    });
  }

  _onBackspacePressed() {
    setState(() {
      _chatController
        ..text = _chatController.text.characters.skipLast(1).toString()
        ..selection = TextSelection.fromPosition(
            TextPosition(offset: _chatController.text.length));
    });
  }

  sendIntroVideo() async {
    if (widget.introVideo == null) {
      return;
    }
    String inVideo = widget.introVideo ?? "";
    String thumbnailPath = "";
    widget.introVideo = null;
    setState(() {});
    var today = DateFormat("MM/dd/yyyy").format(DateTime.now());
    var introDate = "";
    var introCount = 0;
    UserProfileModel? currentUserData;
    final userRef = ref.read(userProfileFutureProvider);
    userRef.when(
        data: (value) => {
              if (value != null)
                {
                  currentUserData = value,
                  introDate = value.introVideoDate ?? "",
                  introCount = value.introVideoCount ?? 0
                }
            },
        error: (_, __) => const SizedBox(),
        loading: () => const SizedBox());
    introCount++;
    if (introDate == today && introCount > 10) {
      EasyLoading.showError(
          "Sorry, you've exceeded the daily limit of 10 introductory videos. Please try again tomorrow.");
    } else {
      // ignore: unnecessary_null_comparison
      if (inVideo != null) {
        CustomAppLoader.showCustomLoader(
            "Reviewing for explicit content, Please wait.");

        var isNudity = false;
        final videoController = VideoPlayerController.file(File(inVideo));
        await videoController.initialize();
        final duration = videoController.value.duration;
        videoController.dispose();
        _videoDuration = duration.toString();
        for (int i = 1; i <= duration.inSeconds; i++) {
          final thumbnail = await VideoThumbnail.thumbnailData(
            video: inVideo,
            imageFormat: ImageFormat.JPEG,
            quality: 80,
            timeMs: (i *
                1000), // Generate thumbnails at different times (every second in this example).
          );
          Directory tempDir = await getTemporaryDirectory();

          // Generate a unique filename
          String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
          String filePath = '${tempDir.path}/image_$timestamp.png';

          if (thumbnailPath.isEmpty) {
            thumbnailPath = filePath;
          }
          // Create a file and write the Uint8List data to it
          File imageFile = File(filePath);
          await imageFile.writeAsBytes(thumbnail!);
          NsfwDetector detector = await NsfwDetector.load();
          NsfwResult? result = await detector.detectNSFWFromFile(imageFile);
          final hasNudity = result?.isNsfw ?? false;
          if (hasNudity) {
            isNudity = true;
            break;
          } else {
            isNudity = false;
          }
        }
        EasyLoading.dismiss();

        CustomAppLoader.showCustomLoader("Uploading video...");
        final chatData = ref.read(chatProvider);
        String? videoUrl = await chatData.uploadFile(
            file: File(inVideo), matchId: widget.matchId);
        String? thumbnail = await chatData.uploadFile(
            file: File(thumbnailPath), matchId: widget.matchId);
        final String? message = _chatController.text.isEmpty
            ? null
            : encryptText(_chatController.text);

        final currentTime = DateTime.now();

        ChatItemModel chatItem = ChatItemModel(
            message: message,
            createdAt: currentTime,
            id: currentTime.millisecondsSinceEpoch.toString(),
            userId: ref.watch(currentUserStateProvider)?.uid,
            matchId: widget.matchId,
            isRead: false,
            video: videoUrl,
            thumbnail: thumbnail,
            videoDuration: _videoDuration,
            isIntroVideo: true);

        chatData.createChatItem(widget.matchId, chatItem);
        if (currentUserData != null) {
          ref.read(userProfileNotifier).updateUserProfile(currentUserData!
              .copyWith(introVideoDate: today, introVideoCount: introCount));
          ref.invalidate(userProfileFutureProvider);
        }
        EasyLoading.dismiss();
      } else {
        EasyLoading.showError(
            "Our platform does not allow the upload of explicit content. Let's keep it classy and respectful for everyone.");
      }
    }
  }

  bool isTimeInRange(DateTime startTime, DateTime endTime) {
    // Get the current time
    DateTime currentTime = DateTime.now();
    if (currentTime.isAfter(startTime) && currentTime.isBefore(endTime)) {
      return true;
    } else {
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    sendIntroVideo();

    final otherUsers = ref.watch(otherUsersProvider);

    ref.watch(userProfileFutureProvider).whenData((value) {
      if (value == null) {
        return;
      }
      userProfileDataModel = value;
      if (value.isDMOn ?? false) {
        var today = DateFormat("MM/dd/yyyy").format(DateTime.now());
        var dmDate =
            DateFormat("MM/dd/yyyy").format(value.dmDate ?? DateTime.now());
        DateTime currentTime = DateTime.now();

        if (today != dmDate ||
            currentTime.isAfter(value.dmEndTime ?? DateTime.now())) {
          ref.read(userProfileNotifier).updateUserProfile(value.copyWith(
              userId: value.id,
              isDMOn: false,
              dmDate: null,
              dmEndTime: null,
              dmStartTime: null));
          ref.invalidate(otherUsersProvider);
          ref.invalidate(userProfileFutureProvider);
          setState(() {
            userProfileDataModel.isDMOn = false;
          });
        }
      }
    });

    final chatStreams = ref.watch(chatStreamProviderProvider(widget.matchId));
    chatStreams.when(
      data: (data) {
        var myTextMessageCount = 1;
        for (var element in data) {
          if (element.userId == ref.watch(currentUserStateProvider)?.uid) {
            myTextMessageCount++;
          }
        }
        messageCount = myTextMessageCount;
        setState(() {});
      },
      error: (error, stackTrace) {},
      loading: () {},
    );
    UserProfileModel? otherUser;
    otherUsers.whenData((value) {
      otherUser = value
          .where((element) {
            return element.userId == widget.otherUserId;
          })
          .toList()
          .first;
      if (otherUser == null) return;
      if (otherUser?.isDMOn ?? false) {
        var today = DateFormat("MM/dd/yyyy").format(DateTime.now());
        var dmDate = DateFormat("MM/dd/yyyy")
            .format(otherUser?.dmDate ?? DateTime.now());
        DateTime currentTime = DateTime.now();
        if (today != dmDate ||
            currentTime.isAfter(otherUser?.dmEndTime ?? DateTime.now())) {
          ref.read(userProfileNotifier).updateUserProfile(otherUser!.copyWith(
              userId: otherUser?.id,
              isDMOn: false,
              dmDate: null,
              dmEndTime: null,
              dmStartTime: null));

          ref.invalidate(otherUsersProvider);
          ref.invalidate(userProfileFutureProvider);
          setState(() {
            otherUser?.isDMOn = false;
          });
        }
      }
    });

    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
        setState(() {
          emojiShowing = false;
        });
      },
      child: ChatPageBackground(
        child: Scaffold(
          backgroundColor: Colors.transparent,
          appBar: AppBar(
            toolbarHeight: 0,
            backgroundColor: const Color(0xFFFFFCF5),
            elevation: 0,
            systemOverlayStyle: SystemUiOverlayStyle.dark,
          ),
          body: SafeArea(
            top: false,
            child: (otherUser == null)
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(
                          height: 10,
                        ),
                        Text(
                          "Please wait...",
                          style: TextStyle(color: Colors.black),
                        )
                      ],
                    ),
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      if (otherUser != null)
                        ChatTopBar(
                          otherUser: otherUser!,
                          myUserId:
                              ref.watch(currentUserStateProvider)?.uid ?? "",
                          matchId: widget.matchId,
                          onSearch: (query) {
                            setState(() {
                              _searchQuery = query;
                            });
                          },
                        ),
                      if (otherUser != null)
                        Expanded(
                          child: ChatBody(
                            matchId: widget.matchId,
                            searchQuery: _searchQuery,
                            otherUser: otherUser!,
                            onSearchClear: () {
                              setState(() {
                                _searchQuery = null;
                              });
                            },
                          ),
                        ),
                      const SizedBox(
                          height: AppConstants.defaultNumericValue / 2),
                      if (messageCount == 1)
                        if (!(((userProfileDataModel.isDMOn ?? false) == true &&
                                isTimeInRange(
                                        userProfileDataModel.dmStartTime ??
                                            DateTime.now(),
                                        userProfileDataModel.dmEndTime ??
                                            DateTime.now()) ==
                                    true) ||
                            ((otherUser?.isDMOn ?? false) == true &&
                                isTimeInRange(
                                        otherUser?.dmStartTime ??
                                            DateTime.now(),
                                        otherUser?.dmEndTime ??
                                            DateTime.now()) ==
                                    true)))
                          InkWell(
                            onTap: () {
                              var today = DateFormat("MM/dd/yyyy")
                                  .format(DateTime.now());
                              var introDate = "";
                              var introCount = 0;
                              final userRef =
                                  ref.read(userProfileFutureProvider);
                              userRef.when(
                                  data: (value) => {
                                        if (value != null)
                                          {
                                            introDate =
                                                value.introVideoDate ?? "",
                                            introCount =
                                                value.introVideoCount ?? 0
                                          }
                                      },
                                  error: (_, __) => const SizedBox(),
                                  loading: () => const SizedBox());
                              introCount++;
                              if ((userProfileDataModel.isPremiumUser ??
                                          false) ==
                                      true &&
                                  (otherUser?.isPremiumUser ?? false) ==
                                      false) {
                                showDialog(
                                    context: context,
                                    builder: (BuildContext mContext) {
                                      return CustomTwoButtonDialog(
                                        title: "Premium User Alert",
                                        desciption:
                                            "Non- premium recipients cannot play intro videos",
                                        buttonText: "Send it anyway",
                                        cancelButtonText: "Ok",
                                        onButtonClick: () {
                                          if (introDate == today &&
                                              introCount > 10) {
                                            EasyLoading.showError(
                                                "Sorry, you've exceeded the daily limit of 10 introductory videos. Please try again tomorrow.");
                                          } else {
                                            pickMedia(
                                                    isVideo: true,
                                                    isCamera: true)
                                                .then((value) {
                                              widget.introVideo = value;
                                              setState(() {});
                                              sendIntroVideo();
                                            });
                                          }
                                        },
                                      );
                                    });
                              } else {
                                if (introDate == today && introCount > 10) {
                                  EasyLoading.showError(
                                      "Sorry, you've exceeded the daily limit of 10 introductory videos. Please try again tomorrow.");
                                } else {
                                  pickMedia(isVideo: true, isCamera: true)
                                      .then((value) {
                                    widget.introVideo = value;
                                    setState(() {});
                                    sendIntroVideo();
                                  });
                                }
                              }
                            },
                            child: SizedBox(
                              width: double.infinity,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  const Text(
                                    "Click here to send introductory video.",
                                    style: TextStyle(
                                      fontSize: 15,
                                      color: Colors.black,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 10,
                                  ),
                                  SvgPicture.asset(AppConstants.introVideo),
                                ],
                              ),
                            ),
                          ),
                      if (((userProfileDataModel.isDMOn ?? false) == true &&
                              isTimeInRange(
                                      userProfileDataModel.dmStartTime ??
                                          DateTime.now(),
                                      userProfileDataModel.dmEndTime ??
                                          DateTime.now()) ==
                                  true) ||
                          ((otherUser?.isDMOn ?? false) == true &&
                              isTimeInRange(
                                      otherUser?.dmStartTime ?? DateTime.now(),
                                      otherUser?.dmEndTime ?? DateTime.now()) ==
                                  true))
                        SizedBox(
                          width: double.infinity,
                          child: Center(
                            child: Text(
                              (userProfileDataModel.isDMOn ?? false)
                                  ? "Please update the Turn off DM settings to enable chat"
                                  : " ${otherUser?.firstName} has DM's turned off at the moment",
                              style: const TextStyle(
                                fontSize: 13,
                                color: Colors.black,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
                        ),
                      if (!(((userProfileDataModel.isDMOn ?? false) == true &&
                              isTimeInRange(
                                      userProfileDataModel.dmStartTime ??
                                          DateTime.now(),
                                      userProfileDataModel.dmEndTime ??
                                          DateTime.now()) ==
                                  true) ||
                          ((otherUser?.isDMOn ?? false) == true &&
                              isTimeInRange(
                                      otherUser?.dmStartTime ?? DateTime.now(),
                                      otherUser?.dmEndTime ?? DateTime.now()) ==
                                  true)))
                        ChatTextFieldAndOthers(
                          chatController: _chatController,
                          onChangeText: () {
                            setState(() {});
                          },
                          onTapEmoji: () {
                            setState(() {
                              emojiShowing = !emojiShowing;
                            });
                            FocusScope.of(context).requestFocus(FocusNode());
                          },
                          onTapVoice: () {
                            showModalBottomSheet(
                              context: context,
                              isDismissible: false,
                              enableDrag: false,
                              backgroundColor: Colors.transparent,
                              builder: (_) =>
                                  VoiceRecorder(matchId: widget.matchId),
                            );
                          },
                          onTapTextField: () {
                            setState(() {
                              emojiShowing = false;
                            });
                          },
                          imageUrl: _imagePath,
                          videoUrl: _videoPath,
                          audioUrl: _audioPath,
                          fileUrl: _filePath,
                          onImageSelected: (String? path) async {
                            if (path == null) {
                              setState(() {
                                _imagePath = path;
                              });
                              return;
                            }
                            NsfwDetector detector = await NsfwDetector.load();
                            NsfwResult? result = await detector.detectNSFWFromFile(File(path));
                            final hasNudity = result?.isNsfw ?? false;
                            if (hasNudity) {
                              EasyLoading.showError(
                                  "Our platform does not allow the upload of explicit content. Let's keep it classy and respectful for everyone.");
                            } else {
                              setState(() {
                                _imagePath = path;
                              });
                            }
                          },
                          onVideoSelected: (String? path) async {
                            var isNudity = false;
                            if (path == null) {
                              setState(() {
                                _videoPath = path;
                                _thumbnail = path;
                              });
                              return;
                            }
                            CustomAppLoader.showCustomLoader(
                                "Reviewing for explicit content, Please wait.");
                            final videoController =
                                VideoPlayerController.file(File(path));
                            await videoController.initialize();
                            final duration = videoController.value.duration;
                            videoController.dispose();
                            _videoDuration = duration.toString();
                            for (int i = 1; i <= duration.inSeconds; i++) {
                              final thumbnail =
                                  await VideoThumbnail.thumbnailData(
                                video: path,
                                imageFormat: ImageFormat.JPEG,
                                quality: 80,
                                timeMs: (i *
                                    1000), // Generate thumbnails at different times (every second in this example).
                              );
                              Directory tempDir = await getTemporaryDirectory();

                              // Generate a unique filename
                              String timestamp = DateTime.now()
                                  .millisecondsSinceEpoch
                                  .toString();
                              String filePath =
                                  '${tempDir.path}/image_$timestamp.png';

                              // Create a file and write the Uint8List data to it
                              if (_thumbnail == null ||
                                  _thumbnail?.isEmpty == true) {
                                setState(() {
                                  _thumbnail = filePath;
                                });
                              }
                              File imageFile = File(filePath);

                              await imageFile.writeAsBytes(thumbnail!);
                              NsfwDetector detector = await NsfwDetector.load();
                              NsfwResult? result = await detector.detectNSFWFromFile(imageFile);
                              final hasNudity = result?.isNsfw ?? false;

                              if (hasNudity) {
                                isNudity = true;
                                break;
                              } else {
                                isNudity = false;
                              }
                            }
                            EasyLoading.dismiss();
                            if (!isNudity) {
                              setState(() {
                                _videoPath = path;
                              });
                            } else {
                              EasyLoading.showError(
                                  "Our platform does not allow the upload of explicit content. Let's keep it classy and respectful for everyone.");
                            }
                          },
                          onAudioSelected: (String? path) {
                            setState(() {
                              _audioPath = path;
                            });
                          },
                          onFileSelected: (String? path) {
                            setState(() {
                              _filePath = path;
                            });
                          },
                          onTapSend: _onSendMessage,
                        ),
                      const SizedBox(
                          height: AppConstants.defaultNumericValue / 2),
                      Offstage(
                        offstage: !emojiShowing,
                        child: SizedBox(
                          height: 250,
                          child: EmojiPicker(
                            onEmojiSelected: (Category? category, Emoji emoji) {
                              _onEmojiSelected(emoji);
                            },
                            onBackspacePressed: _onBackspacePressed,
                            // config: _emojiPickerConfig,
                          ),
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }
}

class ChatBody extends ConsumerStatefulWidget {
  final String matchId;
  final String? searchQuery;
  final VoidCallback onSearchClear;
  final UserProfileModel otherUser;
  const ChatBody(
      {super.key,
      required this.matchId,
      required this.searchQuery,
      required this.onSearchClear,
      required this.otherUser});

  @override
  ConsumerState<ChatBody> createState() => _ChatBodyState();
}

class _ChatBodyState extends ConsumerState<ChatBody> {
  final _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    final chatStreams = ref.watch(chatStreamProviderProvider(widget.matchId));
    //String lastDate = "";

    return chatStreams.when(
        data: (data) {
          String lastDate = "";
          if (data.isNotEmpty) {
            lastDate = DateFormatter.toYearMonth(data[0].createdAt);
          }

          return Column(
            children: [
              if (widget.searchQuery != null && widget.searchQuery!.isNotEmpty)
                ListTile(
                  title: Text(
                    "Searching for",
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  leading: const Icon(Icons.search),
                  minLeadingWidth: 0,
                  subtitle: Text(
                    widget.searchQuery!,
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: widget.onSearchClear,
                      ),
                      IconButton(
                          onPressed: () {
                            //Move to a specific chat item
                            _scrollController.animateTo(
                              _scrollController.position.minScrollExtent,
                              duration: const Duration(milliseconds: 1000),
                              curve: Curves.easeInOut,
                            );
                          },
                          icon: const Icon(Icons.arrow_downward)),
                      IconButton(
                          onPressed: () {
                            _scrollController.animateTo(
                              _scrollController.position.maxScrollExtent,
                              duration: const Duration(milliseconds: 1000),
                              curve: Curves.easeInOut,
                            );
                          },
                          icon: const Icon(Icons.arrow_upward)),
                    ],
                  ),
                ),
              if (widget.searchQuery != null && widget.searchQuery!.isNotEmpty)
                const Divider(height: 0),
              Expanded(
                child: ListView.builder(
                  controller: _scrollController,
                  reverse: true,
                  itemCount: data.length,
                  itemBuilder: (context, index) {
                    final item = data[index];
                    final bool isSearching = widget.searchQuery != null &&
                        widget.searchQuery!.isNotEmpty &&
                        item.message != null &&
                        decryptText(item.message!)
                            .toLowerCase()
                            .contains(widget.searchQuery!.toLowerCase());
                    if (!(item.isVisible ?? false)) {
                      if (index < (data.length - 2)) {
                        if (lastDate !=
                            DateFormatter.toYearMonth(
                                data[index + 1].createdAt)) {
                          lastDate = DateFormatter.toYearMonth(
                              data[index + 1].createdAt);
                          item.isDateVisible = true;
                        } else {
                          item.isDateVisible = false;
                        }
                      } else {
                        item.isDateVisible = true;
                      }
                      item.isVisible = true;
                    }

                    return MessageSingleTile(
                      key: ValueKey(item.id),
                      chat: item,
                      matchId: widget.matchId,
                      isSearching: isSearching,
                      otherUser: widget.otherUser,
                    );
                  },
                ),
              ),
            ],
          );
        },
        error: (_, __) => const SizedBox(),
        loading: () => const SizedBox());
  }
}

class ChatTopBar extends ConsumerStatefulWidget {
  final UserProfileModel otherUser;
  final String myUserId;
  final Function(String?) onSearch;

  final String matchId;
  const ChatTopBar({
    super.key,
    required this.otherUser,
    required this.myUserId,
    required this.onSearch,
    required this.matchId,
  });

  @override
  ConsumerState<ChatTopBar> createState() => _ChatTopBarState();
}

class _ChatTopBarState extends ConsumerState<ChatTopBar> {
  final CustomPopupMenuController _moreMenuController =
      CustomPopupMenuController();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFC70973),
            Color(0xFF46239F),
          ],
        ),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(AppConstants.defaultNumericValue),
          bottomRight: Radius.circular(AppConstants.defaultNumericValue),
        ),
        // gradient: AppConstants.defaultGradient
      ),
      child: ListTile(
        onTap: () {
          Navigator.of(context).push(
            CupertinoPageRoute(
              builder: (context) => UserDetailsPage(
                user: widget.otherUser,
                matchId: widget.matchId,
              ),
            ),
          );
        },
        contentPadding: const EdgeInsets.only(bottom: 4),
        title: Row(
          children: [
            Text(
              widget.otherUser.firstName ?? "",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.titleMedium!.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
            ),
            if (widget.otherUser.isOnline)
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 8),
                child: OnlineStatus(),
              ),
          ],
        ),
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(
              width: 15,
            ),
            const BackButton(color: Colors.white),
            //const SizedBox(width: 16,),
            UserCirlePicture(
              imageUrl: widget.otherUser.profilePicture,
              size: AppConstants.defaultNumericValue * 3,
            )
          ],
        ),
      ),
    );
  }
}

class ChatTextFieldAndOthers extends StatefulWidget {
  final TextEditingController chatController;
  final VoidCallback onTapEmoji;
  final VoidCallback onTapVoice;
  final VoidCallback onTapSend;
  final VoidCallback onChangeText;
  final VoidCallback onTapTextField;
  final String? imageUrl;
  final String? videoUrl;
  final String? audioUrl;
  final String? fileUrl;
  final Function(String?) onImageSelected;
  final Function(String?) onVideoSelected;
  final Function(String?) onAudioSelected;
  final Function(String?) onFileSelected;

  const ChatTextFieldAndOthers({
    super.key,
    required this.chatController,
    required this.onTapEmoji,
    required this.onTapVoice,
    required this.onTapSend,
    required this.onChangeText,
    required this.onTapTextField,
    this.imageUrl,
    this.videoUrl,
    this.audioUrl,
    this.fileUrl,
    required this.onImageSelected,
    required this.onVideoSelected,
    required this.onAudioSelected,
    required this.onFileSelected,
  });

  @override
  State<ChatTextFieldAndOthers> createState() => _ChatTextFieldAndOthersState();
}

class _ChatTextFieldAndOthersState extends State<ChatTextFieldAndOthers> {
  final CustomPopupMenuController _addMenuController =
      CustomPopupMenuController();

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (widget.imageUrl != null)
          Stack(
            children: [
              Image.file(
                File(widget.imageUrl!),
                fit: BoxFit.cover,
                height: 300,
              ),
              Positioned(
                right: 0,
                top: 0,
                child: IconButton(
                  icon: const Icon(Icons.close),
                  color: AppConstants.primaryColor,
                  onPressed: () {
                    widget.onImageSelected(null);
                  },
                ),
              ),
            ],
          ),
        if (widget.videoUrl != null)
          Stack(
            children: [
              VideoPlayerThumbNail(
                  onTap: () {
                    Navigator.of(context).push(MaterialPageRoute(
                      builder: (context) => VideoPlayerPage(
                          isNetwork: false, videoUrl: widget.videoUrl!),
                    ));
                  },
                  videoUrl: widget.videoUrl),
              Positioned(
                right: 0,
                top: 0,
                child: IconButton(
                  icon: const Icon(Icons.close),
                  color: AppConstants.primaryColor,
                  onPressed: () {
                    widget.onVideoSelected(null);
                  },
                ),
              ),
            ],
          ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            CustomPopupMenu(
              menuBuilder: () => ClipRRect(
                borderRadius:
                    BorderRadius.circular(AppConstants.defaultNumericValue / 2),
                child: Container(
                  decoration: BoxDecoration(color: AppConstants.primaryColor),
                  child: IntrinsicWidth(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        ChatAddMenuItem(
                          icon: CupertinoIcons.photo_camera_solid,
                          title: 'Camera',
                          onTap: () async {
                            _addMenuController.hideMenu();
                            pickMedia(isCamera: true).then((value) {
                              widget.onImageSelected(value);
                            });
                          },
                        ),
                        ChatAddMenuItem(
                          icon: CupertinoIcons.photo_fill,
                          title: 'Gallery',
                          onTap: () {
                            _addMenuController.hideMenu();
                            pickMedia(isCamera: false).then((value) {
                              widget.onImageSelected(value);
                            });
                          },
                        ),
                        // ChatAddMenuItem(
                        //   icon: CupertinoIcons.mic_solid,
                        //   title: 'Audio',
                        //   onTap: () {
                        //     _addMenuController.hideMenu();
                        //     widget.onAudioSelected(null);
                        //   },
                        // ),
                        ChatAddMenuItem(
                          icon: CupertinoIcons.video_camera_solid,
                          title: 'Video',
                          onTap: () {
                            _addMenuController.hideMenu();
                            pickMedia(isVideo: true).then((value) {
                              widget.onVideoSelected(value);
                            });
                          },
                        ),
                        // ChatAddMenuItem(
                        //   icon: CupertinoIcons.paperclip,
                        //   title: 'File',
                        //   onTap: () {
                        //     _addMenuController.hideMenu();
                        //     widget.onFileSelected(null);
                        //   },
                        // ),
                      ],
                    ),
                  ),
                ),
              ),
              pressType: PressType.singleClick,
              verticalMargin: -10,
              controller: _addMenuController,
              arrowColor: AppConstants.primaryColor,
              barrierColor: AppConstants.primaryColor.withOpacity(0.1),
              child: CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: null,
                child: SvgPicture.asset(AppConstants.camera),
              ),
            ),
            Expanded(
              child: Container(
                padding: const EdgeInsets.only(
                    left: AppConstants.defaultNumericValue),
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.circular(AppConstants.defaultNumericValue),
                  color: const Color(0xFFF2F2F2),
                ),
                child: Row(
                  // crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Expanded(
                      child: TextField(
                        controller: widget.chatController,
                        keyboardType: TextInputType.multiline,
                        minLines: 1,
                        maxLines: 6,
                        onTap: widget.onTapTextField,
                        onSubmitted: (value) {
                          widget.onTapSend();
                        },
                        onChanged: (_) {
                          widget.onChangeText();
                        },
                        decoration: const InputDecoration(
                          hintText: 'Type here...',
                          border: InputBorder.none,

                          // contentPadding: EdgeInsets.zero,
                          isDense: true,
                        ),
                      ),
                    ),
                    //Emoji
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed: widget.onTapEmoji,
                      child: Image.asset(AppConstants.smiley),
                    ),
                  ],
                ),
              ),
            ),

            //TODO: Send Voice Message
            widget.chatController.text.isEmpty &&
                    widget.imageUrl == null &&
                    widget.videoUrl == null &&
                    widget.audioUrl == null &&
                    widget.fileUrl == null
                // ? CupertinoButton(
                //     padding: EdgeInsets.zero,
                //     onPressed: widget.onTapVoice,
                //     child: const Icon(CupertinoIcons.mic_circle_fill),
                //   )
                ? CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: null,
                    child: Image.asset(AppConstants.sendMessage),
                  )
                : CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: widget.onTapSend,
                    child: Image.asset(AppConstants.sendMessage),
                  ),
          ],
        ),
      ],
    );
  }
}

class MoreMenuTitle extends StatelessWidget {
  final VoidCallback onTap;

  final String title;
  const MoreMenuTitle({
    super.key,
    required this.onTap,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultNumericValue,
            vertical: AppConstants.defaultNumericValue / 1.2),
        child: Text(title,
            style: Theme.of(context)
                .textTheme
                .titleSmall!
                .copyWith(color: Colors.black87)),
      ),
    );
  }
}

class ChatAddMenuItem extends StatelessWidget {
  final VoidCallback onTap;
  final IconData icon;
  final String title;
  const ChatAddMenuItem({
    super.key,
    required this.onTap,
    required this.icon,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.defaultNumericValue,
            vertical: AppConstants.defaultNumericValue / 1.2),
        child: Row(
          children: [
            Icon(
              icon,
              size: Theme.of(context).textTheme.titleSmall!.fontSize,
              color: Colors.white,
            ),
            const SizedBox(width: AppConstants.defaultNumericValue),
            Expanded(
              child: Text(title,
                  style: Theme.of(context).textTheme.titleSmall!.copyWith(
                      color: Colors.white, fontWeight: FontWeight.bold)),
            ),
          ],
        ),
      ),
    );
  }
}

class MessageSingleTile extends ConsumerWidget {
  final ChatItemModel chat;
  final String matchId;
  final bool isSearching;
  final UserProfileModel otherUser;

  const MessageSingleTile(
      {super.key,
      required this.chat,
      required this.matchId,
      required this.isSearching,
      required this.otherUser});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    bool isPrimeUser = false;
    bool isPrimePlusUser = false;
    String currentUserId = "";

    final bool? isNotMe = chat.userId == null
        ? null
        : chat.userId != ref.watch(currentUserStateProvider)?.uid;

    final userProfileRef = ref.watch(userProfileFutureProvider);
    userProfileRef.when(
      data: (data) {
        isPrimeUser = data?.isPremiumUser ?? false;
        isPrimePlusUser = data?.isPremiumPlusUser ?? false;
        currentUserId = data?.userId ?? "";
        if (!isPrimeUser) {
          isPrimeUser = (otherUser.isPremiumUser ?? false) ? false : true;
          isPrimePlusUser =
              (otherUser.isPremiumPlusUser ?? false) ? false : true;
        }
      },
      error: (error, stackTrace) {},
      loading: () {},
    );

    if (isNotMe == null) {
      return const SizedBox();
      // return Padding(
      //   padding: const EdgeInsets.all(8.0),
      //   child: Center(
      //       child: Container(
      //     padding: const EdgeInsets.symmetric(
      //       horizontal: AppConstants.defaultNumericValue,
      //       vertical: AppConstants.defaultNumericValue / 2,
      //     ),
      //     decoration: BoxDecoration(
      //       color: Colors.black.withOpacity(0.2),
      //       borderRadius: BorderRadius.circular(
      //         AppConstants.defaultNumericValue,
      //       ),
      //     ),
      //     child: Text(decryptText(chat.message ?? "")),
      //   )),
      // );
    } else {
      if (!chat.isRead) {
        if (isNotMe) {
          ref
              .read(chatProvider)
              .updateChatItem(matchId, chat.copyWith(isRead: true));
        }
      }

      return Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          //if(lastDate != DateFormatter.toYearMonth(chat.createdAt))
          Visibility(
            visible: chat.isDateVisible ?? false,
            child: Align(
              alignment: isNotMe ? Alignment.centerLeft : Alignment.centerRight,
              child: Container(
                margin: EdgeInsets.only(
                    left: isNotMe ? AppConstants.defaultNumericValue / 2 : 0,
                    right: !isNotMe ? AppConstants.defaultNumericValue / 2 : 0,
                    top: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  //mainAxisSize: MainAxisSize.min,
                  children: [
                    const Expanded(
                      child: Padding(
                          padding: EdgeInsets.only(left: 22, right: 12),
                          child: Divider(height: 10, color: Colors.grey)),
                    ),
                    Text(
                      DateFormatter.toYearMonth(chat.createdAt),
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    const Expanded(
                      child: Padding(
                          padding: EdgeInsets.only(left: 12, right: 22),
                          child: Divider(height: 10, color: Colors.grey)),
                    ),
                  ],
                ),
              ),
            ),
          ),

          Align(
            alignment: isNotMe ? Alignment.centerLeft : Alignment.centerRight,
            child: Padding(
              padding: EdgeInsets.only(
                  left: isNotMe ? 22 : 0, right: isNotMe ? 0 : 22),
              child: Container(
                margin: EdgeInsets.only(
                    left: isNotMe ? AppConstants.defaultNumericValue / 2 : 0,
                    right: !isNotMe ? AppConstants.defaultNumericValue / 2 : 0,
                    top: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      DateFormatter.toTime(chat.createdAt),
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ),
          ),
          Align(
            alignment: isNotMe ? Alignment.centerLeft : Alignment.centerRight,
            child: Padding(
              padding: EdgeInsets.only(
                  left: isNotMe ? 22 : 0, right: isNotMe ? 0 : 22),
              child: Container(
                margin:
                    const EdgeInsets.all(AppConstants.defaultNumericValue / 4),
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.8,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      isNotMe
                          ? AppConfig.chatTextFieldAndOtherText
                          : chat.message != null
                              ? const Color(0xFFC70973)
                              : AppConfig.chatTextFieldAndOtherText,
                      isNotMe
                          ? AppConfig.chatTextFieldAndOtherText
                          : chat.message != null
                              ? const Color(0xFF46239F)
                              : AppConfig.chatTextFieldAndOtherText,
                    ],
                  ),
                  // color: isNotMe
                  //     ? AppConfig.chatTextFieldAndOtherText
                  //     : AppConfig.chatMyTextColor,
                  borderRadius: BorderRadius.only(
                    topLeft: isNotMe
                        ? Radius.zero
                        : const Radius.circular(
                            AppConstants.defaultNumericValue - 2),
                    topRight: const Radius.circular(
                        AppConstants.defaultNumericValue - 2),
                    bottomLeft: const Radius.circular(
                        AppConstants.defaultNumericValue - 2),
                    bottomRight: isNotMe
                        ? const Radius.circular(
                            AppConstants.defaultNumericValue - 2)
                        : Radius.zero,
                  ),
                ),
                child: Padding(
                  padding: EdgeInsets.all(chat.message != null
                      ? AppConstants.defaultNumericValue / 1.3
                      : 0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: isNotMe
                        ? CrossAxisAlignment.start
                        : CrossAxisAlignment.end,
                    children: [
                      if (chat.image != null)
                        Container(
                          //height: MediaQuery.of(context).size.height * 0.3,
                          height: 180,
                          width: 130,
                          decoration: const BoxDecoration(
                            borderRadius: BorderRadius.all(Radius.circular(16)),
                          ),
                          child: GestureDetector(
                            onTap: () {
                              Navigator.of(context).push(MaterialPageRoute(
                                builder: (context) => ChatVideoPlayerPage(
                                    isNetwork: true,
                                    videoUrl: "",
                                    userName:
                                        isNotMe ? otherUser.firstName ?? "" : "You",
                                    imageUrl: chat.image ?? "",
                                    time: DateFormatter.toTime(chat.createdAt)),
                              ));
                            },
                            child: ClipRRect(
                                //decoration: const BoxDecoration(
                                borderRadius:
                                    const BorderRadius.all(Radius.circular(16)),
                                //color: Colors.black38,
                                // ),
                                child: Image.network(
                                  //height: 100,
                                  //width: 130,
                                  chat.image!,
                                  fit: BoxFit.cover,
                                )),
                          ),
                        ),
                      if (chat.image != null && chat.message != null)
                        const SizedBox(height: 8),
                      if (chat.video != null)
                        InkWell(
                          onTap: () {
                            if ((isPrimeUser || isPrimePlusUser)) {
                              Navigator.of(context).push(MaterialPageRoute(
                                builder: (context) => ChatVideoPlayerPage(
                                    isNetwork: true,
                                    videoUrl: chat.video!,
                                    userName:
                                        isNotMe ? otherUser.firstName ?? "" : "You",
                                    imageUrl: "",
                                    time: DateFormatter.toTime(chat.createdAt)),
                              ));
                            } else {
                              if (chat.isIntroVideo &&
                                  currentUserId != chat.userId) {
                                EasyLoading.showToast(
                                    "You are not a prime user for watching this video");
                              } else {
                                Navigator.of(context).push(MaterialPageRoute(
                                  builder: (context) => ChatVideoPlayerPage(
                                      isNetwork: true,
                                      videoUrl: chat.video!,
                                      userName:
                                          isNotMe ? otherUser.firstName ?? "" : "You",
                                      imageUrl: "",
                                      time:
                                          DateFormatter.toTime(chat.createdAt)),
                                ));
                              }
                            }
                          },
                          child: ClipRRect(
                              borderRadius: BorderRadius.circular(
                                  16), // Adjust the radius as needed
                              child: Stack(children: [
                                SizedBox(
                                    height: 180,
                                    width: 130,
                                    child: Image.network(
                                      chat.thumbnail ?? "",
                                      height:
                                          MediaQuery.of(context).size.height /
                                              1.5,
                                      width: MediaQuery.of(context).size.width,
                                      fit: BoxFit.cover,
                                      loadingBuilder:
                                          (context, child, loadingProgress) {
                                        if (loadingProgress == null)
                                          return child;
                                        return const Center(
                                            child: CircularProgressIndicator
                                                .adaptive());
                                      },
                                      errorBuilder:
                                          (context, error, stackTrace) {
                                        return const Center(
                                            child: Icon(CupertinoIcons.photo));
                                      },
                                    )),
                                Positioned(
                                  bottom: 10,
                                  left: 10,
                                  child: Text(
                                      parseDuration(chat.videoDuration ?? ""),
                                      style:
                                          const TextStyle(color: Colors.white)),
                                )
                              ])),
                        ),
                      if (chat.video != null && chat.message != null)
                        const SizedBox(height: 8),
                      if (chat.audio != null)
                        // VoiceMessage(
                        //   audioSrc: chat.audio!,
                        //   me: !isNotMe,
                        //   contactBgColor: Colors.white,
                        //   meBgColor: AppConstants.primaryColor,
                        //   contactFgColor: AppConstants.primaryColor,
                        //   contactPlayIconColor: Colors.white,
                        //   mePlayIconColor: AppConstants.primaryColor,
                        // ),
                        if (chat.audio != null && chat.message != null)
                          const SizedBox(height: 8),
                      if (chat.message != null)
                        Text(
                          decryptText(chat.message!),
                          style: TextStyle(
                              fontSize: 16,
                              backgroundColor:
                                  isSearching ? Colors.white : null,
                              color: isNotMe ? null : Colors.white),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          isNotMe
              ? const SizedBox(height: AppConstants.defaultNumericValue / 8)
              : const SizedBox()
        ],
      );
    }
  }
}

String parseDuration(String durationString) {
  List<String> parts = durationString.split(':');
  int hours = int.parse(parts[0]);
  int minutes = int.parse(parts[1]);
  int seconds = int.parse(parts[2].split('.')[0]);
  // int milliseconds = int.parse(parts[2].split('.')[1]);
  // final duration = Duration(hours: hours, minutes: minutes, seconds: seconds, milliseconds: milliseconds);
  return '${minutes.toString().padLeft(2, '0')}:${(seconds).toString().padLeft(2, '0')}';
}

class VoiceRecorder extends ConsumerWidget {
  final String matchId;
  const VoiceRecorder({super.key, required this.matchId});

  @override
  Widget build(BuildContext context, ref) {
    return Container(
      margin: const EdgeInsets.all(AppConstants.defaultNumericValue),
      // height: MediaQuery.of(context).size.height * 0.2,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.defaultNumericValue),
      ),
      padding: const EdgeInsets.all(AppConstants.defaultNumericValue),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            contentPadding: EdgeInsets.zero,
            title: const Text("Record your voice message"),
            trailing: IconButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                icon: const Icon(Icons.close)),
            subtitle: const Text("Press and hold the button to record"),
          ),
          const Divider(height: 0),
          const SizedBox(height: AppConstants.defaultNumericValue),
          Align(
            alignment: Alignment.centerRight,
            child: SocialMediaRecorder(
              recordIconWhenLockBackGroundColor: AppConstants.primaryColor,
              recordIconBackGroundColor: AppConstants.primaryColor,
              recordIcon: const Icon(
                CupertinoIcons.mic_circle_fill,
                color: Colors.white,
                size: 30,
              ),
              backGroundColor: AppConstants.primaryColor,
              radius: BorderRadius.circular(8),
              sendRequestFunction: (soundFile, _) async {
                final chatData = ref.read(chatProvider);
                final currentTime = DateTime.now();
                CustomAppLoader.showCustomLoader("Sending voice message...");

                await chatData
                    .uploadFile(file: soundFile, matchId: matchId)
                    .then((audioUrl) {
                  EasyLoading.dismiss();

                  if (audioUrl == null) {
                    EasyLoading.showError('Failed to send voice message');
                  } else {
                    ChatItemModel chatItem = ChatItemModel(
                      createdAt: currentTime,
                      id: currentTime.millisecondsSinceEpoch.toString(),
                      userId: ref.watch(currentUserStateProvider)!.uid,
                      matchId: matchId,
                      isRead: false,
                      audio: audioUrl,
                    );

                    chatData.createChatItem(matchId, chatItem);
                  }
                  Navigator.of(context).pop();
                });
              },
              encode: AudioEncoderType.AAC_LD,
            ),
          ),
        ],
      ),
    );
  }
}

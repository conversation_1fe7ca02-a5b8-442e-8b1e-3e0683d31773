import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomStatusBarTheme extends StatelessWidget {
  final Widget child;
  final bool isLightTheme;
  const CustomStatusBarTheme(
      {Key? key, required this.child, this.isLightTheme = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle(
            statusBarIconBrightness:
                isLightTheme ? Brightness.light : Brightness.dark,
            statusBarBrightness:
                isLightTheme ? Brightness.light : Brightness.dark,
            statusBarColor: Colors.transparent),
        child: child);
  }
}

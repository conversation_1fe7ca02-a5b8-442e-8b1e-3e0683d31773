import 'package:flutter/material.dart';
import 'custom_button.dart';

class CustomConfirmationDialog extends StatelessWidget {
  final String? title;
  final String desciption;
  final String? buttonText;
  final Function() onButtonClick;

  const CustomConfirmationDialog(
      {super.key,
      this.title,
      required this.desciption,
      required this.onButtonClick,
      this.buttonText});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      elevation: 0,
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Visibility(
                  visible: title!.isNotEmpty,
                  child: Center(
                    child: Text(
                      title ?? "Alert",
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                          fontSize: 18, fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 8,
                ),
                Text(
                  desciption,
                  style: const TextStyle(
                      fontSize: 14, fontWeight: FontWeight.w400),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            const SizedBox(
              height: 40,
            ),
            Container(
              alignment: Alignment.center,
              child: CustomButton(
                onPressed: () {
                  Navigator.pop(context);
                  onButtonClick();
                },
                text: buttonText ?? "Ok",
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomTwoButtonDialog extends StatelessWidget {
  final String? title;
  final String desciption;
  final String? buttonText;
  final String? cancelButtonText;
  final Function() onButtonClick;

  const CustomTwoButtonDialog(
      {super.key,
      this.title,
      required this.desciption,
      required this.onButtonClick,
      required this.cancelButtonText,
      this.buttonText});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      elevation: 0,
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Visibility(
                  visible: title!.isNotEmpty,
                  child: Center(
                    child: Text(
                      title ?? "Alert",
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                          fontSize: 18, fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 8,
                ),
                Text(
                  desciption,
                  style: const TextStyle(
                      fontSize: 14, fontWeight: FontWeight.w400),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            const SizedBox(
              height: 40,
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  flex: 1,
                  child: Container(
                    alignment: Alignment.center,
                    child: CustomButton(
                      onPressed: () {
                        Navigator.pop(context);
                        onButtonClick();
                      },
                      text: buttonText ?? "Ok",
                    ),
                  ),
                ),
                const SizedBox(
                  width: 5,
                ),
                Expanded(
                  flex: 1,
                  child: Container(
                    alignment: Alignment.center,
                    child: CustomButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      text: cancelButtonText ?? "Cancel",
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

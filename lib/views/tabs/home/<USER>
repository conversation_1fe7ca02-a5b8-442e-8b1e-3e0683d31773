import 'package:cached_network_image/cached_network_image.dart';
import 'package:fringle_app/config/config.dart';
import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/helpers/media_picker_helper.dart';
import 'package:fringle_app/models/match_model.dart';
import 'package:fringle_app/models/notification_model.dart';
import 'package:fringle_app/models/user_account_settings_model.dart';
import 'package:fringle_app/models/user_interaction_model.dart';
import 'package:fringle_app/models/user_profile_model.dart';
import 'package:fringle_app/providers/auth_providers.dart';
import 'package:fringle_app/providers/interaction_provider.dart';
import 'package:fringle_app/providers/match_provider.dart';
import 'package:fringle_app/providers/notifiaction_provider.dart';
import 'package:fringle_app/providers/other_users_provider.dart';
import 'package:fringle_app/providers/user_profile_provider.dart';
import 'package:fringle_app/views/custom/custom_app_bar.dart';
import 'package:fringle_app/views/custom/custom_button.dart';
import 'package:fringle_app/views/custom/lottie/no_item_found_widget.dart';
import 'package:fringle_app/views/custom/subscription_builder.dart';
import 'package:fringle_app/views/others/user_card_widget.dart';
import 'package:fringle_app/views/settings/account_settings.dart';
import 'package:fringle_app/views/tabs/home/<USER>';
import 'package:fringle_app/views/tabs/home/<USER>';
import 'package:fringle_app/views/tabs/messages/components/chat_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:swipe_cards/swipe_cards.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../../../providers/swipe_count_provider.dart';
import '../../custom/custom_app_loader.dart';
import '../../custom/custom_confirmation_dialog.dart';
import '../../custom/tag_widget.dart';
import '../../membership/membership_screen.dart';
import 'filter_screen.dart';
import '../../others/user_details_page.dart';

class HomePage extends ConsumerStatefulWidget {
  final Function(NotificationModel notificationData)? onTapChange;

  const HomePage({super.key, this.onTapChange});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> {
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  final _menuKey = GlobalKey();
  final _locationKey = GlobalKey();
  final _notificationKey = GlobalKey();
  final _exploreKey = GlobalKey();

  final List<TargetFocus> _targets = [];
  int totalInteraction = 0;
  FilterData? filterData;
  bool isRefresh = true;
  bool isGridView = false;
  bool isPrimeUser = false;
  bool isPrimePlusUser = false;
  late SharedPreferences _prefs;
  bool isCoupleAccount = false;

  @override
  void initState() {
    final showGuidedTour = Hive.box(HiveConstants.hiveBox)
        .get(HiveConstants.guidedTour, defaultValue: true) as bool;

    if (showGuidedTour) {
      Future.delayed(const Duration(milliseconds: 500), () async {
        _showTutorials();
        await setShowGuidedTour(false);
      });
    }
    setData();

    super.initState();
  }

  Future<void> setData() async {
    _prefs = await SharedPreferences.getInstance();
    filterData = FilterData(
      gender: _prefs.getString('gender') ?? "",
      seekingFilter: _prefs.getString('seekingFilter') ?? "",
      startAge: (_prefs.getDouble('ageStart') ?? 18.0).toInt(),
      endAge: (_prefs.getDouble('ageEnd') ?? 99.0).toInt(),
      distance: (_prefs.getDouble('distance') ?? 100).toInt(),
    );
    setState(() {});
  }

  void _showTutorials() {
    _targets.clear();
    _targets.add(
      TargetFocus(
        identify: "Menu",
        keyTarget: _menuKey,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Menu",
                  style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 20.0),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 10.0),
                  child: Text(
                    "This is the app menu.\n\nClick here to open the menu.\n\nYou will find your profile, account settings, and other options here.\n\nYou can also logout from here.",
                    style: TextStyle(color: Colors.white),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );

    _targets.add(
      TargetFocus(
        identify: "Location",
        keyTarget: _locationKey,
        shape: ShapeLightFocus.RRect,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Location",
                  style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 20.0),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 10.0),
                  child: Text(
                    "This is your current location.\n\nYou can change your location here.\n\nYou can also change your location from the app menu.\n\nTapping here will take you to the account settings page.",
                    style: TextStyle(color: Colors.white),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );

    _targets.add(
      TargetFocus(
        identify: "Notification",
        keyTarget: _notificationKey,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Notifications",
                  style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 20.0),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 10.0),
                  child: Text(
                    "This is the notification icon.\n\nYou will get notifications here.\n\nTapping here will take you to the notification page.",
                    style: TextStyle(color: Colors.white),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );

    _targets.add(
      TargetFocus(
        identify: "Explore",
        keyTarget: _exploreKey,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Explore",
                  style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 20.0),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 10.0),
                  child: Text(
                    "This is the explore icon.\n\nTapping here will take you to the explore page.\n\nYou can explore other users based on interests. You can also search for users here.",
                    style: TextStyle(color: Colors.white),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );

    // TutorialCoachMark(
    //   targets: _targets,
    //   colorShadow: AppConstants.primaryColor,
    //   onClickTarget: (target) {},
    //   onClickTargetWithTapPosition: (target, tapDetails) {},
    //   onClickOverlay: (target) {},
    //   onSkip: () {},
    //   onFinish: () {},
    // ).show(context: context);
  }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       key: _scaffoldKey,
//       appBar: AppBar(
//         backgroundColor: Colors.transparent,
//         elevation: 0,
//         systemOverlayStyle: SystemUiOverlayStyle.dark,
//         leading: const SizedBox(),
//         toolbarHeight: 0,
//       ),
//       //drawer: const AppDrawer(),
//       body: Padding(
//         padding: const EdgeInsets.symmetric(
//             horizontal: AppConstants.defaultNumericValue),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.stretch,
//           children: [
//             const SizedBox(height: AppConstants.defaultNumericValue),
//             CustomAppBar(
//               leading: Image.asset(
//                 AppConstants.flusterLogoDatkPng,
//                 height: 50,
//                 width: 148,
//               ),

//               // CustomIconButton(
//               //   key: _menuKey,
//               //   icon: CupertinoIcons.phone_arrow_up_right,
//               //   onPressed: () {
//               //     _scaffoldKey.currentState?.openDrawer();
//               //   },
//               //   padding: const EdgeInsets.all(
//               //       AppConstants.defaultNumericValue / 1.5),
//               // ),
//               title: Consumer(
//                 key: _locationKey,
//                 builder: (context, ref, _) {
//                   final user = ref.watch(userProfileFutureProvider);
//                   return user.when(
//                       data: (data) {
//                         isPrimeUser = data?.isPremiumUser ?? false;
//                         isPrimePlusUser = data?.isPremiumPlusUser ?? false;
//                         isCoupleAccount = data?.isCoupleAccount ?? false;
//                         if (data?.userAccountSettingsModel.showOnlineStatus !=
//                             false) {
//                           if (data?.isOnline == false) {
//                             ref.read(userProfileNotifier).updateUserProfile(
//                                 data!.copyWith(isOnline: true));
//                           }
//                         }

//                         return data == null
//                             ? const SizedBox()
//                             : GestureDetector(
//                                 onTap: () {
//                                   Navigator.push(
//                                     context,
//                                     MaterialPageRoute(
//                                       builder: (context) =>
//                                           const AccountSettingsLandingWidget(),
//                                     ),
//                                   );
//                                 },
//                                 child: Row(
//                                   mainAxisSize: MainAxisSize.min,
//                                   mainAxisAlignment: MainAxisAlignment.center,
//                                   children: [
//                                     // Icon(
//                                     //   CupertinoIcons.phone_circle_fill,
//                                     //   color: AppConstants.primaryColor,
//                                     //   size: 18,
//                                     // ),
//                                     // const SizedBox(
//                                     //     width:
//                                     //         AppConstants.defaultNumericValue /
//                                     //             3),
//                                     // Flexible(
//                                     //   child: Text(
//                                     //     data.userAccountSettingsModel.location
//                                     //         .addressText,
//                                     //     maxLines: 1,
//                                     //     overflow: TextOverflow.ellipsis,
//                                     //     style: Theme.of(context)
//                                     //         .textTheme
//                                     //         .titleSmall!
//                                     //         .copyWith(
//                                     //             fontWeight: FontWeight.bold),
//                                     //   ),
//                                     // ),
//                                   ],
//                                 ),
//                               );
//                       },
//                       error: (_, __) => const SizedBox(),
//                       loading: () => const SizedBox());
//                 },
//               ),
//               trailing: Row(
//                 children: [
//                   InkWell(
//                     onTap: () {
//                       if ((isPrimeUser || isPrimePlusUser) == true) {
//                         setState(() {
//                           isGridView = !isGridView;
//                         });
//                       } else {
//                         // ignore: use_build_context_synchronously
//                         // showDialog(
//                         //     context: context,
//                         //     builder: (BuildContext mContext) {
//                         //       return CustomConfirmationDialog(
//                         //         title: "Unlock Premium Features",
//                         //         desciption: AppConstants.subscriptionDesc,
//                         //         onButtonClick: () {
//                         //           Navigator.of(context).push(
//                         //               MaterialPageRoute(builder: (context) {
//                         //             return MembershipScreen(
//                         //               isPremiumUser: isPrimeUser,
//                         //               isPremiumPlusUser: isPrimePlusUser,
//                         //             );
//                         //           }));
//                         //         },
//                         //       );
//                         //     });
//                       }
//                     },
//                     child: Padding(
//                       padding: const EdgeInsets.all(8.0),
//                       child: isGridView
//                           ? SvgPicture.asset(AppConstants.swipviewIcon)
//                           : SvgPicture.asset(AppConstants.gridViewIcon),
//                     ),
//                   ),
//                   const SizedBox(
//                     width: 6,
//                   ),
//                   InkWell(
//                     onTap: () {
//                       Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                             builder: (context) => NotificationPage(
//                               notificationData: widget.onTapChange,
//                             ),
//                           ));
//                     },
//                     child: NotificationButton(key: _notificationKey),
//                   ),
//                   InkWell(
//                       onTap: () {
//                         Navigator.push(
//                             context,
//                             MaterialPageRoute(
//                               builder: (context) => FilterScreen(
//                                 isPremiumUser: isPrimeUser,
//                                 isPremiumPlusUser: isPrimePlusUser,
//                                 isCoupleAccount: isCoupleAccount,
//                                 filterData: (data) {
//                                   setState(() {
//                                     filterData = data;
//                                     isRefresh = false;
//                                   });
//                                   Future.delayed(
//                                       const Duration(milliseconds: 100), () {
//                                     setState(() {
//                                       isRefresh = true;
//                                     });
//                                   });
//                                 },
//                               ),
//                             ));
//                       },
//                       child: Container(
//                         margin: const EdgeInsets.only(right: 4),
//                         child: SvgPicture.asset(AppConstants.filter),
//                       )),
//                 ],
//               ),
//             ),
//             Expanded(
//               child: Consumer(
//                 builder: (context, ref, child) {
//                   final filteredUsers = ref.watch(filteredOtherUsersProvider);

//                   return filteredUsers.when(
//                     data: (data) {
//                       return data.isEmpty
//                           ? const HomePageNoUsersFoundWidget()
//                           : FilterInteraction(
//                                     isPremiumUser: true,
//                                     isPremiumPlusUser: true,
//                                     users: data,
//                                     // filterData: filterData,
//                                     isRefresh: isRefresh,
//                                     isGridView: isGridView,
//                           );
//                     },
//                     error: (_, __) => const Center(
//                       child: Text("Something Went Wrong!"),
//                     ),
//                     loading: () => const Center(
//                       child: CircularProgressIndicator.adaptive(),
//                     ),
//                   );
//                 },
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final filteredUsers = ref.watch(filteredOtherUsersProvider);
    
        return filteredUsers.when(
          data: (data) {
            return data.isEmpty
                ? const HomePageNoUsersFoundWidget()
                : FilterInteraction(
                    isPremiumUser: true,
                    isPremiumPlusUser: true,
                    users: data,
                    // filterData: filterData,
                    isRefresh: isRefresh,
                    isGridView: isGridView,
                  );
          },
          error: (_, __) => const Center(
            child: Text("Something Went Wrong!"),
          ),
          loading: () => const Center(
            child: CircularProgressIndicator.adaptive(),
          ),
        );
      },
    );
  }
}

class NotificationButton extends ConsumerWidget {
  const NotificationButton({
    super.key,
  });

  @override
  Widget build(BuildContext context, ref) {
    final matchingNotifications = ref.watch(notificationsStreamProvider);

    int count = 0;

    matchingNotifications.whenData((value) {
      for (var element in value) {
        if (element.isRead == false) {
          count++;
        }
      }
    });

    return Stack(
      children: [
        Container(
          margin: const EdgeInsets.only(right: 12),
          height: 42,
          width: 42,
          child: Center(
            child: SvgPicture.asset(AppConstants.notificationBellPng),
          ),
        ),
        if (count > 0)
          Positioned(
            left: 20,
            top: 5,
            child: Badge(
              backgroundColor: Colors.red,
              label: Text(
                count.toString(),
                style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                    fontSize: 7),
              ),
            ),
          ),
      ],
    );
  }
}

class FilterInteraction extends ConsumerWidget {
  final bool isPremiumUser;
  final bool isPremiumPlusUser;
  final List<UserProfileModel> users;
  FilterData? filterData;
  bool isRefresh;
  final bool isGridView;
  bool? isSecondaryUser;
  FilterInteraction(
      {super.key,
      required this.isPremiumUser,
      required this.isPremiumPlusUser,
      required this.users,
      this.filterData,
      required this.isRefresh,
      required this.isGridView,
      this.isSecondaryUser});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentUserProfile = ref.watch(userProfileFutureProvider);
    UserProfileModel? currentUser;
    currentUserProfile.when(
        data: (data) {
          if (data != null) {
            currentUser = data;
          }
        },
        error: (_, __) => const SizedBox(),
        loading: () => const SizedBox());
    bool isPremiumUser = currentUser!.isPremiumUser ?? false;
    bool isPremiumPlusUser = currentUser!.isPremiumPlusUser ?? false;
    final otherUsers = ref.watch(otherUsersProvider);
    otherUsers.when(
      data: (data) {
        for (var element in data) {
          if (element.isDMOn ?? false) {
            var today = DateFormat("MM/dd/yyyy").format(DateTime.now());
            var dmDate = DateFormat("MM/dd/yyyy")
                .format(element.dmDate ?? DateTime.now());
            DateTime currentTime = DateTime.now();
            if (today != dmDate ||
                currentTime.isAfter(element.dmEndTime ?? DateTime.now())) {
              ref.read(userProfileNotifier).updateUserProfile(element.copyWith(
                  userId: element.id,
                  isDMOn: false,
                  dmDate: null,
                  dmEndTime: null,
                  dmStartTime: null));
              ref.invalidate(userProfileFutureProvider);
            }
          }
        }
      },
      error: (error, stackTrace) {},
      loading: () {},
    );
    final interactionProvider = ref.watch(interactionFutureProvider);

    return interactionProvider.when(
      data: (data) {
        final List<UserProfileModel> filteredUsers = users;
        if (filteredUsers.isNotEmpty) {
          final usersToRemove = [];
          for (final mData in data) {
            usersToRemove.add(mData.intractToUserId);
          }
          filteredUsers.removeWhere((user) => usersToRemove.contains(user.userId));
        }

        List<UserProfileModel> boostedUsers = [];
        List<UserProfileModel> preferencedUsers = [];
        List<UserProfileModel> preferencedUsers2 = [];
        List<UserProfileModel> preferencedUsers3 = [];
        List<UserProfileModel> preferencedUsers4 = [];
        List<UserProfileModel> preferencedUsers5 = [];
        List<UserProfileModel> preferencedUsers6 = [];

        for (var user in filteredUsers) {
          if (user.isFeaturedOn == true) {
            if (user.featuredTime != null) {
              DateTime futureDate =
                  user.featuredTime!.add(const Duration(days: 1));
              if (futureDate.isAfter(DateTime.now())) {
                boostedUsers.add(user);
                //setState(() {});
              } else {
                ref.read(userProfileNotifier).updateUserProfile(user.copyWith(
                    userId: user.id, isFeaturedOn: false, featuredTime: null));
                ref.invalidate(userProfileFutureProvider);
              }
            }
          }
        }

        for (var user in users) {
          if (!boostedUsers.contains(user)) {
            boostedUsers.add(user);
          }
        }

        // if (filterData != null) {
        //   for (var user in boostedUsers) {
        //     var userAge =
        //         DateTime.now().difference(user.birthDay!).inDays ~/ 365;
        //     if (filterData!.gender.isNotEmpty) {
        //       if ((userAge > filterData!.startAge &&
        //               userAge <= filterData!.endAge) &&
        //           user.gender == filterData!.gender) {
        //         preferencedUsers4.add(user);
        //       }
        //     } else {
        //       if ((userAge > filterData!.startAge &&
        //           userAge <= filterData!.endAge)) {
        //         preferencedUsers4.add(user);
        //       }
        //     }
        //   }
        // } else {

        if (currentUser?.preferences != null &&
                currentUser?.preferences?.isNotEmpty == true ||
            (seekingFilterListWithPremiumPlus
                    .contains(filterData?.seekingFilter) &&
                filterData?.seekingFilter != "Couples")) {
          for (var user in boostedUsers) {
            if ((currentUser?.preferences?["seeking"] != null) &&
                currentUser?.preferences?["seeking"] == "Couple") {
              if (user.isCoupleAccount ?? false) {
                preferencedUsers.add(user);
              }
            } else if (user.preferences != null) {
              if (currentUser?.preferences?["seeking"] != null) {
                if (user.preferences?["iAm"] ==
                    currentUser?.preferences?["seeking"]) {
                  preferencedUsers.add(user);
                }
              } else {
                preferencedUsers.addAll(boostedUsers);
              }
            } else {
              preferencedUsers.addAll(boostedUsers);
              break;
            }
          }

          if (currentUser?.preferences?["whoIs2"] != null) {
            for (var user in preferencedUsers) {
              if (user.preferences?["whoIs"] ==
                  currentUser?.preferences?["whoIs2"]) {
                preferencedUsers2.add(user);
              }
            }
          } else {
            preferencedUsers2.addAll(preferencedUsers);
          }
          // this condition is about if filter selected then priority will be for filter condition
          if (filterData != null &&
              filterData?.seekingFilter != null &&
              filterData?.seekingFilter != "") {
            if (seekingFilterList.contains(filterData?.seekingFilter)) {
              for (var user in preferencedUsers2) {
                if (user.preferences?["typeOfRelationship"] ==
                    filterData?.seekingFilter) {
                  preferencedUsers3.add(user);
                }
              }
            } else {
              preferencedUsers3.addAll(preferencedUsers2);
            }
            if ((filterData?.seekingFilter == "Kinksters") &&
                (isPremiumUser || isPremiumPlusUser) &&
                (currentUser?.preferences?["seeking"] != "Couple")) {
              for (var user in preferencedUsers3) {
                if (listKink.contains(user.preferences?["kink"])) {
                  preferencedUsers4.add(user);
                }
              }
            } else {
              preferencedUsers4.addAll(preferencedUsers3);
            }

            if ((filterData?.seekingFilter == "Collaborators") &&
                (isPremiumPlusUser) &&
                (currentUser?.preferences?["seeking"] != "Couple")) {
              for (var user in preferencedUsers4) {
                if (listContentCreator
                    .contains(user.preferences?["contentCreator"])) {
                  preferencedUsers5.add(user);
                }
              }
            } else {
              preferencedUsers5.addAll(preferencedUsers4);
            }

            if (filterData?.seekingFilter == "Couples" &&
                (isPremiumUser || isPremiumPlusUser)) {
              List<UserProfileModel> couplePrefrence = [];
              for (var user in boostedUsers) {
                if (user.isCoupleAccount ?? false) {
                  if ((user.couplePrefrenceSecond?["coupleSecondary"] ==
                          "Couple") ||
                      user.couplePrefrencePrimary?["couplePrimery"] ==
                          "Couple") {
                    couplePrefrence.add(user);
                  }
                }
              }
              if (couplePrefrence.isNotEmpty) {
                preferencedUsers5.addAll(couplePrefrence);
              }
            } else {
              List<UserProfileModel> couplePrefrence = [];
              for (var user in boostedUsers) {
                if (user.isCoupleAccount ?? false) {
                  if ((user.couplePrefrenceSecond?["coupleSecondary"] ==
                          currentUser?.preferences?["whoIs2"]) ||
                      user.couplePrefrencePrimary?["couplePrimery"] ==
                          currentUser?.preferences?["whoIs2"]) {
                    couplePrefrence.add(user);
                  }
                }
              }
              if (couplePrefrence.isNotEmpty) {
                preferencedUsers5.addAll(couplePrefrence);
              }
            }
          } else {
            if (currentUser?.preferences?["typeOfRelationship"] != null) {
              for (var user in preferencedUsers2) {
                if (user.preferences?["typeOfRelationship"] ==
                    currentUser?.preferences?["typeOfRelationship"]) {
                  preferencedUsers3.add(user);
                }
              }
            } else {
              preferencedUsers3.addAll(preferencedUsers2);
            }

            // if (seekingFilterList.contains(filterData?.seekingFilter)) {
            //   for (var user in preferencedUsers2) {
            //     if (user.preferences?["typeOfRelationship"] ==
            //         filterData?.seekingFilter) {
            //       preferencedUsers3.add(user);
            //     }
            //   }
            // } else {
            //   preferencedUsers3.addAll(preferencedUsers2);
            // }

            if (currentUser?.preferences?["kink"] != null &&
                (isPremiumUser || isPremiumPlusUser) &&
                (currentUser?.preferences?["seeking"] != "Couple")) {
              for (var user in preferencedUsers3) {
                if (user.preferences?["kink"] ==
                    currentUser?.preferences?["kink"]) {
                  preferencedUsers4.add(user);
                }
              }
            } else {
              preferencedUsers4.addAll(preferencedUsers3);
            }

            if (currentUser?.preferences?["contentCreator"] != null &&
                (isPremiumPlusUser) &&
                (currentUser?.preferences?["seeking"] != "Couple")) {
              for (var user in preferencedUsers4) {
                if (user.preferences?["contentCreator"] ==
                    currentUser?.preferences?["contentCreator"]) {
                  preferencedUsers5.add(user);
                }
              }
            } else {
              preferencedUsers5.addAll(preferencedUsers4);
            }

            List<UserProfileModel> couplePrefrence = [];
            for (var user in boostedUsers) {
              if (user.isCoupleAccount ?? false) {
                if ((user.couplePrefrenceSecond?["coupleSecondary"] ==
                        currentUser?.preferences?["whoIs2"]) ||
                    user.couplePrefrencePrimary?["couplePrimery"] ==
                        currentUser?.preferences?["whoIs2"]) {
                  couplePrefrence.add(user);
                }
              }
            }
            if (couplePrefrence.isNotEmpty) {
              preferencedUsers5.addAll(couplePrefrence);
            }
          }
        } else {
          if (filterData?.seekingFilter == "Couples" &&
              (isPremiumUser || isPremiumPlusUser)) {
            List<UserProfileModel> couplePrefrence = [];
            for (var user in boostedUsers) {
              if (user.isCoupleAccount ?? false) {
                couplePrefrence.add(user);
              }
            }
            if (couplePrefrence.isNotEmpty) {
              preferencedUsers5.addAll(couplePrefrence);
            }
          } else {
            if ((currentUser?.isCoupleAccount ?? false)) {
              List<UserProfileModel> couplePrefrence = [];
              if (isSecondaryUser ?? false) {
                if (currentUser?.couplePrefrenceSecond != null &&
                    currentUser?.couplePrefrenceSecond?.isNotEmpty == true) {
                  for (var user in boostedUsers) {
                    if ((user.preferences?["whoIs"] ==
                            currentUser
                                ?.couplePrefrenceSecond?['seekingHer']) ||
                        (user.preferences?["whoIs"] ==
                            currentUser
                                ?.couplePrefrenceSecond?['seekingHim'])) {
                      couplePrefrence.add(user);
                    } else if (currentUser
                                ?.couplePrefrenceSecond?['seekingHer'] ==
                            "Any" &&
                        user.gender == "Female") {
                      couplePrefrence.add(user);
                    } else if (currentUser
                                ?.couplePrefrenceSecond?['seekingHim'] ==
                            "Any" &&
                        user.gender == "Male") {
                      couplePrefrence.add(user);
                    }
                  }
                  preferencedUsers5.addAll(couplePrefrence);
                } else {
                  preferencedUsers5.addAll(boostedUsers);
                }
              } else {
                if (currentUser?.couplePrefrencePrimary != null &&
                    currentUser?.couplePrefrencePrimary?.isNotEmpty == true) {
                  for (var user in boostedUsers) {
                    if ((user.preferences?["whoIs"] ==
                            currentUser
                                ?.couplePrefrencePrimary?['seekingHer']) ||
                        (user.preferences?["whoIs"] ==
                            currentUser
                                ?.couplePrefrencePrimary?['seekingHim'])) {
                      couplePrefrence.add(user);
                    } else if (currentUser
                                ?.couplePrefrencePrimary?['seekingHer'] ==
                            "Any" &&
                        user.gender == "Female") {
                      couplePrefrence.add(user);
                    } else if (currentUser
                                ?.couplePrefrencePrimary?['seekingHim'] ==
                            "Any" &&
                        user.gender == "Male") {
                      couplePrefrence.add(user);
                    }
                  }
                  preferencedUsers5.addAll(couplePrefrence);
                } else {
                  preferencedUsers5.addAll(boostedUsers);
                }
              }
            } else {
              preferencedUsers5.addAll(boostedUsers);
            }
          }
        }
        if (filterData != null) {
          for (var user in preferencedUsers5.toSet().toList()) {
            var userAge =
                DateTime.now().difference(user.birthDay!).inDays ~/ 365;
            if (filterData?.gender.isNotEmpty == true) {
              if ((userAge >= filterData!.startAge &&
                      userAge <= filterData!.endAge) &&
                  user.gender == filterData!.gender &&
                  (((Geolocator.distanceBetween(
                                  currentUser!.latitude ?? 0,
                                  currentUser!.longitude ?? 0,
                                  user.latitude ?? 0,
                                  user.longitude ?? 0) /
                              1000) *
                          0.621371) <=
                      (filterData?.distance ?? 100).toDouble())) {
                preferencedUsers6.add(user);
              }
            } else {
              if ((userAge >= filterData!.startAge &&
                  userAge <= filterData!.endAge &&
                  (((Geolocator.distanceBetween(
                                  currentUser!.latitude ?? 0,
                                  currentUser!.longitude ?? 0,
                                  user.latitude ?? 0,
                                  user.longitude ?? 0) /
                              1000) *
                          0.621371) <=
                      (filterData?.distance ?? 100).toDouble()))) {
                preferencedUsers6.add(user);
              }
            }
          }
        } else {
          preferencedUsers6.addAll(preferencedUsers5.toSet().toList());
        }
        return isRefresh == false
            ? const SizedBox()
            : HomeBody(
                users: users,
                isPremiumUser: true,
                isPremiumPlusUser: true,
                currentUser: currentUser,
                isGridView: isGridView,
                isRefresh: isRefresh,
              );
        //}
        // return preferencedUsers4.isEmpty
        //     ? const Center(child: NoItemFoundWidget(text: "No users found"))
        //     : !isRefresh
        //         ? const SizedBox()
        //         : HomeBody(
        //             users: preferencedUsers5,
        //             isPremiumUser: isPremiumUser,
        //             currentUser: currentUser,
        //             isGridView: isGridView,
        //             isRefresh: isRefresh,
        //           );
      },
      error: (_, __) => const Center(
        child: Text("Something Went Wrong!"),
      ),
      loading: () => const Center(
        child: CircularProgressIndicator.adaptive(),
      ),
    );
  }
}

class HomeBody extends ConsumerStatefulWidget {
  final List<UserProfileModel> users;
  final bool isPremiumUser;
  final bool isPremiumPlusUser;
  final UserProfileModel? currentUser;
  final bool isGridView;
  final bool? isRefresh;

  const HomeBody({
    super.key,
    required this.isPremiumUser,
    required this.isPremiumPlusUser,
    required this.users,
    required this.currentUser,
    required this.isGridView,
    required this.isRefresh,
  });

  @override
  ConsumerState<HomeBody> createState() => _HomeBodyState();

  //void onVideoSelected(String? value) {}
}

class _HomeBodyState extends ConsumerState<HomeBody> {
  late MatchEngine _matchEngine;
  final List<SwipeItem> _swipeItems = [];
  int indexNo = 0;

  //InterstitialAd? _interstitialAd;
  bool _isInterstitialAdLoaded = false;
  int totalInteraction = 0;
  int swipeCountLimit = 0;

  List<UserProfileModel> allUsers = [];
  List<UserProfileModel> boostUsers = [];
  List<UserProfileModel> nonBoosterUser = [];
  List<UserProfileModel> listOfuser = [];
  bool isMatch = false;

  @override
  void initState() {
    //final users = widget.users;
    //users.shuffle();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.isPremiumUser || widget.isPremiumPlusUser) {
        swipeCountLimit = 100000;
      } else {
        swipeCountLimit = ref.watch(aNumber);
      }
      setState(() {});
    });

    for (var element in widget.users) {
      if ((element.isFeaturedOn ?? false)) {
        boostUsers.add(element);
      } else {
        nonBoosterUser.add(element);
      }
      listOfuser.add(element);
    }
    //nonBoosterUser.shuffle();
    //boostUsers.shuffle();
    allUsers.addAll(boostUsers);
    allUsers.addAll(nonBoosterUser);
    allUsers.addAll(widget.users);

    for (var user in allUsers) {
      _swipeItems.add(
        SwipeItem(
          content: user,
          likeAction: () {
            if (totalInteraction < swipeCountLimit) {
              print("indexNo$indexNo");

              final user = _swipeItems[indexNo].content as UserProfileModel;

              final String myUserId = ref.watch(currentUserStateProvider)!.uid;
              final String id = myUserId + user.id;

              final UserInteractionModel interaction = UserInteractionModel(
                id: id,
                userId: myUserId,
                intractToUserId: user.id,
                isSuperLike: false,
                isLike: false,
                isDislike: false,
                createdAt: DateTime.now(),
              );

              onLike(interaction, myUserId, user, false);
            } else {
              // EasyLoading.showInfo(
              //     'You have used up your 50 swipes for today, you need to wait until tomorrow.');
            }
          },
          nopeAction: () {
            if (totalInteraction < swipeCountLimit) {
              print("indexNo$indexNo");

              final user = _swipeItems[indexNo].content as UserProfileModel;

              final String myUserId = ref.watch(currentUserStateProvider)!.uid;
              final String id = myUserId + user.id;

              final UserInteractionModel interaction = UserInteractionModel(
                id: id,
                userId: myUserId,
                intractToUserId: user.id,
                isSuperLike: false,
                isLike: false,
                isDislike: false,
                createdAt: DateTime.now(),
              );

              onDislike(interaction, false);
            } else {
              // EasyLoading.showInfo(
              //     'You have used up your 50 swipes for today, you need to wait until tomorrow.');
            }
          },
          superlikeAction: () {
            if (totalInteraction < swipeCountLimit) {
              print("indexNo$indexNo");

              final user = _swipeItems[indexNo].content as UserProfileModel;

              final String myUserId = ref.watch(currentUserStateProvider)!.uid;
              final String id = myUserId + user.id;

              final UserInteractionModel interaction = UserInteractionModel(
                id: id,
                userId: myUserId,
                intractToUserId: user.id,
                isSuperLike: false,
                isLike: false,
                isDislike: false,
                createdAt: DateTime.now(),
              );

              favourated(interaction, myUserId, user, false);
            } else {
              // EasyLoading.showInfo(
              //     'You have used up your 50 swipes for today, you need to wait until tomorrow.');
            }
          },
        ),
      );
    }

    _matchEngine = MatchEngine(swipeItems: _swipeItems);

    if (!(widget.isPremiumUser || widget.isPremiumPlusUser) &&
        isAdmobAvailable) {
      //InterstitialAd.load(
      //  adUnitId: Platform.isAndroid
      //      ? AndroidAdUnits.interstitialId
      //      : IOSAdUnits.interstitialId,
      //  request: const AdRequest(),
      //  adLoadCallback: InterstitialAdLoadCallback(
      //    onAdLoaded: (ad) {
      //      setState(() {
      //        _interstitialAd = ad;
      //        _isInterstitialAdLoaded = true;
      //      });
      //    },
      //    onAdFailedToLoad: (error) {},
      //  ),
      //);
    }
    getCurrentIneractionCount();

    super.initState();
  }

  @override
  void dispose() {
    _matchEngine.dispose();
    super.dispose();
  }

  void createInteractionNotification(
      {required String title,
      required String body,
      required String userId,
      required UserProfileModel currentUser,
      required int notificationType}) async {
    final currentTime = DateTime.now();
    final id = currentTime.millisecondsSinceEpoch.toString();
    final NotificationModel notificationModel = NotificationModel(
      id: id,
      userId: currentUser.userId,
      receiverId: userId,
      title: title,
      body: body,
      userName: "${currentUser.firstName} ${currentUser.lastName}",
      notificationType: notificationType,
      image: currentUser.profilePicture,
      createdAt: currentTime,
      isRead: false,
      isMatchingNotification: false,
      isInteractionNotification: true,
    );

    await addNotification(notificationModel);
  }

  void showMatchingDialog({
    required UserProfileModel currentUser,
    required UserProfileModel otherUser,
  }) async {
    final MatchModel matchModel = MatchModel(
      id: currentUser.userId + otherUser.userId,
      userIds: [currentUser.userId, otherUser.userId],
      isMatched: true,
    );

    await createConversation(matchModel).then((matchResult) {
      if (matchResult) {
        final currentTime = DateTime.now();
        final id =
            matchModel.id + currentTime.millisecondsSinceEpoch.toString();
        final NotificationModel notificationModel = NotificationModel(
          id: currentTime.millisecondsSinceEpoch.toString(),
          userId: currentUser.userId,
          receiverId: otherUser.userId,
          matchId: matchModel.id,
          title: "New Notification",
          body: "You have a new match",
          userName: "${currentUser.firstName} ${currentUser.lastName}",
          notificationType: AppConstants.matchNotifacation,
          image: currentUser.profilePicture,
          createdAt: currentTime,
          isRead: false,
          isMatchingNotification: true,
          isInteractionNotification: false,
        );

        addNotification(notificationModel);

        showDialog(
          context: context,
          builder: (context) {
            return SimpleDialog(
              shape: RoundedRectangleBorder(
                borderRadius:
                    BorderRadius.circular(AppConstants.defaultNumericValue),
              ),
              insetPadding:
                  const EdgeInsets.all(AppConstants.defaultNumericValue),
              contentPadding:
                  const EdgeInsets.all(AppConstants.defaultNumericValue),
              title: Stack(
                children: [
                  const Center(
                      child: Text(
                    "Matched",
                    textAlign: TextAlign.center,
                  )),
                  Positioned(
                    right: 5,
                    child: InkWell(
                      child: const Icon(Icons.close_rounded),
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                    ),
                  )
                ],
              ),
              children: [
                const SizedBox(height: AppConstants.defaultNumericValue),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    UserCirlePicture(
                        imageUrl: otherUser.profilePicture, size: 40),
                    const SizedBox(width: AppConstants.defaultNumericValue / 4),
                    UserCirlePicture(
                        imageUrl: currentUser.profilePicture, size: 40),
                  ],
                ),
                const SizedBox(height: AppConstants.defaultNumericValue),
                Center(
                  child: Text("You are now matched with ${otherUser.firstName}"),
                ),
                const SizedBox(height: AppConstants.defaultNumericValue),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                        child: OutlinedButton(
                            onPressed: () {
                              var today = DateFormat("MM/dd/yyyy")
                                  .format(DateTime.now());
                              var introDate = "";
                              var introCount = 0;
                              final userRef =
                                  ref.read(userProfileFutureProvider);
                              userRef.when(
                                  data: (value) => {
                                        if (value != null)
                                          {
                                            introDate =
                                                value.introVideoDate ?? "",
                                            introCount =
                                                value.introVideoCount ?? 0
                                          }
                                      },
                                  error: (_, __) => const SizedBox(),
                                  loading: () => const SizedBox());
                              introCount++;
                              // if (introDate == today && introCount > 10) {
                              //   EasyLoading.showError(
                              //       "Sorry, you've exceeded the daily limit of 10 introductory videos. Please try again tomorrow.");
                              // } else {
                              //   pickMedia(isVideo: true, isCamera: true)
                              //       .then((value) {
                              //     Navigator.of(context).pop();
                              //     Navigator.of(context).push(
                              //       MaterialPageRoute(
                              //         builder: (context) => ChatPage(
                              //           matchId: matchModel.id,
                              //           otherUserId: otherUser.userId,
                              //           introVideo: value,
                              //         ),
                              //       ),
                              //     );
                              //   });
                              // }
                              if ((((currentUser.isPremiumPlusUser ?? false) ||
                                          (currentUser.isPremiumUser ??
                                              false)) ==
                                      true) &&
                                  ((otherUser.isPremiumUser ?? false) ||
                                          (otherUser.isPremiumPlusUser ??
                                              false)) ==
                                      false) {
                                showDialog(
                                    context: context,
                                    builder: (BuildContext mContext) {
                                      return CustomTwoButtonDialog(
                                        title: "Premium User Alert",
                                        desciption:
                                            "Non- premium recipients cannot play intro videos",
                                        buttonText: "Send it anyway",
                                        cancelButtonText: "Ok",
                                        onButtonClick: () {
                                          if (introDate == today &&
                                              introCount > 10) {
                                            EasyLoading.showError(
                                                "Sorry, you've exceeded the daily limit of 10 introductory videos. Please try again tomorrow.");
                                          } else {
                                            pickMedia(
                                                    isVideo: true,
                                                    isCamera: true)
                                                .then((value) {
                                              Navigator.of(context).pop();
                                              Navigator.of(context).push(
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      ChatPage(
                                                    matchId: matchModel.id,
                                                    otherUserId:
                                                        otherUser.userId,
                                                    introVideo: value,
                                                  ),
                                                ),
                                              );
                                            });
                                          }
                                        },
                                      );
                                    });
                              } else {
                                if (introDate == today && introCount > 10) {
                                  EasyLoading.showError(
                                      "Sorry, you've exceeded the daily limit of 10 introductory videos. Please try again tomorrow.");
                                } else {
                                  pickMedia(isVideo: true, isCamera: true)
                                      .then((value) {
                                    Navigator.of(context).pop();
                                    Navigator.of(context).push(
                                      MaterialPageRoute(
                                        builder: (context) => ChatPage(
                                          matchId: matchModel.id,
                                          otherUserId: otherUser.userId,
                                          introVideo: value,
                                        ),
                                      ),
                                    );
                                  });
                                }
                              }
                            },
                            child: const Text("Send Intro Video",
                                style: TextStyle(fontSize: 12)))),
                    const SizedBox(width: AppConstants.defaultNumericValue),
                    Expanded(
                      child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => ChatPage(
                                  matchId: matchModel.id,
                                  otherUserId: otherUser.userId,
                                ),
                              ),
                            );
                          },
                          child: const Text("Start Chat",
                              style: TextStyle(fontSize: 12))),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 10,
                ),
                // if (otherUser.isPremiumUser == null ||
                //     otherUser.isPremiumUser != true)
                //   Text("${otherUser.fullName} is not a premium user")
              ],
            );
          },
        );
      }
    });
  }

  getCurrentIneractionCount() {
    final interactionProvider = ref.read(interactionFutureProvider);

    interactionProvider.when(
        data: (data) {
          totalInteraction = 0;
          for (var user in data) {
            DateTime now = DateTime.now();
            String dt1 = DateFormat('yyyy-MM-dd').format(now);

            //DateTime dt1 = now.toDate();

            DateTime date = DateTime.parse(user.createdAt.toString());
            String dt2 = DateFormat('yyyy-MM-dd').format(date);

            if (dt1 == dt2) {
              totalInteraction = totalInteraction + 1;
              setState(() {});
            }
          }

          if (totalInteraction == swipeCountLimit) {
            // EasyLoading.showInfo(
            //     'You have used up your 50 swipes for today, you need to wait until tomorrow.');
          }
          return Container();
        },
        error: (_, __) => const SizedBox(),
        loading: () => const SizedBox());
  }

  onDislike(UserInteractionModel interaction, bool fromButton) {
    final user = ref.read(userProfileFutureProvider);
    return user.when(
        data: (data) async {
          if (data != null) {
            if (totalInteraction < swipeCountLimit) {
              if (data.isNewUser == null) {
                showDialog(
                    barrierDismissible: false,
                    context: context,
                    builder: (context) {
                      return TutorialDialog(
                        onTapAction: () async {
                          data.isNewUser = false;

                          ref.read(userProfileNotifier).updateUserProfile(
                              data.copyWith(isNewUser: false));

                          _matchEngine.currentItem?.nope();
                          final newInteraction = interaction.copyWith(
                              isDislike: true, createdAt: DateTime.now());
                          await createInteraction(newInteraction);

                          if (_isInterstitialAdLoaded) {
                            //_interstitialAd?.show();
                            _isInterstitialAdLoaded = false;
                          }
                          fromButton
                              ? ""
                              : totalInteraction = totalInteraction + 1;
                          setState(() {});
                        },
                      );
                    });
              } else {
                _matchEngine.currentItem?.nope();
                final newInteraction = interaction.copyWith(
                    isDislike: true, createdAt: DateTime.now());
                await createInteraction(newInteraction);

                if (_isInterstitialAdLoaded) {
                  //_interstitialAd?.show();
                  _isInterstitialAdLoaded = false;
                }
                fromButton ? "" : totalInteraction = totalInteraction + 1;
                setState(() {});

                // if (totalInteraction >= 5) {
                //  EasyLoading.showInfo(
                //     'You have used up your 50 swipes for today, you need to wait until tomorrow.');
                //}
              }
            } else {
              // EasyLoading.showInfo(
              //     'You have used up your 50 swipes for today, you need to wait until tomorrow.');
            }
          }
        },
        error: (_, __) => const SizedBox(),
        loading: () => const SizedBox());
  }

  onLike(UserInteractionModel interaction, String myUserId,
      UserProfileModel mUser, bool fromButton,
      {int position = -1, bool isNotification = true}) {
    final user = ref.read(userProfileFutureProvider);
    user.when(
        data: (data) async {
          if (data != null) {
            if (totalInteraction < swipeCountLimit) {
              if (position != -1) {
                setState(() {
                  _swipeItems.removeAt(position);
                  listOfuser.removeAt(position);
                });
                EasyLoading.showToast("Like Successfully");
              }
              if (data.isNewUser == null && !widget.isGridView) {
                data.isNewUser = false;
                ref
                    .read(userProfileNotifier)
                    .updateUserProfile(data.copyWith(isNewUser: false));
                showDialog(
                    barrierDismissible: false,
                    context: context,
                    builder: (context) {
                      return TutorialDialog(onTapAction: () async {});
                    });
              }
              _matchEngine.currentItem?.like();
              final newInteraction =
                  interaction.copyWith(isLike: true, createdAt: DateTime.now());
              await createInteraction(newInteraction).then((result) async {
                if (result) {
                  await getExistingInteraction(mUser.id, myUserId)
                      .then((otherUserInteraction) {
                    if (otherUserInteraction != null) {
                      isMatch = true;
                      createInteraction(newInteraction.copyWith(isMatch: true));
                      createInteraction(
                          otherUserInteraction.copyWith(isMatch: true));
                      var otherUserFavList = mUser.favouriteIdList;
                      otherUserFavList?.remove(myUserId);
                      ref.read(userProfileNotifier).updateUserProfile(
                          mUser.copyWith(favouriteIdList: otherUserFavList));

                      var currentUserFavList = data.favouriteIdList;
                      currentUserFavList?.remove(mUser.id);
                      ref.read(userProfileNotifier).updateUserProfile(
                          data.copyWith(favouriteIdList: currentUserFavList));
                      // ref.invalidate(userProfileFutureProvider);
                      // ref.invalidate(otherUsersProvider);
                      // ref.invalidate(currentUserInteractionProvider);
                    } else {
                      if (isNotification) {
                        createInteractionNotification(
                            title: "You have a new Interaction!",
                            body: "${data.firstName} has liked you!",
                            userId: mUser.id,
                            currentUser: data,
                            notificationType: AppConstants.likeNotifacation);
                      }
                    }
                  });
                }
              });
              fromButton ? "" : totalInteraction = totalInteraction + 1;
              setState(() {});
              if (isMatch) {
                setState(() {
                  isMatch = false;
                });
                showMatchingDialog(
                  currentUser: data,
                  otherUser: mUser,
                );
              }
            } else {
              EasyLoading.showInfo(
                  'You have used up your 50 swipes for today, you need to wait until tomorrow.');
            }

            if (_isInterstitialAdLoaded) {
              //_interstitialAd?.show();
              _isInterstitialAdLoaded = false;
            }
          }
        },
        error: (_, __) => const SizedBox(),
        loading: () => const SizedBox());
  }

  favourated(UserInteractionModel interaction, String myUserId,
      UserProfileModel mUser, bool fromButton,
      {int position = -1, bool isNotification = true}) {
    final user = ref.read(userProfileFutureProvider);
    user.when(
        data: (data) async {
          if (data != null) {
            if (totalInteraction < swipeCountLimit) {
              if (position != -1) {
                setState(() {
                  _swipeItems.removeAt(position);
                  listOfuser.removeAt(position);
                });
              }
              if (data.isNewUser == null && !widget.isGridView) {
                data.isNewUser = false;
                ref
                    .read(userProfileNotifier)
                    .updateUserProfile(data.copyWith(isNewUser: false));
                showDialog(
                    barrierDismissible: false,
                    context: context,
                    builder: (context) {
                      return TutorialDialog(onTapAction: () async {});
                    });
              }
              final newInteraction =
                  interaction.copyWith(isLike: true, createdAt: DateTime.now());
              await createInteraction(newInteraction).then((result) async {
                if (result) {
                  await getExistingInteraction(mUser.id, myUserId)
                      .then((otherUserInteraction) {
                    if (otherUserInteraction != null) {
                      isMatch = true;
                      createInteraction(newInteraction.copyWith(isMatch: true));
                      createInteraction(
                          otherUserInteraction.copyWith(isMatch: true));
                      var otherUserFavList = mUser.favouriteIdList;
                      otherUserFavList?.remove(myUserId);
                      ref.read(userProfileNotifier).updateUserProfile(
                          mUser.copyWith(favouriteIdList: otherUserFavList));

                      var currentUserFavList = data.favouriteIdList;
                      currentUserFavList?.remove(mUser.id);
                      ref.read(userProfileNotifier).updateUserProfile(
                          data.copyWith(favouriteIdList: currentUserFavList));
                      // ref.invalidate(userProfileFutureProvider);
                      // ref.invalidate(otherUsersProvider);
                      // ref.invalidate(currentUserInteractionProvider);
                    } else {
                      createInteractionNotification(
                          title: "You have a new Interaction!",
                          body:
                              "${data.firstName} has added you to their favorite list",
                          userId: mUser.id,
                          currentUser: data,
                          notificationType: AppConstants.favoritedNotifacation);
                    }
                  });
                }
              });
              fromButton ? "" : totalInteraction = totalInteraction + 1;
              setState(() {});
              if (isMatch) {
                setState(() {
                  isMatch = false;
                });
                showMatchingDialog(
                  currentUser: data,
                  otherUser: mUser,
                );
              }
            } else {
              EasyLoading.showInfo(
                  'You have used up your 50 swipes for today, you need to wait until tomorrow.');
            }

            if (_isInterstitialAdLoaded) {
              //_interstitialAd?.show();
              _isInterstitialAdLoaded = false;
            }
          }
        },
        error: (_, __) => const SizedBox(),
        loading: () => const SizedBox());
  }

  @override
  Widget build(BuildContext context) {
    final currentUserProfile = ref.watch(userProfileFutureProvider);
    return currentUserProfile.when(
      data: (data) {
        if (data == null) {
          return const SizedBox();
        } else {
          return listOfuser.isEmpty
              ? const Center(child: NoItemFoundWidget(text: "No users found"))
              : widget.isGridView
                  ? GridView.builder(
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 2,
                              childAspectRatio:
                                  0.7 // Number of columns in the grid
                              ),
                      itemBuilder: (BuildContext context, int index) {
                        // Replace with your grid item widget
                        var userData =
                            _swipeItems[index].content as UserProfileModel;
                        return Container(
                          margin: const EdgeInsets.all(8.0),
                          decoration: const BoxDecoration(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10)),
                              color: Color.fromARGB(27, 0, 0, 0)),
                          child: ClipRRect(
                            borderRadius:
                                const BorderRadius.all(Radius.circular(8)),
                            child: Stack(
                              alignment: Alignment.bottomCenter,
                              children: [
                                Image.network(
                                  userData.profilePicture ?? "",
                                  height:
                                      MediaQuery.of(context).size.height / 1.5,
                                  width: MediaQuery.of(context).size.width,
                                  fit: BoxFit.cover,
                                  loadingBuilder:
                                      (context, child, loadingProgress) {
                                    if (loadingProgress == null) return child;
                                    return const Center(
                                        child: CircularProgressIndicator
                                            .adaptive());
                                  },
                                  errorBuilder: (context, error, stackTrace) {
                                    return const Center(
                                        child: Icon(CupertinoIcons.photo));
                                  },
                                ),
                                Positioned(
                                  top: 12,
                                  right: 12,
                                  child: SvgPicture.asset(
                                      userData.isDMOn ?? false
                                          ? AppConstants.dmOffIcon
                                          : AppConstants.dmOnIcon
                                      // height: 15,
                                      ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(
                                      bottom: 13, left: 12, right: 12),
                                  child: SizedBox(
                                    height: 75,
                                    child: Stack(
                                      alignment: Alignment.bottomCenter,
                                      children: [
                                        InkWell(
                                          onTap: () {
                                            Navigator.push(
                                                context,
                                                CupertinoPageRoute(
                                                    builder: (context) =>
                                                        UserDetailsPage(
                                                          user: userData,
                                                          totalInteraction:
                                                              totalInteraction,
                                                        )));
                                          },
                                          child: Container(
                                            height: 55,
                                            decoration: const BoxDecoration(
                                                borderRadius: BorderRadius.all(
                                                    Radius.circular(8)),
                                                color: Colors.black38),
                                            width: double.infinity,
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.fromLTRB(
                                                      8, 6, 8, 6),
                                              child: Column(
                                                children: [
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.start,
                                                    children: [
                                                      Flexible(
                                                        child: Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .only(
                                                                  right: 4),
                                                          child: Text(
                                                            "${userData.firstName}",
                                                            maxLines: 1,
                                                            overflow:
                                                                TextOverflow
                                                                    .ellipsis,
                                                            style: const TextStyle(
                                                                fontSize: 14,
                                                                color: Colors
                                                                    .white,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600),
                                                          ),
                                                        ),
                                                      ),
                                                      Text(
                                                        (DateTime.now()
                                                                    .difference(
                                                                        userData
                                                                            .birthDay!)
                                                                    .inDays ~/
                                                                365)
                                                            .toString(),
                                                        maxLines: 1,
                                                        style: const TextStyle(
                                                            fontSize: 14,
                                                            color: Colors.white,
                                                            fontWeight:
                                                                FontWeight
                                                                    .w400),
                                                      )
                                                    ],
                                                  ),
                                                  const SizedBox(
                                                    height: 4,
                                                  ),
                                                  Row(
                                                    children: [
                                                      Image.asset(
                                                        AppConstants
                                                            .locationIcon,
                                                        height: 18,
                                                        width: 18,
                                                      ),
                                                      const SizedBox(
                                                        width: 4,
                                                      ),
                                                      Flexible(
                                                        child: Text(
                                                          '${((Geolocator.distanceBetween(data.latitude ?? 0, data.longitude ?? 0, userData.latitude ?? 0, userData.longitude ?? 0) / 1000) * 0.621371).toStringAsFixed(2)} miles away',
                                                          maxLines: 1,
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                          style: const TextStyle(
                                                              fontSize: 12,
                                                              color:
                                                                  Colors.white,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w400),
                                                        ),
                                                      )
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                        Positioned(
                                          right: 4,
                                          top: 0,
                                          child: InkWell(
                                            onTap: () {
                                              indexNo = index;

                                              final String myUserId = ref
                                                  .watch(
                                                      currentUserStateProvider)!
                                                  .uid;
                                              final String id =
                                                  myUserId + userData.id;

                                              final UserInteractionModel
                                                  interaction =
                                                  UserInteractionModel(
                                                id: id,
                                                userId: myUserId,
                                                intractToUserId: userData.id,
                                                isSuperLike: false,
                                                isLike: false,
                                                isDislike: false,
                                                createdAt: DateTime.now(),
                                              );
                                              onLike(interaction, myUserId,
                                                  userData, true,
                                                  position: index);
                                            },
                                            child: Image.asset(
                                              AppConstants.newLikeIcon,
                                              width: 40,
                                              height: 40,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ),
                        );
                      },
                      itemCount:
                          _swipeItems.length, // Number of items in the grid
                    )
                  : Center(
                      child: SizedBox(
                        height: MediaQuery.of(context).size.height * 0.72,
                        width: MediaQuery.of(context).size.width * 0.95,
                        child: Stack(
                          fit: StackFit.passthrough,
                          children: [
                            Container(
                              // padding: const EdgeInsets.only(bottom: 50, top: 10, left: 10, right: 10),
                              // color: Colors.blue,

                              child: SwipeCards(
                                nopeTag: TagWidget(
                                  text: 'NOPE',
                                  fontSize: 30,
                                  color: Colors.red[400]!,
                                ),
                                likeTag: TagWidget(
                                  text: 'LIKE',
                                  fontSize: 30,
                                  color: Colors.green[400]!,
                                ),
                                upSwipeAllowed: false,
                                leftSwipeAllowed:
                                    totalInteraction < swipeCountLimit
                                        ? true
                                        : false,
                                rightSwipeAllowed:
                                    totalInteraction < swipeCountLimit
                                        ? true
                                        : false,
                                matchEngine: _matchEngine,
                                itemChanged: (p0, p1) {
                                  if (_isInterstitialAdLoaded) {
                                    //_interstitialAd?.show();
                                    _isInterstitialAdLoaded = false;
                                  }
                                },
                                onStackFinished: () {
                                  ref.invalidate(interactionFutureProvider);
                                },
                                itemBuilder: (context, index) {
                                  indexNo = index;
                                  final user = _swipeItems[index].content
                                      as UserProfileModel;

                                  final String myUserId =
                                      ref.watch(currentUserStateProvider)!.uid;
                                  final String id = myUserId + user.id;

                                  final UserInteractionModel interaction =
                                      UserInteractionModel(
                                    id: id,
                                    userId: myUserId,
                                    intractToUserId: user.id,
                                    isSuperLike: false,
                                    isLike: false,
                                    isDislike: false,
                                    createdAt: DateTime.now(),
                                  );

                                  return Consumer(
                                    builder: (context, ref, child) {
                                      final myProfile =
                                          ref.watch(userProfileFutureProvider);
                                      return myProfile.when(
                                          data: (data) {
                                            if (data != null) {
                                              return UserCardWidget(
                                                user:
                                                    _swipeItems[index].content,
                                                totalInteraction:
                                                    totalInteraction,
                                                matchEngine: _matchEngine,
                                                onTapBolt: () {
                                                  if (totalInteraction <
                                                      swipeCountLimit) {
                                                    if (data.favouriteIdList !=
                                                        null) {
                                                      if (data.favouriteIdList!
                                                          .contains(
                                                              user.userId)) {
                                                        EasyLoading.showToast(
                                                            "${user.firstName} is already added in your Favorites");
                                                      } else {
                                                        data.favouriteIdList
                                                            ?.add(user.userId);
                                                        ref
                                                            .read(
                                                                userProfileNotifier)
                                                            .updateUserProfile(
                                                                data.copyWith(
                                                                    favouriteIdList:
                                                                        data.favouriteIdList));
                                                        final newInteraction =
                                                            interaction.copyWith(
                                                                isLike: true,
                                                                createdAt:
                                                                    DateTime
                                                                        .now());
                                                        createInteraction(
                                                            newInteraction);

                                                        // setState(() {
                                                        //   _matchEngine.currentItem!
                                                        //       .like();
                                                        // });

                                                        if ((user.isPremiumUser ??
                                                                false) ||
                                                            (user.isPremiumPlusUser ??
                                                                false)) {
                                                          // createInteractionNotification(
                                                          //     title:
                                                          //         "You have a new Interaction!",
                                                          //     body:
                                                          //         "${data.fullName} has added you to their favorite list",
                                                          //     userId:
                                                          //         user.id,
                                                          //     currentUser:
                                                          //         data,notificationType: AppConstants.favoritedNotifacation);
                                                          // favourated(
                                                          //   interaction,
                                                          //   myUserId,
                                                          //   user,
                                                          //   false,
                                                          //   isNotification:
                                                          //       false,
                                                          // );
                                                          _matchEngine
                                                              .currentItem
                                                              ?.superLike();
                                                        } else {
                                                          // favourated(
                                                          //   interaction,
                                                          //   myUserId,
                                                          //   user,
                                                          //   false,

                                                          // );
                                                          _matchEngine
                                                              .currentItem
                                                              ?.superLike();
                                                        }

                                                        EasyLoading.showToast(
                                                            "${user.firstName} is added in your Favorites");
                                                      }
                                                    } else {
                                                      List<String> favList = [];
                                                      favList.add(user.userId);
                                                      data.favouriteIdList =
                                                          favList;
                                                      ref
                                                          .read(
                                                              userProfileNotifier)
                                                          .updateUserProfile(
                                                              data.copyWith(
                                                                  favouriteIdList:
                                                                      data.favouriteIdList));

                                                      final newInteraction =
                                                          interaction.copyWith(
                                                              isLike: true,
                                                              createdAt:
                                                                  DateTime
                                                                      .now());
                                                      createInteraction(
                                                          newInteraction);

                                                      if ((user.isPremiumUser ??
                                                              false) ||
                                                          (user.isPremiumPlusUser ??
                                                              false)) {
                                                        // createInteractionNotification(
                                                        //     title:
                                                        //         "You have a new Interaction!",
                                                        //     body:
                                                        //         "${data.fullName} has added you to their favorite list",
                                                        //     userId: user.id,
                                                        //     currentUser:
                                                        //         data,notificationType: AppConstants.favoritedNotifacation);
                                                        // favourated(
                                                        //     interaction,
                                                        //     myUserId,
                                                        //     user,
                                                        //     false,
                                                        //     isNotification:
                                                        //         false,
                                                        //     position:
                                                        //         indexNo);
                                                        _matchEngine.currentItem
                                                            ?.superLike();
                                                      } else {
                                                        // favourated(
                                                        //     interaction,
                                                        //     myUserId,
                                                        //     user,
                                                        //     false,
                                                        //     position:
                                                        //         indexNo);
                                                        _matchEngine.currentItem
                                                            ?.superLike();
                                                      }

                                                      EasyLoading.showToast(
                                                          "${user.firstName} is added in your Favorites");
                                                    }
                                                  }
                                                },
                                                onTapCross: () {
                                                  _matchEngine.currentItem!
                                                      .nope();
                                                  // onDislike(interaction, true);
                                                },
                                                onTapHeart: () {
                                                  _matchEngine.currentItem!
                                                      .like();
                                                },
                                              );
                                            } else {
                                              return const SizedBox();
                                            }
                                          },
                                          error: (_, __) => const SizedBox(),
                                          loading: () => const SizedBox());
                                    },
                                  );
                                },
                              ),
                            ),
                            Positioned(
                                bottom: 0,
                                left: 0,
                                right: 0,
                                child: Visibility(
                                  visible: totalInteraction >= swipeCountLimit
                                      ? true
                                      : false,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.red,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    width: double.infinity,
                                    padding: const EdgeInsets.all(15),
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.info,
                                          color: Colors.white,
                                        ),
                                        SizedBox(
                                          width: 20,
                                        ),
                                        Text(
                                          "You have used up your 50 swipes for today,\nyou need to wait until tomorrow.",
                                          style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 12,
                                              fontWeight: FontWeight.w700),
                                        ),
                                      ],
                                    ),
                                  ),
                                ))
                          ],
                        ),
                      ),
                    );
        }
      },
      error: (_, __) => const SizedBox(),
      loading: () => const SizedBox(),
    );
  }
}

class UserCirlePicture extends StatelessWidget {
  final String? imageUrl;
  final double? size;
  const UserCirlePicture({
    super.key,
    required this.imageUrl,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    final newSize = size ?? AppConstants.defaultNumericValue * 5;
    return Container(
      width: newSize,
      height: newSize,
      decoration: BoxDecoration(
        borderRadius:
            BorderRadius.circular(AppConstants.defaultNumericValue * 10),
        border: Border.all(color: const Color(0xFF019393), width: 2),
      ),
      child: ClipRRect(
        borderRadius:
            BorderRadius.circular(AppConstants.defaultNumericValue * 10),
        child: imageUrl == null || imageUrl!.isEmpty
            ? CircleAvatar(
                backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                child: Icon(
                  CupertinoIcons.person_fill,
                  color: AppConstants.primaryColor,
                  size: newSize * 0.8,
                ),
              )
            : CachedNetworkImage(
                imageUrl: imageUrl!,
                placeholder: (context, url) =>
                    const Center(child: CircularProgressIndicator.adaptive()),
                errorWidget: (context, url, error) =>
                    const Center(child: Icon(Icons.error)),
                fit: BoxFit.cover,
              ),
      ),
    );
  }
}

class HomePageNoUsersFoundWidget extends ConsumerWidget {
  const HomePageNoUsersFoundWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final interactions = ref.watch(interactionFutureProvider);
    final closestUsers = ref.watch(closestUsersProvider);

    return interactions.when(
      data: (data) {
        final users = closestUsers
            .where((element) => !data.any((interaction) =>
                interaction.intractToUserId == element.user.id))
            .toList();

        users.sort((a, b) => a.distance.compareTo(b.distance));

        return Center(
          child: Padding(
            padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.defaultNumericValue * 2),
            child: users.isEmpty
                ? const NoItemFoundWidget(
                    text: "No users found with your preferences")
                : AccountSettingsLandingWidget(
                    builder: (data) {
                      return ChangeRadiusFromHomePageWidget(
                        closestUsersDistanceInKM: users.first.distance / 1000,
                        user: data,
                      );
                    },
                  ),
          ),
        );
      },
      error: (_, __) => const SizedBox(),
      loading: () => const Center(
        child: CircularProgressIndicator.adaptive(),
      ),
    );
  }
}

class ChangeRadiusFromHomePageWidget extends ConsumerStatefulWidget {
  final double closestUsersDistanceInKM;
  final UserProfileModel user;
  const ChangeRadiusFromHomePageWidget({
    super.key,
    required this.closestUsersDistanceInKM,
    required this.user,
  });

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _ChangeRadiusFromHomePageWidgetState();
}

class _ChangeRadiusFromHomePageWidgetState
    extends ConsumerState<ChangeRadiusFromHomePageWidget> {
  late double _distanceInKm;
  late bool _isWorldWide;
  late double _maxDistanceInKm;

  @override
  void initState() {
    _distanceInKm = widget.user.userAccountSettingsModel.distanceInKm ??
        AppConfig.initialMaximumDistanceInKM;
    _isWorldWide = widget.user.userAccountSettingsModel.distanceInKm == null;
    _maxDistanceInKm = AppConfig.initialMaximumDistanceInKM;

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const NoItemFoundWidget(
              text: "No users found in your area right now", isSmall: true),
          Text.rich(
            TextSpan(
              children: [
                const TextSpan(
                  text:
                      "But you can change your radius to find more users. There are lots of users are waiting for you in just ",
                ),
                TextSpan(
                  text:
                      "${widget.closestUsersDistanceInKM.toStringAsFixed(0)} MM",
                  style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                        color: AppConstants.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const TextSpan(text: " away!"),
              ],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultNumericValue),
          Padding(
            padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.defaultNumericValue),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Radius',
                    style: Theme.of(context)
                        .textTheme
                        .titleSmall!
                        .copyWith(fontWeight: FontWeight.bold),
                  ),
                ),
                if (!_isWorldWide)
                  Text(
                    '${_distanceInKm.toInt()} km',
                    style: Theme.of(context).textTheme.titleLarge!.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppConstants.primaryColor),
                  ),
              ],
            ),
          ),
          if (_isWorldWide)
            const SizedBox(height: AppConstants.defaultNumericValue / 2),
          if (!_isWorldWide)
            Slider(
              value: _distanceInKm,
              min: 1,
              max: _maxDistanceInKm,
              onChanged: (value) {
                setState(() {
                  _distanceInKm = value;
                });
              },
            ),
          Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius:
                  BorderRadius.circular(AppConstants.defaultNumericValue),
            ),
            borderOnForeground: true,
            child: CheckboxListTile(
              value: _isWorldWide,
              controlAffinity: ListTileControlAffinity.leading,
              onChanged: (value) {
                setState(() {
                  _isWorldWide = value!;
                  _distanceInKm = value
                      ? AppConfig.initialMaximumDistanceInKM
                      : widget.user.userAccountSettingsModel.distanceInKm ??
                          AppConfig.initialMaximumDistanceInKM;
                });
              },
              title: Text(
                "Anywhere",
                style: Theme.of(context)
                    .textTheme
                    .bodyLarge!
                    .copyWith(fontWeight: FontWeight.bold),
              ),
            ),
          ),
          const SizedBox(height: AppConstants.defaultNumericValue),
          CustomButton(
            onPressed: () async {
              final UserAccountSettingsModel newSettingsModel =
                  widget.user.userAccountSettingsModel.copyWith(
                distanceInKm:
                    _isWorldWide ? null : _distanceInKm.toInt().toDouble(),
              );

              final userProfileModel = widget.user
                  .copyWith(userAccountSettingsModel: newSettingsModel);
              CustomAppLoader.showCustomLoader("Updating...");

              await ref
                  .read(userProfileNotifier)
                  .updateUserProfile(userProfileModel)
                  .then((value) {
                ref.invalidate(userProfileFutureProvider);
                EasyLoading.dismiss();
              });
            },
            text: 'Apply',
          ),
        ],
      ),
    );
  }
}

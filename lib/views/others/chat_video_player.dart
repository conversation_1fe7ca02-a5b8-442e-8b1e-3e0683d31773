import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:fringle_app/helpers/constants.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:video_player/video_player.dart';
import 'package:flutter/material.dart';

class ChatVideoPlayerPage extends StatefulWidget {
  final String videoUrl;
  final bool isNetwork;
  final String userName;
  final String time;
  final String imageUrl;

  const ChatVideoPlayerPage(
      {super.key,
      required this.videoUrl,
      required this.isNetwork,
      required this.userName,
      required this.time,
      required this.imageUrl});

  @override
  State<ChatVideoPlayerPage> createState() => _ChatVideoPlayerPage();
}

class _ChatVideoPlayerPage extends State<ChatVideoPlayerPage> {
  late VideoPlayerController _controller;

  @override
  void initState() {
    super.initState();

    if (widget.isNetwork) {
      _controller = VideoPlayerController.network(widget.videoUrl)
        ..initialize().then((_) {
          setState(() {});
          _controller.play();
        });
    } else {
      _controller = VideoPlayerController.file(File(widget.videoUrl))
        ..initialize().then((_) {
          setState(() {});
          _controller.play();
        });
    }

    _controller.setLooping(true);
    _controller.addListener(() {
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            SizedBox(
              width: double.infinity,
              child: Row(
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: SvgPicture.asset(AppConstants.blackBackIcon),
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.userName,
                        style: const TextStyle(
                            fontSize: 12,
                            color: Colors.black,
                            fontWeight: FontWeight.w600),
                      ),
                      Text(
                        widget.time,
                        style: const TextStyle(
                            fontSize: 10,
                            color: Color(0xFF737373),
                            fontWeight: FontWeight.w400),
                      )
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 15,
            ),
            Visibility(
                visible: (widget.imageUrl).isNotEmpty,
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 40),
                  child: ClipRRect(
                    borderRadius: const BorderRadius.all(Radius.circular(16)),
                    child: SizedBox(
                      height: 450,
                      width: double.infinity,
                      child: CachedNetworkImage(
                        imageUrl:
                            widget.imageUrl, // Replace with your image URL
                        fit: BoxFit.cover,
                        progressIndicatorBuilder:
                            (context, url, downloadProgress) {
                          // Show a loading indicator while the image is loading.
                          return Center(
                            child: CircularProgressIndicator(
                              value: downloadProgress.progress,
                            ),
                          );
                        },
                        errorWidget: (context, url, error) {
                          // Show an error icon if there's an issue loading the image.
                          return const Center(
                            child: Icon(CupertinoIcons.photo),
                          );
                        },
                      ),
                    ),
                  ),
                )),
            InkWell(
              onTap: () {
                setState(() {
                  _controller.value.isPlaying
                      ? _controller.pause()
                      : _controller.play();
                });
              },
              child: Visibility(
                visible: (widget.videoUrl).isNotEmpty,
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 8, horizontal: 60),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          SizedBox(
                              height: 460,
                              width: double.infinity,
                              child: ClipRRect(
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(16)),
                                  child: VideoPlayer(_controller))),
                          Visibility(
                              visible: !(_controller.value.isPlaying),
                              child:
                                  SvgPicture.asset(AppConstants.videoPlayIcon))
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 8),
                      child: Row(
                        children: [
                          Text(
                            _controller.value.position.inHours > 0
                                ? "${_controller.value.position.inHours.toString().padLeft(2, '0')}:${_controller.value.position.inMinutes.remainder(60).toString().padLeft(2, '0')}:${(_controller.value.position.inSeconds.remainder(60).toString().padLeft(2, '0'))}"
                                : "${_controller.value.position.inMinutes.remainder(60).toString().padLeft(2, '0')}:${(_controller.value.position.inSeconds.remainder(60).toString().padLeft(2, '0'))}",
                            style: const TextStyle(fontSize: 12),
                          ),
                          const SizedBox(
                            width: 12,
                          ),
                          Flexible(
                            child: SizedBox(
                              height: 8,
                              child: VideoProgressIndicator(
                                _controller,
                                allowScrubbing: true,
                                colors: VideoProgressColors(
                                  playedColor: Colors.blueGrey,
                                  bufferedColor: Colors.grey.shade400,
                                  backgroundColor: Colors.grey,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(
                            width: 12,
                          ),
                          Text(
                            _controller.value.position.inHours > 0
                                ? "${_controller.value.duration.inHours.toString().padLeft(2, '0')}:${_controller.value.duration.inMinutes.remainder(60).toString().padLeft(2, '0')}:${(_controller.value.duration.inSeconds.remainder(60).toString().padLeft(2, '0'))}"
                                : "${_controller.value.duration.inMinutes.remainder(60).toString().padLeft(2, '0')}:${(_controller.value.duration.inSeconds.remainder(60).toString().padLeft(2, '0'))}",
                            style: const TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();
  }
}

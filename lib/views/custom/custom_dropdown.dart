import 'package:fringle_app/helpers/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CustomDropdown extends StatelessWidget {
  final List<String> items;
  final String? selectedValue;
  final ValueChanged<String> onChanged;
  final String labelText;
  final String hintText;
  final FormFieldValidator<String>? validator;

  const CustomDropdown({
    super.key,
    required this.items,
    required this.selectedValue,
    required this.onChanged,
    required this.labelText,
    required this.hintText,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    final primaryColor = Theme.of(context).primaryColor;

    return DropdownButtonFormField<String>(
      icon: SvgPicture.asset(
        AppConstants.polygon,
        // height: 15,
      ),
      value: selectedValue,
      onChanged: (v) {
        onChanged(v ?? "");
      },
      decoration: InputDecoration(
        floatingLabelBehavior: FloatingLabelBehavior.always,
        labelText: labelText,
        hintText: hintText,
        labelStyle: const TextStyle(
          color: Color(0xff000000),
          fontSize: 12,
          fontWeight: FontWeight.w400,
        ),
        hintStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Colors.black,
        ),
        alignLabelWithHint: true,
        helperText: '',
        enabledBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: Color(0xffA1A1A1)),
        ),
        focusedBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: Color(0xffA1A1A1)),
        ),
      ),
      validator: validator,
      items: items.map((String item) {
        return DropdownMenuItem<String>(
          value: item,
          child: Text(
            item,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black,
            ),
          ),
        );
      }).toList(),
    );
  }
}

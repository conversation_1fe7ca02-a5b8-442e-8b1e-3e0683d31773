<svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="56" height="56" fill="#1E1E1E"/>
<path d="M-2420 -5206C-2420 -5207.1 -2419.1 -5208 -2418 -5208H6413C6414.1 -5208 6415 -5207.1 6415 -5206V17026C6415 17027.1 6414.1 17028 6413 17028H-2418C-2419.1 17028 -2420 17027.1 -2420 17026V-5206Z" fill="#444444"/>
<path d="M-2418 -5208V-5196H6413V-5208V-5220H-2418V-5208ZM6415 -5206H6403V17026H6415H6427V-5206H6415ZM6413 17028V17016H-2418V17028V17040H6413V17028ZM-2420 17026H-2408V-5206H-2420H-2432V17026H-2420ZM-2418 17028V17016C-2412.48 17016 -2408 17020.5 -2408 17026H-2420H-2432C-2432 17033.7 -2425.73 17040 -2418 17040V17028ZM6415 17026H6403C6403 17020.5 6407.48 17016 6413 17016V17028V17040C6420.73 17040 6427 17033.7 6427 17026H6415ZM6413 -5208V-5196C6407.48 -5196 6403 -5200.48 6403 -5206H6415H6427C6427 -5213.73 6420.73 -5220 6413 -5220V-5208ZM-2418 -5208V-5220C-2425.73 -5220 -2432 -5213.73 -2432 -5206H-2420H-2408C-2408 -5200.48 -2412.48 -5196 -2418 -5196V-5208Z" fill="white"/>
<path d="M-1183 -429C-1183 -430.105 -1182.1 -431 -1181 -431H1340C1341.1 -431 1342 -430.105 1342 -429V595C1342 596.105 1341.1 597 1340 597H-1181C-1182.1 597 -1183 596.105 -1183 595V-429Z" fill="#333333"/>
<path d="M-1181 -431V-430H1340V-431V-432H-1181V-431ZM1342 -429H1341V595H1342H1343V-429H1342ZM1340 597V596H-1181V597V598H1340V597ZM-1183 595H-1182V-429H-1183H-1184V595H-1183ZM-1181 597V596C-1181.55 596 -1182 595.552 -1182 595H-1183H-1184C-1184 596.657 -1182.66 598 -1181 598V597ZM1342 595H1341C1341 595.552 1340.55 596 1340 596V597V598C1341.66 598 1343 596.657 1343 595H1342ZM1340 -431V-430C1340.55 -430 1341 -429.552 1341 -429H1342H1343C1343 -430.657 1341.66 -432 1340 -432V-431ZM-1181 -431V-432C-1182.66 -432 -1184 -430.657 -1184 -429H-1183H-1182C-1182 -429.552 -1181.55 -430 -1181 -430V-431Z" fill="white" fill-opacity="0.1"/>
<g clip-path="url(#clip0_0_1)">
<rect width="375" height="812" transform="translate(-159 -322)" fill="white"/>
<rect x="-20" y="-20" width="96" height="96" rx="8" fill="url(#paint0_linear_0_1)" fill-opacity="0.14"/>
<g filter="url(#filter0_d_0_1)">
<path d="M46.667 9.3335H9.33366C6.76699 9.3335 4.69033 11.4335 4.69033 14.0002L4.66699 42.0002C4.66699 44.5668 6.76699 46.6668 9.33366 46.6668H46.667C49.2337 46.6668 51.3337 44.5668 51.3337 42.0002V14.0002C51.3337 11.4335 49.2337 9.3335 46.667 9.3335ZM46.667 18.6668L28.0003 30.3335L9.33366 18.6668V14.0002L28.0003 25.6668L46.667 14.0002V18.6668Z" fill="url(#paint1_linear_0_1)"/>
</g>
</g>
<defs>
<filter id="filter0_d_0_1" x="0.666992" y="9.3335" width="54.667" height="45.3335" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_0_1"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_0_1" result="shape"/>
</filter>
<linearGradient id="paint0_linear_0_1" x1="-12.32" y1="-29.5796" x2="28.0701" y2="101.381" gradientUnits="userSpaceOnUse">
<stop stop-color="#2EBFF1"/>
<stop offset="1" stop-color="#005BB9"/>
</linearGradient>
<linearGradient id="paint1_linear_0_1" x1="8.40033" y1="5.60809" x2="21.3717" y2="58.1811" gradientUnits="userSpaceOnUse">
<stop stop-color="#2EBFF1"/>
<stop offset="1" stop-color="#005BB9"/>
</linearGradient>
<clipPath id="clip0_0_1">
<rect width="375" height="812" fill="white" transform="translate(-159 -322)"/>
</clipPath>
</defs>
</svg>

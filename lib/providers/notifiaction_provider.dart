import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/models/notification_model.dart';
import 'package:fringle_app/providers/auth_providers.dart';
import 'package:http/http.dart' as http;

final notificationsStreamProvider =
    StreamProvider<List<NotificationModel>>((ref) {
  const notificationCollection = FirebaseConstants.notificationsCollection;

  final currentUserId = ref.watch(currentUserStateProvider)!.uid;

  return FirebaseFirestore.instance
      .collection(notificationCollection)
      .where("receiverId", isEqualTo: currentUserId)
      //.orderBy("createdAt", descending: true)
      .snapshots()
      .map((snapshot) {
    return snapshot.docs.reversed
        .map((doc) => NotificationModel.fromMap(doc.data()))
        .toList();
  });
});

const _notificationCollection = FirebaseConstants.notificationsCollection;

Future<bool> addNotification(NotificationModel notificationModel) async {
  try {
    await FirebaseFirestore.instance
        .collection(_notificationCollection)
        .doc(notificationModel.id)
        .set(notificationModel.toMap());
    var notificationType = notificationModel.notificationType ==
            AppConstants.favoritedNotifacation
        ? "fav"
        : notificationModel.notificationType == AppConstants.likeNotifacation
            ? "like"
            : notificationModel.notificationType ==
                    AppConstants.matchNotifacation
                ? "match"
                : "message";
    sendPushNotification(
        notificationModel.title,
        notificationModel.body,
        notificationModel.receiverId,
        notificationType,
        notificationModel.userId ?? "",
        notificationModel.matchId ?? "");
    return true;
  } catch (e) {
    return false;
  }
}

// Function to send an FCM notification
Future<void> sendPushNotification(String title, String body, String userId,
    String notificationType, String senderId, String matchId) async {
  CollectionReference deviceTokensCollection = FirebaseFirestore.instance
      .collection(FirebaseConstants.deviceTokensCollection);

  QuerySnapshot qsUserId =
      await deviceTokensCollection.where('userId', isEqualTo: userId).get();

  for (QueryDocumentSnapshot data in qsUserId.docs) {
    var deviceToken =
        data.get("deviceToken").isNotEmpty ? data.get("deviceToken") : "";
    if (deviceToken != "") {
      final Map<String, dynamic> notificationData = {
        'notification': {'title': title, 'body': body, 'sound': 'default'},
        'data': {
          'type': notificationType,
          'userId': senderId,
          'matchId': matchId
        },
        'to': deviceToken, // Send to a topic or specify a device's FCM token
      };

      final headers = <String, String>{
        'Content-Type': 'application/json',
        'Authorization': 'key=${FirebaseConstants.firebaseServerKey}',
      };

      try {
        await http.post(
          Uri.parse('https://fcm.googleapis.com/fcm/send'),
          headers: headers,
          body: jsonEncode(notificationData),
        );
      } catch (e) {
        print('Error sending notification: $e');
      }
    }
  }
}

//Update notification
Future<bool> updateNotification(NotificationModel notificationModel) async {
  try {
    await FirebaseFirestore.instance
        .collection(_notificationCollection)
        .doc(notificationModel.id)
        .update(notificationModel.toMap());

    return true;
  } catch (e) {
    return false;
  }
}

// Mark All As Read Notification
Future<bool> markAllAsRead(String currentUserId) async {
  try {
    await FirebaseFirestore.instance
        .collection(_notificationCollection)
        .where("receiverId", isEqualTo: currentUserId)
        .get()
        .then((snapshot) async {
      for (var doc in snapshot.docs) {
        final notificationModel = NotificationModel.fromMap(doc.data());
        if (notificationModel.isRead == false) {
          notificationModel.isRead = true;
          await updateNotification(notificationModel);
        }
      }
    });

    return true;
  } catch (e) {
    return false;
  }
}

Future<bool> deleteNotification(String notificationId) async {
  try {
    await FirebaseFirestore.instance
        .collection(_notificationCollection)
        .doc(notificationId)
        .delete();

    return true;
  } catch (e) {
    return false;
  }
}

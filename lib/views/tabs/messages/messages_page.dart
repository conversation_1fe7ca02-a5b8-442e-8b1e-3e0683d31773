import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
//import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:fringle_app/config/config.dart';
import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/helpers/date_formater.dart';
import 'package:fringle_app/helpers/encrypt_helper.dart';
import 'package:fringle_app/models/chat_item_model.dart';
import 'package:fringle_app/models/match_model.dart';
import 'package:fringle_app/models/user_profile_model.dart';
import 'package:fringle_app/providers/auth_providers.dart';
import 'package:fringle_app/providers/chat_provider.dart';
import 'package:fringle_app/providers/match_provider.dart';
import 'package:fringle_app/providers/other_users_provider.dart';
import 'package:fringle_app/views/custom/custom_app_bar.dart';
import 'package:fringle_app/views/custom/lottie/no_item_found_widget.dart';
import 'package:fringle_app/views/others/error_page.dart';
import 'package:fringle_app/views/others/loading_page.dart';
import 'package:fringle_app/views/others/user_card_widget.dart';
import 'package:fringle_app/views/tabs/home/<USER>';
import 'package:fringle_app/views/tabs/messages/components/chat_page.dart';
import 'package:flutter_svg/svg.dart';
import '../../../models/notification_model.dart';
import '../home/<USER>';

class MessageConsumerPage extends ConsumerWidget {
  final Function(NotificationModel notificationData)? onTapChange;
  const MessageConsumerPage({super.key, this.onTapChange});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final matches = ref.watch(matchStreamProvider);
    //final bool onlineStatus = false;

    return matches.when(
      data: (data) {
        final List<MessageViewModel> messages = [];

        messages.addAll(getAllMessages(ref, data));

        // return MessagesPage(messages: messages);
        return MessagesPage(
          messages: messages,
          isPremiumUser: true,
          isPremiumPlusUser: true,
          onTapChange: onTapChange,
        );
      },
      error: (_, __) => const ErrorPage(),
      loading: () => const LoadingPage(),
    );
  }
}

class MessagesPage extends ConsumerStatefulWidget {
  final List<MessageViewModel> messages;
  final bool isPremiumUser;
  final bool isPremiumPlusUser;
  final Function(NotificationModel notificationData)? onTapChange;
  const MessagesPage({
    super.key,
    required this.messages,
    required this.isPremiumUser,
    required this.isPremiumPlusUser,
    this.onTapChange,
  });

  @override
  ConsumerState<MessagesPage> createState() => _MessagesPageState();
}

class _MessagesPageState extends ConsumerState<MessagesPage> {
  bool _isSearchBarVisible = false;
  final _searchController = TextEditingController();

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.invalidate(otherUsersProvider);
      ref.watch(otherUsersProvider);
    });

    if (!(widget.isPremiumUser || widget.isPremiumPlusUser) &&
        isAdmobAvailable) {
      //InterstitialAd.load(
      //  adUnitId: Platform.isAndroid
      //      ? AndroidAdUnits.interstitialId
      //      : IOSAdUnits.interstitialId,
      //  request: const AdRequest(),
      //  adLoadCallback: InterstitialAdLoadCallback(
      //    onAdLoaded: (ad) async {
      //      debugPrint('InterstitialAd loaded.');

      //      await Future.delayed(const Duration(seconds: 4)).then((value) {
      //        ad.show();
      //      });
      //    },
      //    onAdFailedToLoad: (error) {},
      //  ),
      //);
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final searchedMessages = widget.messages.where((element) {
      return element.matchedUser.firstName!
          .toLowerCase()
          .contains(_searchController.text.toLowerCase()) ||
          element.matchedUser.lastName!
              .toLowerCase()
              .contains(_searchController.text.toLowerCase());
    }).toList();

    searchedMessages.sort((a, b) {
      return b.lastMessageDate.compareTo(a.lastMessageDate);
    });

    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 0,
        backgroundColor: const Color(0xFFFFFCF5),
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      body: Container(
        color: const Color(0xFFFFFCF5),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const SizedBox(height: AppConstants.defaultNumericValue),
            Padding(
              padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.defaultNumericValue),
              child: CustomAppBar(
                leading: Padding(
                  padding: const EdgeInsets.all(
                      AppConstants.defaultNumericValue / 1.5),
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        _isSearchBarVisible = !_isSearchBarVisible;
                        _searchController.clear();
                      });
                    },
                    child: SvgPicture.asset(AppConstants.search),
                  ),
                ),
                title: const Center(
                  child: Text(
                    'Messages',
                    style: TextStyle(
                        fontFamily: "assets/fonts/Poppins-Bold",
                        fontSize: 18,
                        fontWeight: FontWeight.w600),
                  ),
                ),
                trailing: InkWell(
                  child: const NotificationButton(),
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => NotificationPage(
                            notificationData: widget.onTapChange,
                          ),
                        ));
                  },
                ),
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: AppConstants.defaultNumericValue),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: AppConstants.defaultNumericValue),
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      transitionBuilder:
                          (Widget child, Animation<double> animation) {
                        return SizeTransition(
                            sizeFactor: animation, child: child);
                      },
                      child: _isSearchBarVisible
                          ? Container(
                              key: const Key('searchBar'),
                              padding: const EdgeInsets.all(
                                  AppConstants.defaultNumericValue / 3),
                              decoration: BoxDecoration(
                                color:
                                    AppConstants.primaryColor.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(
                                  AppConstants.defaultNumericValue,
                                ),
                              ),
                              child: TextField(
                                controller: _searchController,
                                autofocus: true,
                                onChanged: (_) {
                                  setState(() {});
                                },
                                decoration: InputDecoration(
                                  hintText: 'Search here...',
                                  border: InputBorder.none,
                                  prefixIcon: Icon(
                                    CupertinoIcons.search,
                                    color: AppConstants.primaryColor,
                                  ),
                                ),
                              ),
                            )
                          : const SizedBox(key: Key('noSearchBar')),
                    ),
                  ),
                  _isSearchBarVisible
                      ? const SizedBox(height: AppConstants.defaultNumericValue)
                      : const SizedBox(height: 0),
                  Expanded(
                    child: searchedMessages.isEmpty
                        ? const Center(
                            child: NoItemFoundWidget(text: 'No messages found'),
                          )
                        : ListView.builder(
                            itemCount: searchedMessages.length,
                            itemBuilder: (context, index) {
                              final message = searchedMessages[index];
                              return ConversationTile(
                                messageViewModel: message,
                                isPremiumUser: widget.isPremiumUser,
                                isPremiumPlusUser: widget.isPremiumPlusUser,
                              );
                            },
                          ),
                  ),
                  const SizedBox(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ConversationTile extends ConsumerStatefulWidget {
  final MessageViewModel messageViewModel;

  bool onlineStatus;
  final bool isPremiumUser;
  final bool isPremiumPlusUser;

  ConversationTile({
    super.key,
    required this.messageViewModel,
    required this.isPremiumUser,
    required this.isPremiumPlusUser,
    this.onlineStatus = false,
  });

  @override
  ConsumerState<ConversationTile> createState() => _ConversationTileState();
}

class _ConversationTileState extends ConsumerState<ConversationTile> {
  //bool onlineStatus =false;
  bool isPrimeUser = false;
  bool isPrimePlusUser = false;

  getOnlineStatus(String userId) async {
    CollectionReference userProfileCollection = FirebaseFirestore.instance
        .collection(FirebaseConstants.userProfileCollection);

    QuerySnapshot querySnapshotUserId =
        await userProfileCollection.where('userId', isEqualTo: userId).get();

    for (var document in querySnapshotUserId.docs) {
      Map<String, dynamic> data = document.data() as Map<String, dynamic>;

      //UserProfileModel userProfile = UserProfileModel(
      widget.onlineStatus = data['isOnline'];
      setState(() {});

      //);
    }
    //return onlineStatus;
  }

  @override
  void initState() {
    super.initState();
    // setSubscriptionUpdate();
  }

  // here we are checking that subscription is for current logged in user or not and set subscription variable true or false accordingly

  // void setSubscriptionUpdate() {
  //   final userRef = ref.read(userProfileFutureProvider);
  //   userRef.when(
  //       data: (value) => {
  //             if (value != null)
  //               {
  //                 ref.read(userProfileNotifier).isUserPremium().then((data) {
  //                   if (data == value.userId) {
  //                     if (widget.isPremiumUser && !widget.isPremiumPlusUser) {
  //                       var preferences = value.preferences;
  //                       if (preferences != null) {
  //                         preferences.remove("contentCreator");
  //                       }
  //                       ref.read(userProfileNotifier).updateUserProfile(
  //                           value.copyWith(
  //                               isPremiumUser: true, isPremiumPlusUser: false));
  //                       isPrimeUser = true;
  //                       isPrimePlusUser = false;
  //                       // ref.read(userProfileNotifier).updateUserProfile(
  //                       //     value.copyWith(
  //                       //         isPremiumPlusUser: false,
  //                       //         preferences: preferences));
  //                     } else if (!widget.isPremiumUser &&
  //                         widget.isPremiumPlusUser) {
  //                       ref.read(userProfileNotifier).updateUserProfile(
  //                           value.copyWith(
  //                               isPremiumPlusUser: true, isPremiumUser: false));
  //                       isPrimeUser = false;
  //                       isPrimePlusUser = true;
  //                     } else if (widget.isPremiumPlusUser &&
  //                         widget.isPremiumUser) {
  //                       ref.read(userProfileNotifier).updateUserProfile(
  //                           value.copyWith(
  //                               isPremiumPlusUser: true, isPremiumUser: false));

  //                       isPrimeUser = false;
  //                       isPrimePlusUser = true;
  //                     }
  //                     ref.invalidate(userProfileFutureProvider);

  //                     setState(() {});
  //                   } else {
  //                     var preferences = value.preferences;
  //                     if (preferences != null) {
  //                       preferences.remove("kink");
  //                       preferences.remove("contentCreator");
  //                       if (preferences["seeking"] == "Couple") {
  //                         preferences.remove("seeking");
  //                       }
  //                     }
  //                     ref.read(userProfileNotifier).updateUserProfile(
  //                         value.copyWith(
  //                             isPremiumUser: false,
  //                             isPremiumPlusUser: false,
  //                             isFeaturedOn: false,
  //                             planType: -1,
  //                             isDMOn: false,
  //                             preferences: preferences));
  //                     ref.invalidate(userProfileFutureProvider);
  //                     isPrimeUser = value.isPremiumUser ?? false;
  //                     isPrimePlusUser = value.isPremiumPlusUser ?? false;
  //                     if (!(isPrimeUser || isPrimePlusUser)) {
  //                       if (widget.messageViewModel.isIntroVideo) {
  //                         if (widget.messageViewModel.senderUserId !=
  //                             value.userId) {
  //                           if ((widget.messageViewModel.matchedUser
  //                                       .isPremiumUser ==
  //                                   true) ||
  //                               (widget.messageViewModel.matchedUser
  //                                       .isPremiumPlusUser ==
  //                                   true)) {
  //                             isPrimeUser = false;
  //                             isPrimePlusUser = false;
  //                           } else {
  //                             isPrimeUser = true;
  //                             isPrimePlusUser = true;
  //                           }
  //                         } else {
  //                           isPrimeUser = true;
  //                           isPrimePlusUser = true;
  //                         }
  //                       } else {
  //                         isPrimeUser = true;
  //                         isPrimePlusUser = true;
  //                       }
  //                     }
  //                     setState(() {});
  //                   }
  //                 })
  //               }
  //           },
  //       error: (_, __) => const SizedBox(),
  //       loading: () => const SizedBox());
  //   final user = ref.read(userProfileFutureProvider);
  //   user.when(
  //     data: (data) {
  //       if (data != null) {}
  //     },
  //     error: (error, stackTrace) {},
  //     loading: () {},
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    //bool isOnlineStatus = getOnlineStatus(messageViewModel.matchedUser.userId);
    //getOnlineStatus(widget.messageViewModel.matchedUser.userId);

    return Column(
      children: [
        Container(
          color: (isPrimeUser || isPrimePlusUser)
              ? const Color(0xFFFFFCF5)
              : widget.messageViewModel.isIntroVideo
                  ? const Color(0xffEBB423).withOpacity(0.2)
                  : const Color(0xFFFFFCF5),
          child: ListTile(
            onTap: () {
              // if (!(isPrimeUser || isPrimePlusUser)) {
              //   if (widget.messageViewModel.isIntroVideo) {
              //     Navigator.push(
              //       context,
              //       CupertinoPageRoute(
              //         builder: (context) => MembershipScreen(
              //           isPremiumUser: isPrimeUser,
              //           isPremiumPlusUser: isPrimePlusUser,
              //         ),
              //       ),
              //     ).then((value) {
              //       setSubscriptionUpdate();
              //       ref.invalidate(matchStreamProvider);
              //     });
              //   } else {
              //     Navigator.push(
              //       context,
              //       CupertinoPageRoute(
              //         builder: (context) => ChatPage(
              //           otherUserId: widget.messageViewModel.matchedUser.userId,
              //           matchId: widget.messageViewModel.matchId,
              //         ),
              //       ),
              //     );
              //   }
              // } else {
                Navigator.push(
                  context,
                  CupertinoPageRoute(
                    builder: (context) => ChatPage(
                      otherUserId: widget.messageViewModel.matchedUser.userId,
                      matchId: widget.messageViewModel.matchId,
                    ),
                  ),
                );
              // }
            },
            title: Container(
              margin: EdgeInsets.only(top: 10),
              child: Row(
                children: [
                  Flexible(
                    child: Text(
                      widget.messageViewModel.matchedUser.firstName ?? "",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: Theme.of(context)
                          .textTheme
                          .titleMedium!
                          .copyWith(fontWeight: FontWeight.bold),
                    ),
                  ),
                  if (widget.messageViewModel.unreadCount > 0)
                    const SizedBox(width: AppConstants.defaultNumericValue / 2),
                  if (widget.messageViewModel.unreadCount > 0)
                    Badge(
                      backgroundColor: Colors.red,
                      label:
                          Text(widget.messageViewModel.unreadCount.toString()),
                    ),
                  if (widget.messageViewModel.matchedUser.isOnline)
                    const SizedBox(width: AppConstants.defaultNumericValue / 2),
                  if (widget.messageViewModel.matchedUser.isOnline)
                    const OnlineStatus(),

                  // if (!widget.onlineStatus)
                  //   const SizedBox(width: AppConstants.defaultNumericValue / 2),
                  // if (!widget.onlineStatus) const Text("Offline")

                  // if (messageViewModel.matchedUser.isOnline) const OnlineStatus()
                ],
              ),
            ),
            trailing: Column(
              children: [
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      DateFormatter.toTime(
                          widget.messageViewModel.lastMessageDate),
                      style: Theme.of(context)
                          .textTheme
                          .bodySmall!
                          .copyWith(color: const Color(0xFF181818)),
                    ),
                    Text(
                      DateFormatter.toYearMonthDay2(
                          widget.messageViewModel.lastMessageDate),
                      style: Theme.of(context)
                          .textTheme
                          .bodySmall!
                          .copyWith(color: const Color(0xFF181818)),
                    ),
                  ],
                ),
              ],
            ),
            subtitle: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                //if (widget.isPremiumUser)
                // if (widget.messageViewModel.lastMessage.userId ==
                //     ref.watch(currentUserStateProvider)!.uid)
                //   const Text('You: '),
                //if (widget.isPremiumUser)
                if (widget.messageViewModel.lastMessage.image != null)
                  Icon(
                    Icons.image,
                    color: AppConstants.primaryColor,
                  ),
                //if (widget.isPremiumUser)
                if (widget.messageViewModel.lastMessage.image != null)
                  const SizedBox(width: AppConstants.defaultNumericValue / 2),
                //if (widget.isPremiumUser)
                if (widget.messageViewModel.lastMessage.video != null)
                  Icon(
                    Icons.movie,
                    color: AppConstants.primaryColor,
                  ),
                // if (widget.isPremiumUser)
                if (widget.messageViewModel.lastMessage.video != null)
                  const SizedBox(width: AppConstants.defaultNumericValue / 2),
                // if (widget.isPremiumUser)
                Flexible(
                  child: Text(
                    decryptText(
                        widget.messageViewModel.lastMessage.message ?? ""),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (!isPrimeUser)
                  if (widget.messageViewModel.isIntroVideo)
                    Container(
                      margin: const EdgeInsets.only(top: 6, bottom: 12),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 8),
                      decoration: BoxDecoration(
                        color: const Color(0xffFFD25F),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        children: [
                          SvgPicture.asset(
                            AppConstants.whiteStar,
                            height: 14,
                            width: 14,
                          ),
                          const SizedBox(width: 3),
                          Text(
                            'Upgrade To Chat',
                            style:
                                Theme.of(context).textTheme.bodySmall!.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 10,
                                    ),
                          ),
                        ],
                      ),
                    )
              ],
            ),
            leading: UserCirlePicture(
                imageUrl: widget.messageViewModel.matchedUser.profilePicture,
                size: AppConstants.defaultNumericValue * 3),
          ),
        ),
        const Divider(
          height: 2,
        ),
      ],
    );
  }
}

class MessageViewModel {
  UserProfileModel matchedUser;
  String matchId;
  ChatItemModel lastMessage;
  DateTime lastMessageDate;
  int unreadCount;
  bool isOnline;
  bool isIntroVideo;
  String senderUserId;
  String currentUserId;
  MessageViewModel(
      {required this.matchedUser,
      required this.matchId,
      required this.lastMessage,
      required this.lastMessageDate,
      required this.unreadCount,
      required this.isOnline,
      required this.isIntroVideo,
      required this.senderUserId,
      required this.currentUserId});

  MessageViewModel copyWith(
      {UserProfileModel? matchedUser,
      String? matchId,
      ChatItemModel? lastMessage,
      DateTime? lastMessageDate,
      int? unreadCount,
      bool? isOnline,
      bool? isIntroVideo,
      String? senderUserId,
      String? currentUserId}) {
    return MessageViewModel(
        matchedUser: matchedUser ?? this.matchedUser,
        matchId: matchId ?? this.matchId,
        lastMessage: lastMessage ?? this.lastMessage,
        lastMessageDate: lastMessageDate ?? this.lastMessageDate,
        unreadCount: unreadCount ?? this.unreadCount,
        isOnline: isOnline ?? this.isOnline,
        isIntroVideo: isIntroVideo ?? this.isIntroVideo,
        senderUserId: senderUserId ?? this.senderUserId,
        currentUserId: currentUserId ?? this.currentUserId);
  }

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'matchedUser': matchedUser.toMap()});
    result.addAll({'matchId': matchId});
    result.addAll({'lastMessage': lastMessage.toMap()});
    result.addAll({'lastMessageDate': lastMessageDate.millisecondsSinceEpoch});
    result.addAll({'unreadCount': unreadCount});
    result.addAll({'isOnline': isOnline});
    result.addAll({'isIntroVideo': isIntroVideo});
    result.addAll({'senderUserId': senderUserId});
    result.addAll({'currentUserId': currentUserId});

    return result;
  }

  factory MessageViewModel.fromMap(Map<String, dynamic> map) {
    return MessageViewModel(
      matchedUser: UserProfileModel.fromMap(map['matchedUser']),
      matchId: map['matchId'] ?? '',
      lastMessage: ChatItemModel.fromMap(map['lastMessage']),
      lastMessageDate:
          DateTime.fromMillisecondsSinceEpoch(map['lastMessageDate']),
      unreadCount: map['unreadCount']?.toInt() ?? 0,
      isOnline: map['isOnline'] as bool,
      isIntroVideo: map['isIntroVideo'] as bool,
      senderUserId: map['senderUserId'] as String,
      currentUserId: map['currentUserId'] as String,
    );
  }

  String toJson() => json.encode(toMap());

  factory MessageViewModel.fromJson(String source) =>
      MessageViewModel.fromMap(json.decode(source));

  @override
  String toString() {
    return 'MessageViewModel(matchedUser: $matchedUser, matchId: $matchId, lastMessage: $lastMessage, lastMessageDate: $lastMessageDate, unreadCount: $unreadCount, isOnline: $isOnline, isIntroVideo: $isIntroVideo, senderUserId : $senderUserId. currentUserId : $currentUserId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is MessageViewModel &&
        other.matchedUser == matchedUser &&
        other.matchId == matchId &&
        other.lastMessage == lastMessage &&
        other.lastMessageDate == lastMessageDate &&
        other.unreadCount == unreadCount &&
        other.isOnline == isOnline &&
        other.senderUserId == senderUserId &&
        other.currentUserId == currentUserId &&
        other.isIntroVideo == isIntroVideo;
  }

  @override
  int get hashCode {
    return matchedUser.hashCode ^
        matchId.hashCode ^
        lastMessage.hashCode ^
        lastMessageDate.hashCode ^
        unreadCount.hashCode ^
        isOnline.hashCode ^
        senderUserId.hashCode ^
        currentUserId.hashCode ^
        isIntroVideo.hashCode;
  }
}

List<MessageViewModel> getAllMessages(WidgetRef ref, List<MatchModel> data) {
  final otherUserIds = data.map((e) {
    return e.userIds.any(
            (element) => element != ref.watch(currentUserStateProvider)!.uid)
        ? e.userIds.firstWhere(
            (element) => element != ref.watch(currentUserStateProvider)!.uid)
        : null;
  }).toList();

  bool isOnlinex = false;

  final otherUsers = ref.watch(otherUsersProvider);
  List<UserProfileModel> matchedUsers = [];

  otherUsers.whenData((value) {
    matchedUsers = value.where((element) {
      return otherUserIds.contains(element.userId);
    }).toList();
  });

  List<MessageViewModel> messages = [];

  for (var match in data) {
    final chatProvider = ref.watch(chatStreamProviderProvider(match.id));
    chatProvider.whenData((value) {
      if (matchedUsers.isEmpty) {
        return;
      }
      final UserProfileModel otherUser = matchedUsers.firstWhere((element) =>
          element.userId ==
          match.userIds.firstWhere((element) =>
              element != ref.watch(currentUserStateProvider)!.uid));
      isOnlinex = otherUser.isOnline;
      //debugPrint("status ${otherUser.isOnline}  ${otherUser.fullName}");

      int unreadCount = 0;
      for (var message in value) {
        if (message.userId != ref.watch(currentUserStateProvider)!.uid) {
          if (message.isRead == false) {
            unreadCount++;
          }
        }
      }

      if (value.isNotEmpty) {
        MessageViewModel message = MessageViewModel(
            matchedUser: otherUser,
            lastMessage: value.first,
            lastMessageDate: value.first.createdAt,
            matchId: match.id,
            unreadCount: unreadCount,
            isOnline: isOnlinex,
            isIntroVideo: value.first.isIntroVideo,
            currentUserId: ref.watch(currentUserStateProvider)?.uid ?? "",
            senderUserId: value.first.userId ?? "");

        messages.add(message);
      }
    });
  }

  return messages;
}

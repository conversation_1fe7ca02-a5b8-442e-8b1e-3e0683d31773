import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../helpers/constants.dart';
import '../../models/custom_check_model.dart';

class ClipItemWidget extends StatelessWidget {
  final CustomCipsModel item;
  final ValueChanged<bool> onSelectionChanged;
  final double borderRadious;

  const ClipItemWidget(
      {super.key,
      required this.item,
      required this.onSelectionChanged,
      this.borderRadious = 12});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onSelectionChanged(!item.isSelected);
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(borderRadious),
          color: item.isSelected ? null : Colors.white,
          border: item.isSelected
              ? Border.all(
                  color: Colors.transparent,
                  width: 1,
                )
              : Border.all(
                  color: const Color(0xFFB5B4B4), // Border color
                  width: 1, // Border width
                ),
          gradient: item.isSelected
              ? LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: AppConstants.defaultGradient.colors,
                )
              : null,
        ),
        child: Container(
            padding: const EdgeInsets.fromLTRB(10, 8, 10, 8),
            child: Text(
              item.name,
              style: GoogleFonts.manrope(
                color: item.isSelected ? Colors.white : const Color(0xFF717171),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            )),
      ),
    );
  }
}

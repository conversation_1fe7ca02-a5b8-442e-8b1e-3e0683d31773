import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:fringle_app/models/user_profile_model_new.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/models/user_profile_model.dart';
import 'package:fringle_app/providers/auth_providers.dart';
// import 'package:purchases_flutter/purchases_flutter.dart';

final userProfileFutureProvider =
    FutureProvider<UserProfileModel?>((ref) async {
  final userCollection = FirebaseFirestore.instance
      .collection(FirebaseConstants.userProfileCollection);

  return userCollection
      .where("userId", isEqualTo: ref.watch(currentUserStateProvider)?.uid)
      .get()
      .then((data) {
    //print("objecthggsh  asghsgd");
    if (data.docs.isEmpty) {
      return null;
    } else {
      return UserProfileModel.fromMap(data.docs.first.data());
    }
  });
});

final userProfileNotifier = Provider<UserProfileNotifier>((ref) {
  return UserProfileNotifier();
});

class UserProfileNotifier {
  final _userCollection = FirebaseFirestore.instance
      .collection(FirebaseConstants.userProfileCollection);

  Future<bool> createProfile(String id, Map<String, dynamic> map) async {
    try {
      await _userCollection.doc(id).set(map);
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> createUserProfile(UserProfileModelNew userProfileModel) async {
    try {
      UserProfileModelNew? newUserProfile;

      if (userProfileModel.profilePicture != null) {
        if (Uri.parse(userProfileModel.profilePicture!).isAbsolute) {
          newUserProfile = userProfileModel;
        } else {
          final profileURL = await _uploadProfilePicture(
              userProfileModel.profilePicture!, userProfileModel.userId);
          newUserProfile =
              userProfileModel.copyWith(profilePicture: profileURL);
        }
      } else {
        newUserProfile = userProfileModel;
      }

      await _userCollection
          .doc(newUserProfile.id)
          .update((newUserProfile.toMap()));
      return true;
    } catch (e) {
      debugPrint(e.toString());
      return false;
    }
  }

  Future<bool> updateUserProfile(UserProfileModel userProfileModel) async {
    try {
      UserProfileModel newUserProfile = userProfileModel;

      if (userProfileModel.profilePicture != null) {
        if (Uri.parse(userProfileModel.profilePicture!).isAbsolute) {
          newUserProfile = userProfileModel;
        } else if (userProfileModel.profilePicture == "") {
          newUserProfile = userProfileModel.copyWith(profilePicture: "");
        } else {
          final profileURL = await _uploadProfilePicture(
              userProfileModel.profilePicture!, userProfileModel.userId);
          newUserProfile =
              userProfileModel.copyWith(profilePicture: profileURL);
        }
      }

      List<String> mediaURLs = [];
      // for (var media in userProfileModel.mediaFiles) {
      //   if (Uri.parse(media).isAbsolute) {
      //     mediaURLs.add(media);
      //   } else if (media == "") {
      //     debugPrint("Media is empty");
      //   } else {
      //     final mediaURL =
      //         await _uploadUserMediaFiles(media, userProfileModel.userId);
      //     if (mediaURL != null) {
      //       mediaURLs.add(mediaURL);
      //     }
      //   }
      // }

      final anotherNewUserProfile =
          newUserProfile.copyWith(mediaFiles: mediaURLs);

      await _userCollection
          .doc(anotherNewUserProfile.id)
          .update(anotherNewUserProfile.toMap());
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<String?> _uploadProfilePicture(String imagePath, String userId) async {
    final storageRef = FirebaseStorage.instance.ref();

    final imageRef = storageRef.child("user_profile_pictures/$userId");
    final uploadTask = imageRef.putFile(File(imagePath));

    String? imageUrl;
    await uploadTask.whenComplete(() async {
      imageUrl = await imageRef.getDownloadURL();
    });
    return imageUrl;
  }

  Future<String?> _uploadUserMediaFiles(String path, String userId) async {
    final storageRef = FirebaseStorage.instance.ref();

    final imageRef =
        storageRef.child("user_media_files/$userId/${path.split("/").last}");
    final uploadTask = imageRef.putFile(File(path));

    String? imageUrl;
    await uploadTask.whenComplete(() async {
      imageUrl = await imageRef.getDownloadURL();
    });
    return imageUrl;
  }

  //Update Online Status
  Future<void> updateOnlineStatus({
    required bool isOnline,
    required String userId,
  }) async {
    await _userCollection.doc(userId).update({"isOnline": isOnline});
  }

  Future<void> updatePreferences({
    required Map<String, dynamic>? preferences,
    required String userId,
  }) async {
    await _userCollection.doc(userId).update({"preferences": preferences});
  }

  Future<void> updateDMOn({
    required DateTime dmDate,
    required DateTime dmStartTime,
    required DateTime dmEndTime,
    required String userId,
  }) async {
    await _userCollection.doc(userId).update({
      "isDMOn": true,
      "dmDate": dmDate,
      "dmStartTime": dmStartTime,
      "dmEndTime": dmEndTime
    });
  }

  Future<void> updateDMOff({
    required String userId,
  }) async {
    await _userCollection.doc(userId).update({
      "isDMOn": false,
      "dmDate": null,
      "dmStartTime": null,
      "dmEndTime": null
    });
  }

  Future<void> updateGetFeatured(
      {required String userId,
      required bool isFeatured,
      required DateTime featuredTime}) async {
    await _userCollection
        .doc(userId)
        .update({"isFeaturedOn": isFeatured, "featuredTime": featuredTime});
  }

  // Future<String> isUserPremium() async {
  //   final purchaseInfo = await Purchases.getCustomerInfo();
  //   final entitlement = purchaseInfo.entitlements.active.values.toList();
  //   var subcriptionUserId = purchaseInfo.originalAppUserId;
  //   if (entitlement.isEmpty) subcriptionUserId = "";
  //   return subcriptionUserId;
  // }
}

final isUserAddedProvider = FutureProvider<bool>((ref) async {
  final userCollection = FirebaseFirestore.instance
      .collection(FirebaseConstants.userProfileCollection);
  final userId = ref.watch(currentUserStateProvider)?.uid;
  bool isUserAdded = false;
  await userCollection.where("userId", isEqualTo: userId).get().then((event) {
    if (event.docs.isNotEmpty) {
      isUserAdded = true;
    }
  });
  return isUserAdded;
});
//Show Guided Tour
Future<void> setShowGuidedTour(bool value) async {
  final box = Hive.box(HiveConstants.hiveBox);
  await box.put(HiveConstants.guidedTour, value);
}

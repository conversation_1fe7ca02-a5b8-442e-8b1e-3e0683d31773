import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_player/video_player.dart';
import 'package:flutter/material.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart';


class VideoPlayerPage extends StatefulWidget {
  final String videoUrl;
  final bool isNetwork;
  const VideoPlayerPage({
    Key? key,
    required this.videoUrl,
    required this.isNetwork,
  }) : super(key: key);

  @override
  State<VideoPlayerPage> createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> {
  late VideoPlayerController _controller;

  bool _isFullScreen = false;

  @override
  void initState() {
    super.initState();

    if (widget.isNetwork) {
      _controller = VideoPlayerController.network(widget.videoUrl)
        ..initialize().then((_) {
          setState(() {});
          _controller.play();
        });
    } else {
      _controller = VideoPlayerController.file(File(widget.videoUrl))
        ..initialize().then((_) {
          setState(() {});
          _controller.play();
        });
    }

    _controller.setLooping(true);
    _controller.addListener(() {
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Video Player'),
      ),
      body: RotatedBox(
        quarterTurns: _isFullScreen ? 1 : 0,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            //if (!_isFullScreen) const Spacer(),
            _isFullScreen
                ? Expanded(
                    child: Container(
                        height: MediaQuery.of(context).size.height / 1.5,
                        width: MediaQuery.of(context).size.width,
                        child: VideoPlayer(_controller)),
                  )
                : Container(
                    height: MediaQuery.of(context).size.height / 1.5,
                    width: MediaQuery.of(context).size.width,
                    child: VideoPlayer(_controller)),

            SizedBox(
              height: 8,
              child: VideoProgressIndicator(
                _controller,
                allowScrubbing: true,
                colors: VideoProgressColors(
                  playedColor: Colors.blueGrey,
                  bufferedColor: Colors.grey.shade400,
                  backgroundColor: Colors.grey,
                ),
              ),
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                CupertinoButton(
                  padding: const EdgeInsets.all(0),
                  onPressed: () {
                    setState(() {
                      _controller.value.isPlaying
                          ? _controller.pause()
                          : _controller.play();
                    });
                  },
                  child: Icon(
                    _controller.value.isPlaying
                        ? Icons.pause
                        : Icons.play_arrow,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  "${_controller.value.position.inHours}:${_controller.value.position.inMinutes.remainder(60)}:${(_controller.value.position.inSeconds.remainder(60))} / ${_controller.value.duration.inHours}:${_controller.value.duration.inMinutes.remainder(60)}:${(_controller.value.duration.inSeconds.remainder(60))}",
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
            if (!_isFullScreen) const Spacer(),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();
  }
}

class VideoPlayerThumbNail extends StatefulWidget {
  final VoidCallback onTap;
  final String? videoUrl;
  late Future<Uint8List?> _thumbnailFuture;
  late Future<Duration> _durationFuture;

  VideoPlayerThumbNail({Key? key, required this.onTap, required this.videoUrl})
      : super(key: key);

  @override
  State<VideoPlayerThumbNail> createState() => _VideoPlayerThumbNailState();
}

class _VideoPlayerThumbNailState extends State<VideoPlayerThumbNail> {
  @override
  Widget build(BuildContext context) {
    widget._thumbnailFuture = generateThumbnail(widget.videoUrl);

    widget._durationFuture = getVideoDuration(widget.videoUrl ?? "");

    return GestureDetector(
        onTap: widget.onTap,
        child: Container(
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(16)),
            color: Colors.black38,
          ),
          height: 180,
          width: 130,
          // color: Colors.black38,
          // child: Icon(
          //   Icons.play_circle_filled,
          //   size: 60,
          //   color: Colors.grey.shade200,
          // ),

          child: FutureBuilder<Uint8List?>(
            future: widget._thumbnailFuture,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Padding(
                  padding: EdgeInsets.only(
                      top: 75.0, bottom: 75, left: 50, right: 50),
                  child: CircularProgressIndicator(),
                );
              } else if (snapshot.hasError) {
                return const Text('Error generating thumbnail');
              } else if (snapshot.hasData) {
                return ClipRRect(
                    borderRadius: BorderRadius.circular(
                        16), // Adjust the radius as needed
                    child: Stack(children: [
                      Container(
                          height: 180,
                          width: 130,
                          child:
                              Image.memory(snapshot.data!, fit: BoxFit.cover)),
                      Positioned(
                        bottom: 10,
                        left: 10,
                        child: FutureBuilder<Duration>(
                          future: widget._durationFuture,
                          builder: (context, snapshot) {
                            if (snapshot.connectionState ==
                                ConnectionState.waiting) {
                              return const CircularProgressIndicator();
                            } else if (snapshot.hasError) {
                              return const Text('Error getting video duration');
                            } else if (snapshot.hasData) {
                              final duration = snapshot.data!;
                              final formattedDuration =
                                  '${duration.inMinutes}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}';
                              return Center(
                                child: Text(formattedDuration,
                                    style:
                                        const TextStyle(color: Colors.white)),
                              );
                            } else {
                             
                              return const Text('Duration not found.');
                            }
                          },
                        ),
                      )
                    ]));
              } else {
                 Future.delayed(Duration(seconds: 1), () {
            setState(() {
              widget._thumbnailFuture = generateThumbnail("your_video_url"); // replace with your video URL
            });
          });
                return const SizedBox();
              }
            },
          ),
        ));
  }

  Future<Uint8List?> generateThumbnail(String? videoUrl) async {
  //   var request = await http.Client().get(Uri.parse(videoUrl??""));
  // var tempDir = await getTemporaryDirectory();
  //   File file = File(join(tempDir.path, "tempfile"));
  //  file.writeAsBytes(request.bodyBytes);
    final thumbnail = await VideoThumbnail.thumbnailData(
      video: videoUrl ?? "",
      imageFormat: ImageFormat.JPEG,
      maxWidth: 128, // Adjust the size as needed
      quality: 100, // Adjust the quality as needed
    );

    return thumbnail;
  }

  Future<Duration> getVideoDuration(String videoUrl) async {
    final videoController = VideoPlayerController.file(File(videoUrl));
    await videoController.initialize();
    final duration = videoController.value.duration;
    videoController.dispose();
    return duration;
  }
}

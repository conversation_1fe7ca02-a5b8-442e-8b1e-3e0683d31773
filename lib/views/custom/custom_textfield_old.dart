import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String labelText;
  final String hintText;
  final bool obscureText;
  final FormFieldValidator<String>? validator;
  final GestureTapCallback? onTap;
  final int? maxLength;
  final int? maxLines;
  final int? minLines;
  final ValueChanged? onChanged;
  final Widget? suffixIcon;
  final TextInputType? textInputType;
  final List<TextInputFormatter>? inputFormatters;
  final Function()? ontap;
  final TextAlign? textAlign;
  final bool? readOnly;

  const CustomTextField(
      {required this.controller,
      required this.labelText,
      required this.hintText,
      this.obscureText = false,
      this.validator,
      this.onTap,
      this.maxLength,
      this.maxLines = 1,
      this.minLines = 1,
      this.onChanged,
      this.suffixIcon,
      this.textInputType,
      this.inputFormatters,
      this.ontap,
      this.textAlign,
      this.readOnly = false,
      });

  @override
  Widget build(BuildContext context) {
    final primaryColor = Theme.of(context).primaryColor;

    return TextFormField(
      minLines: minLines,
      obscuringCharacter: "*",
      maxLength: maxLength,
      onChanged: onChanged,
      maxLines: maxLines,
      onTap: onTap,
      readOnly: readOnly!,
      controller: controller,
      textAlign: textAlign ?? TextAlign.start,
      // enableInteractiveSelection: false,
      keyboardType: textInputType,
      decoration: InputDecoration(
        
          suffixIcon:suffixIcon!=null? InkWell(
            onTap: (){

              ontap!();
              
            },
            child: suffixIcon):suffixIcon,
          // counterText: '',
          alignLabelWithHint: true,
          floatingLabelBehavior: FloatingLabelBehavior.always,
          hintText: "$hintText",
          labelText: "$labelText",
          labelStyle: const TextStyle(
            color: Color(0xff000000),
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
          hintStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Color(0xffE7E7E7), // Customize the hint text color if needed
          ),
          enabledBorder: const UnderlineInputBorder(
            borderSide: BorderSide(color: Color(0xffA1A1A1)),
          ),
          focusedBorder: const UnderlineInputBorder(
            borderSide: BorderSide(color: Color(0xffA1A1A1)),
          )),
      cursorColor: Colors.black,
      style: const TextStyle(
          fontWeight: FontWeight.w500, color: Colors.black, fontSize: 16),

      inputFormatters:inputFormatters,

      // decoration: InputDecoration(
      //   labelText: labelText,
      //   border: OutlineInputBorder(
      //     borderSide: BorderSide(
      //       color: primaryColor,
      //     ),
      //     borderRadius: BorderRadius.circular(AppConstants.defaultNumericValue),
      //   ),
      //   enabledBorder: OutlineInputBorder(
      //     borderSide: BorderSide(
      //       color: primaryColor,
      //     ),
      //     borderRadius: BorderRadius.circular(AppConstants.defaultNumericValue),
      //   ),
      //   focusedBorder: OutlineInputBorder(
      //     borderSide: BorderSide(
      //       color: primaryColor,
      //     ),
      //     borderRadius: BorderRadius.circular(AppConstants.defaultNumericValue),
      //   ),
      // ),
      obscureText: obscureText,
      validator: validator,
    );
  }
}

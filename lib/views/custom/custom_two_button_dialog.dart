import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../../helpers/validators.dart';
import 'custom_button.dart';
import 'custom_textfield.dart';

class CustomTwoButtonWithInputDialog extends StatefulWidget {
  final Function(String) onAuthenticate;
  final String title;
  final String description;
  final bool? isDeleteAccount;

  const CustomTwoButtonWithInputDialog(
      {super.key,
      required this.onAuthenticate,
      required this.title,
      required this.description,
      this.isDeleteAccount});

  @override
  // ignore: library_private_types_in_public_api
  _CustomTwoButtonWithInputDialogState createState() =>
      _CustomTwoButtonWithInputDialogState();
}

class _CustomTwoButtonWithInputDialogState
    extends State<CustomTwoButtonWithInputDialog> {
  final TextEditingController _passwordController = TextEditingController();
  bool _isObscureText = true;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      elevation: 0,
      child: Container(
        padding: const EdgeInsets.only(top: 30, left: 20, right: 20),
        height: 300,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  widget.title,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                      fontSize: 18, fontWeight: FontWeight.w600),
                ),
                const SizedBox(
                  height: 8,
                ),
                Text(
                  widget.description,
                  style: const TextStyle(
                      fontSize: 14, fontWeight: FontWeight.w400),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 10),
                CustomTextField(
                  suffixIcon: InkWell(
                    onTap: () {
                      _isObscureText = !_isObscureText;
                      setState(() {});
                    },
                    child: _isObscureText
                        ? const Icon(
                            Icons.visibility_off,
                            color: Color(0xFFC0C0C0),
                          )
                        : const Icon(Icons.visibility,
                            color: Color(0xFFC0C0C0)),
                  ),
                  lableColor: Colors.black,
                  hintColor: Colors.black,
                  controller: _passwordController,
                  labelText: 'Password',
                  hintText: 'Enter password',
                  obscureText: _isObscureText,
                  validator: Validators.validatePassword,
                  onTap: () {},
                ),
              ],
            ),
            const SizedBox(
              height: 30,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Container(
                    alignment: Alignment.center,
                    child: CustomButton(
                      onPressed: () {
                        final password = _passwordController.text;
                        FocusManager.instance.primaryFocus?.unfocus();

                        if (password.isEmpty) {
                          EasyLoading.showToast(
                              "Please enter your password to proceed.");
                        } else {
                          widget.onAuthenticate(password);
                        }
                        Navigator.pop(context);
                      },
                      text: (widget.isDeleteAccount ?? false)
                          ? "Delete"
                          : "Authenticate",
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    alignment: Alignment.center,
                    child: CustomButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      text: "Cancel",
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class AccountAuthDialog extends StatefulWidget {
  final Function(String) onAuthenticate;
  final String title;

  const AccountAuthDialog({
    Key? key,
    required this.onAuthenticate,
    required this.title,
  }) : super(key: key);

  @override
  // ignore: library_private_types_in_public_api
  _AccountAuthDialogState createState() => _AccountAuthDialogState();
}

class _AccountAuthDialogState extends State<AccountAuthDialog> {
  final TextEditingController _passwordController = TextEditingController();
  bool _isObscureText = true;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      elevation: 0,
      child: Container(
        padding: const EdgeInsets.only(top: 30, left: 20, right: 20),
        height: 230,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  widget.title,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.w400),
                ),
                const SizedBox(height: 10),
                CustomTextField(
                  suffixIcon: InkWell(
                    onTap: () {
                      _isObscureText = !_isObscureText;
                      setState(() {});
                    },
                    child: _isObscureText
                        ? const Icon(
                            Icons.visibility_off,
                            color: Color(0xFFC0C0C0),
                          )
                        : const Icon(Icons.visibility,
                            color: Color(0xFFC0C0C0)),
                  ),
                  controller: _passwordController,
                  labelText: 'Password',
                  hintText: 'Enter password',
                  hintColor: Colors.black,
                  lableColor: Colors.black,
                  obscureText: _isObscureText,
                  validator: Validators.validatePassword,
                  onTap: () {},
                ),
              ],
            ),
            const SizedBox(
              height: 30,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: 140,
                  alignment: Alignment.center,
                  child: CustomButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    text: "Cancel",
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 10),
                  child: Container(
                    width: 140,
                    alignment: Alignment.center,
                    child: CustomButton(
                      onPressed: () {
                        final password = _passwordController.text;
                        FocusManager.instance.primaryFocus?.unfocus();

                        if (password.isEmpty) {
                          EasyLoading.showToast(
                              "Please enter your password to proceed.");
                        } else {
                          widget.onAuthenticate(password);
                        }
                        Navigator.pop(context);
                      },
                      text: "Authenticate",
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// import 'package:fringle_app/views/membership/premium_chip_widget.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';

// import '../../helpers/constants.dart';
// import '../custom/new_custom_app_button.dart';
// import 'custom_feature_row_widget.dart';
// import 'membership_screen.dart';

// class PremiumMembership extends StatefulWidget {
//   final bool isFirstTime;
//   final Function(bool isPurchase, int selectedOption) isPurchase;
//   final bool isPremiumUser;
//   final bool isPremiumPlusUser;

//   const PremiumMembership({
//     super.key,
//     Key? key,
//     this.isFirstTime = false,
//     required this.isPurchase,
//     required this.isPremiumUser,
//     required this.isPremiumPlusUser,
//   });

//   @override
//   State<PremiumMembership> createState() => _PremiumMembershipState();
// }

// class _PremiumMembershipState extends State<PremiumMembership> {
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: SingleChildScrollView(
//         scrollDirection: Axis.vertical,
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             const SizedBox(
//               height: 24,
//             ),
//             Padding(
//               padding: EdgeInsets.symmetric(horizontal: 16),
//               child: Container(
//                 decoration: BoxDecoration(
//                   borderRadius: BorderRadius.circular(14),
//                   color: const Color(0xffF2F2F2),
//                   gradient: const LinearGradient(
//                     colors: [
//                       // Colors.yellow,Colors.red,
//                       // Colors.black,
//                       Color(0xFFC70973),
//                       Color(0xFF46239F),
//                     ],
//                     begin: Alignment.topCenter,
//                     end: Alignment.bottomCenter,
//                     //  tileMode: TileMode.clamp,
//                     //  transform: GradientRotation(0),
//                   ),
//                 ),
//                 child: Column(
//                   children: [
//                     if (widget.isPremiumUser)
//                       Container(
//                         padding:
//                             EdgeInsets.symmetric(horizontal: 20, vertical: 5),
//                         width: double.infinity,
//                         decoration: BoxDecoration(
//                             color: Colors.white.withOpacity(.4),
//                             // gradient: LinearGradient(
//                             //   colors: [
//                             //     Color(
//                             //         0xFFD9056C).withOpacity(.5), // Color(red: 0.85, green: 0.02, blue: 0.42)
//                             //     Color(
//                             //         0xFF3A24A2).withOpacity(.5), // Color(red: 0.23, green: 0.14, blue: 0.64)
//                             //   ],
//                             //   begin: Alignment.centerLeft,
//                             //   end: Alignment.centerRight,
//                             //   // begin: Alignment(0, -0.42),
//                             //   // end: Alignment(0.98, 1.6),
//                             // ),
//                             borderRadius: BorderRadius.only(
//                                 topRight: Radius.circular(14),
//                                 topLeft: Radius.circular(14))),
//                         child: const Text(
//                           "CURRENT PLAN",
//                           style: TextStyle(
//                               color: Colors.white,
//                               fontSize: 10,
//                               fontWeight: FontWeight.w700),
//                         ),
//                       ),
//                     Padding(
//                       padding: const EdgeInsets.symmetric(
//                           horizontal: 20, vertical: 20),
//                       child: Column(
//                         children: [
//                           Column(
//                             children: [
//                               Row(
//                                 mainAxisAlignment:
//                                     MainAxisAlignment.spaceBetween,
//                                 children: [
//                                   Column(
//                                     crossAxisAlignment:
//                                         CrossAxisAlignment.start,
//                                     children: [
//                                       Text(
//                                         "Premium",
//                                         style: TextStyle(
//                                           color: Colors.white,
//                                           fontSize: 24,
//                                           fontWeight: FontWeight.w700,
//                                         ),
//                                       ),
//                                       SizedBox(
//                                         height: 10,
//                                       ),
//                                       Text(
//                                         "For Those Seeking More!",
//                                         style: TextStyle(
//                                           color: Colors.white,
//                                           fontSize: 14,
//                                           fontWeight: FontWeight.w400,
//                                         ),
//                                       )
//                                     ],
//                                   ),
//                                   SvgPicture.asset(
//                                     AppConstants.premiumIcon,
//                                     // height: 15,
//                                   ),
//                                 ],
//                               ),
//                               const SizedBox(
//                                 height: 20,
//                               ),
//                               PremiumChipWidget(
//                                 chipList: [
//                                   "Polyamorous Relationships",
//                                   "Casual Encounters",
//                                   "Ethical Non-Monogomy",
//                                   "Kink",
//                                   "Swinging",
//                                 ],
//                               ),
//                               SizedBox(
//                                 height: 20,
//                               ),
//                               const CustomFeatureRow(
//                                   color: Colors.white,
//                                   name: "Swipe",
//                                   iconPath: AppConstants.premiumWhiteCheckIcon),
//                               SizedBox(
//                                 height: 2,
//                               ),
//                               const CustomFeatureRow(
//                                   color: Colors.white,
//                                   name: "Match",
//                                   iconPath: AppConstants.premiumWhiteCheckIcon),
//                               SizedBox(
//                                 height: 2,
//                               ),
//                               const CustomFeatureRow(
//                                   color: Colors.white,
//                                   name: "Message",
//                                   iconPath: AppConstants.premiumWhiteCheckIcon),
//                               SizedBox(
//                                 height: 2,
//                               ),
//                               const CustomFeatureRow(
//                                   color: Colors.white,
//                                   name: "Like",
//                                   iconPath: AppConstants.premiumWhiteCheckIcon),
//                               SizedBox(
//                                 height: 2,
//                               ),
//                               const CustomFeatureRow(
//                                   color: Colors.white,
//                                   name: "Unlimited Scroll to New Profiles",
//                                   iconPath: AppConstants.premiumWhiteCheckIcon),
//                               SizedBox(
//                                 height: 2,
//                               ),
//                               const CustomFeatureRow(
//                                   color: Colors.white,
//                                   name: "Send 8 sec Introductory Video",
//                                   iconPath: AppConstants.premiumWhiteCheckIcon),
//                               SizedBox(
//                                 height: 2,
//                               ),
//                               const CustomFeatureRow(
//                                   color: Colors.white,
//                                   name: "Create a Couple Account",
//                                   iconPath: AppConstants.premiumWhiteCheckIcon),
//                               SizedBox(
//                                 height: 2,
//                               ),
//                               const CustomFeatureRow(
//                                   color: Colors.white,
//                                   name: "Get Featured",
//                                   iconPath: AppConstants.premiumWhiteCheckIcon),
//                               SizedBox(
//                                 height: 2,
//                               ),
//                               const CustomFeatureRow(
//                                   color: Colors.white,
//                                   name: "Content Creator",
//                                   iconPath: AppConstants.premiumCloseIcon),
//                             ],
//                           ),

//                           // Spacer(),
//                           SizedBox(
//                             height: 35,
//                           ),
//                           if (!(widget.isPremiumUser))
//                             SizedBox(
//                               height: 52,
//                               child: NewCustomAppButton(
//                                   onTap: () {
//                                     showModalBottomSheet(
//                                       shape: const RoundedRectangleBorder(
//                                         // <-- SEE HERE
//                                         borderRadius: BorderRadius.vertical(
//                                           top: Radius.circular(25.0),
//                                         ),
//                                       ),
//                                       context: context,
//                                       builder: (BuildContext context) =>
//                                           MyBottomSheet(
//                                         isPremiumUser: widget.isPremiumUser,
//                                         isPremiumPlusUser:
//                                             widget.isPremiumPlusUser,
//                                         isFirstTime: widget.isFirstTime,
//                                         isPurchase:
//                                             (isPurchase, selectedOption) {
//                                           widget.isPurchase(
//                                               isPurchase, selectedOption);
//                                         },
//                                       ),
//                                     );
//                                   },
//                                   isGradient: false,
//                                   borderRadius: 12,
//                                   textColor: Colors.black,
//                                   buttonTxt: "Buy now"),
//                             ),
//                           // SizedBox(height: 20,)
//                         ],
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//             const SizedBox(
//               height: 24,
//             ),
//             // const SizedBox(
//             //   height: 100,
//             // )
//           ],
//         ),
//       ),
//       // bottomSheet: Padding(
//       //   padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 29),
//       //   child: SizedBox(
//       //       height: 52,
//       //       child: NewCustomAppButton(
//       //           onTap: () {
//       //             showModalBottomSheet(
//       //               backgroundColor: Colors.black,
//       //               shape: const RoundedRectangleBorder(
//       //                 // <-- SEE HERE
//       //                 borderRadius: BorderRadius.vertical(
//       //                   top: Radius.circular(25.0),
//       //                 ),
//       //               ),
//       //               context: context,
//       //               builder: (BuildContext context) => MyBottomSheet(
//       //                 isFirstTime: widget.isFirstTime,
//       //                 isPurchase: (isPurchase, selectedOption) {
//       //                   widget.isPurchase(isPurchase, selectedOption);
//       //                 },
//       //               ),
//       //             );
//       //           },
//       //           isGradient: true,
//       //           borderRadius: 12,
//       //           textColor: Colors.white,
//       //           buttonTxt: "Buy now")),
//       // ),
//     );
//   }
// }

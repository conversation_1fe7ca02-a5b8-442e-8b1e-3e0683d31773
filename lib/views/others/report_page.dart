// ignore_for_file: use_build_context_synchronously
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../helpers/constants.dart';
import '../../helpers/media_picker_helper.dart';
import '../../models/report_model.dart';
import '../../models/user_profile_model.dart';
import '../../providers/auth_providers.dart';
import '../../providers/report_provider.dart';
import '../custom/custom_app_loader.dart';
import '../custom/custom_button.dart';
import '../custom/custom_textfield.dart';
import '../tabs/home/<USER>';

class ReportPage extends ConsumerStatefulWidget {
  final UserProfileModel userProfileModel;
  const ReportPage({
    super.key,
    required this.userProfileModel,
  });

  @override
  ConsumerState<ReportPage> createState() => _ReportPageState();
}

class _ReportPageState extends ConsumerState<ReportPage> {
  final _imagesScrollcontroller = ScrollController();
  final _formKey = GlobalKey<FormState>();
  final _reasonController = TextEditingController();
  final List<String> _imagePaths = [];

  void onTapAddImage() async {
    final image = await pickMedia();
    if (image == null) {
      return;
    } else {
      setState(() {
        _imagePaths.add(image);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          'Report',
          style: GoogleFonts.manrope(
            fontSize: 18,
            fontWeight: FontWeight.w500,
            color: AppConstants.primaryTextColor,
          ),
        ),
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        automaticallyImplyLeading: false,
        elevation: 2,
        shadowColor: Colors.white.withOpacity(0.6),
        leading: Padding(
          padding: const EdgeInsets.only(left: 16.0),
          child: IconButton(
            icon: SvgPicture.asset(
              AppConstants.backIcon,
              color: Colors.black,
              height: 24,
              width: 24,
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultNumericValue),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                "You can report this user if you find this person offensive or if you think they are a bot. We will review your report and if we find it to be true, we will block the user from using the app. Thank you for your cooperation.",
                style: GoogleFonts.manrope(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppConstants.primaryTextColor,
                ),
              ),
              const SizedBox(height: AppConstants.defaultNumericValue),
              Card(
                color: Colors.white,
                child: ListTile(
                  contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  leading: UserCirlePicture(
                    imageUrl: widget.userProfileModel.profilePicture,
                    size: 50,
                  ),
                  title: Text(
                    "${widget.userProfileModel.firstName} ${widget.userProfileModel.lastName}",
                    style: GoogleFonts.manrope(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppConstants.primaryTextColor,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: AppConstants.defaultNumericValue),
              CustomTextField(
                controller: _reasonController,
                maxLines: 9,
                maxLength: 350,
                hintText: 'Please enter your reason for reporting this user',
                hintColor: AppConstants.primaryTextColor,
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Please enter a reason';
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppConstants.defaultNumericValue),
              Text(
                'Add some images to support your report',
                style: GoogleFonts.manrope(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppConstants.primaryTextColor,
                ),
              ),
              const SizedBox(height: AppConstants.defaultNumericValue),
              _imagePaths.isEmpty
                  ? AddNewImageWidget(
                      onPressed: onTapAddImage,
                    )
                  : SingleChildScrollView(
                      controller: _imagesScrollcontroller,
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: _imagePaths.map((imagePath) {
                          return Row(
                            children: [
                              Stack(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(right: 16.0),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(12),
                                      child: Image.file(
                                        File(imagePath),
                                        fit: BoxFit.cover,
                                        width: MediaQuery.of(context).size.width * 0.3,
                                        height: MediaQuery.of(context).size.width * 0.5,
                                      ),
                                    ),
                                  ),
                                  Positioned(
                                    top: 8,
                                    right: 31,
                                    child: GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          _imagePaths.remove(imagePath);
                                        });
                                      },
                                      child: Container(
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          shape: BoxShape.circle,
                                        ),
                                        padding: EdgeInsets.all(4),
                                        child: SvgPicture.asset(
                                          AppConstants.dislike,
                                          color: Colors.black,
                                          height: 12,
                                          width: 12,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              if (_imagePaths.indexOf(imagePath) == _imagePaths.length - 1)
                                const SizedBox(width: AppConstants.defaultNumericValue),
                              if (_imagePaths.indexOf(imagePath) == _imagePaths.length - 1)
                                AddNewImageWidget(
                                  onPressed: onTapAddImage,
                                ),
                            ],
                          );
                        }).toList(),
                      ),
                    ),
              const SizedBox(height: AppConstants.defaultNumericValue),
              CustomButton(
                onPressed: () async {
                  if (_formKey.currentState!.validate()) {
                    final ReportModel reportModel = ReportModel(
                      id: "${widget.userProfileModel.id}report${DateTime.now().millisecondsSinceEpoch}",
                      createdAt: DateTime.now(),
                      images: _imagePaths,
                      reason: _reasonController.text,
                      reportedByUserId: ref.watch(currentUserStateProvider)!.uid,
                      reportingUserId: widget.userProfileModel.id,
                    );
                    CustomAppLoader.showCustomLoader("Sending report...");
                    await reportUser(reportModel).then((value) {
                      EasyLoading.dismiss();
                      showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (context) {
                          return Dialog(
                            backgroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Container(
                              padding: EdgeInsets.all(16),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    "Report sent!",
                                    style: GoogleFonts.manrope(
                                      fontSize: 18,
                                      fontWeight: FontWeight.w700,
                                      color: AppConstants.primaryTextColor,
                                    ),
                                  ),
                                  SizedBox(height: 16),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 24),
                                    child: Text(
                                      "Do you want to block this user as well?",
                                      style: GoogleFonts.manrope(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                        color: Color(0xFF7C7C7C),
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                  SizedBox(height: 24),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: TextButton(
                                          onPressed: () {
                                            Navigator.pop(context);
                                            Navigator.pop(context);
                                          },
                                          child: Text(
                                            "No",
                                            style: GoogleFonts.manrope(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w700,
                                              color: AppConstants.primaryTextColor,
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 16),
                                      Expanded(
                                        child: ElevatedButton(
                                          onPressed: () {
                                          },
                                          style: ElevatedButton.styleFrom(
                                            elevation: 0,
                                            minimumSize: Size(double.infinity, 38),
                                            backgroundColor: Color(0xFFDD0000),
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(14),
                                            ),
                                          ),
                                          child: Text(
                                            "Yes, block",
                                            style: GoogleFonts.manrope(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w700,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      );
                    });
                  }
                },
                text: 'Submit',
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class AddNewImageWidget extends StatelessWidget {
  final VoidCallback onPressed;
  const AddNewImageWidget({
    super.key,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.3,
        height: MediaQuery.of(context).size.width * 0.5,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey, width: 1),
          borderRadius: BorderRadius.circular(AppConstants.defaultNumericValue),
        ),
        child: const Center(child: Icon(Icons.add)),
      ),
    );
  }
}

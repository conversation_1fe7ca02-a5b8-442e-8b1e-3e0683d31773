import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../helpers/constants.dart';
import '../../models/custom_check_model.dart';

class GenderItemWidget extends StatelessWidget {
  final CustomCheckModel item;
  final ValueChanged<bool> onSelectionChanged;

  const GenderItemWidget(
      {super.key, required this.item, required this.onSelectionChanged});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onSelectionChanged(!item.isSelected);
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: item.isSelected ? null : Colors.white,
          border: item.isSelected
              ? null
              : Border.all(
                  color: const Color(0xFFB5B4B4), // Border color
                  width: 1, // Border width
                ),
          gradient: item.isSelected ? AppConstants.defaultGradient : null,
          boxShadow: item.isSelected ? [
            BoxShadow(
              color: const Color(0xFF4B0B5C).withOpacity(0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ] : null,
        ),
        padding: const EdgeInsets.symmetric(vertical: 13),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (item.selectedIcon.isNotEmpty)
              SvgPicture.asset(
                item.selectedIcon,
                color: item.isSelected ? Colors.white : const Color(0xFF717171),
              ),
            if (item.selectedIcon.isNotEmpty)
              const SizedBox(width: 16),
            Text(
              item.name,
              style: GoogleFonts.manrope(
                color: item.isSelected ? Colors.white : const Color(0xFF717171),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';

class CustomAlertDialog extends StatelessWidget {
  final String title;
  final String imagePath;
  final Duration? autoDismissDuration;
  final double? iconSize;
  final Color? textColor;
  final double? borderRadious;

  // ignore: use_key_in_widget_constructors
  const CustomAlertDialog(
      {required this.title,
      required this.imagePath,
      this.autoDismissDuration,
      this.iconSize,
      this.textColor = Colors.black,
      this.borderRadious = 7});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      contentPadding: const EdgeInsets.all(10),
      content: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(borderRadious ?? 7),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              imagePath,
              height: 41,
              width: 41,
            ),
            const SizedBox(height: 20),
            Text(
              title,
              style: TextStyle(
                  color: textColor,
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w400,
                  fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  // Function to show the custom dialog
  show(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return this;
      },
    );

    // Auto dismiss the dialog after a specified duration
    if (autoDismissDuration != null) {
      Future.delayed(autoDismissDuration!, () {
        Navigator.pop(context);
      });
    }
  }

  onDismiss(BuildContext context) {
    Navigator.pop(context);
  }
}

import 'package:fringle_app/views/others/custom_gradient_background.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class GradientAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  const GradientAppBar({super.key, required this.title});
  @override
  Widget build(BuildContext context) {
    return AppBar(
      systemOverlayStyle: const SystemUiOverlayStyle(
        // Status bar color
        statusBarColor: Color(0xFFFFFCF5),
        // Status bar brightness (optional)
        statusBarIconBrightness: Brightness.dark, // For Android (dark icons)
        statusBarBrightness: Brightness.light, // For iOS (dark icons)
      ),
      backgroundColor: const Color(0xFFFFFCF5), // Make AppBar transparent
      elevation: 0, // Remove the shadow
      flexibleSpace: SafeArea(
        child: CustomGradientWidget(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          child: Align(
            alignment: Alignment.bottomLeft,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Center(
                child: Text(
                  title,
                  style: const TextStyle(
                      color: Colors.white,
                      fontSize: 17,
                      fontWeight: FontWeight.w400),
                ),
              ),
            ),
          ),
        ),
      ),
      // Other AppBar properties as needed
    );
  }

  @override
  Size get preferredSize {
    return const Size.fromHeight(kToolbarHeight); // Use default AppBar height
  }
}

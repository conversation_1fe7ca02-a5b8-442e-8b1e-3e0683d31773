import 'package:custom_pop_up_menu/custom_pop_up_menu.dart';
import 'package:fringle_app/views/others/custom_gradient_background.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/models/notification_model.dart';
import 'package:fringle_app/providers/auth_providers.dart';
import 'package:fringle_app/providers/notifiaction_provider.dart';
import 'package:fringle_app/views/custom/custom_app_bar.dart';
import 'package:fringle_app/views/custom/custom_icon_button.dart';
import 'package:fringle_app/views/tabs/messages/components/chat_page.dart';
import 'package:flutter_svg/flutter_svg.dart';

class NotificationPage extends ConsumerStatefulWidget {
  final Function(NotificationModel data)? notificationData;
  const NotificationPage({super.key, this.notificationData});

  @override
  ConsumerState<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends ConsumerState<NotificationPage> {
  final CustomPopupMenuController _moreMenuController =
      CustomPopupMenuController();
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.invalidate(notificationsStreamProvider);
    });
  }

  @override
  Widget build(BuildContext context) {
    final currentUserRef = ref.watch(currentUserStateProvider);
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 0,
        backgroundColor: Colors.transparent,
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SizedBox(height: AppConstants.defaultNumericValue),
          Padding(
            padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.defaultNumericValue),
            child: CustomAppBar(
              leading: CustomGradientWidget(
                borderRadius: 15,
                child: CustomIconButton(
                    icon: CupertinoIcons.back,
                    color: Colors.white,
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    padding: const EdgeInsets.all(
                        AppConstants.defaultNumericValue / 1.5)),
              ),
              title: const Center(
                  child: Text(
                "Notifications",
                style: TextStyle(
                    fontFamily: AppConstants.fontStyleName,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black),
              )),
              trailing: CustomPopupMenu(
                menuBuilder: () => ClipRRect(
                  borderRadius: BorderRadius.circular(
                      AppConstants.defaultNumericValue / 2),
                  child: Container(
                    decoration: const BoxDecoration(color: Colors.white),
                    child: IntrinsicWidth(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          MoreMenuTitle(
                            title: 'Mark all as read',
                            onTap: () async {
                              _moreMenuController.hideMenu();
                              await markAllAsRead(currentUserRef!.uid);
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                pressType: PressType.singleClick,
                verticalMargin: 0,
                controller: _moreMenuController,
                showArrow: true,
                arrowColor: Colors.white,
                barrierColor: AppConstants.primaryColor.withOpacity(0.1),
                child: GestureDetector(
                  child: const Icon(CupertinoIcons.ellipsis_vertical),
                ),
              ),
            ),
          ),
          const SizedBox(height: AppConstants.defaultNumericValue),
          Expanded(
              child: NotificationBody(
            notificationData: widget.notificationData,
          )),
        ],
      ),
    );
  }
}

class NotificationBody extends ConsumerWidget {
  final Function(NotificationModel data)? notificationData;
  const NotificationBody({super.key, this.notificationData});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    void deleteNotificationDialog(NotificationModel item) {
      showModalBottomSheet(
          context: context,
          builder: (context) {
            return Container(
              padding: const EdgeInsets.all(AppConstants.defaultNumericValue),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                      'Are you sure you want to delete this notification?'),
                  const SizedBox(height: AppConstants.defaultNumericValue),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      TextButton(
                        child: const Text(
                          'Cancel',
                          style: TextStyle(color: Colors.black),
                        ),
                        onPressed: () {
                          Navigator.pop(context);
                        },
                      ),
                      const SizedBox(width: AppConstants.defaultNumericValue),
                      TextButton(
                        child: const Text('Delete'),
                        onPressed: () {
                          Navigator.pop(context);
                          deleteNotification(item.id);
                        },
                      ),
                    ],
                  ),
                ],
              ),
            );
          });
    }

    void onTapNotification(WidgetRef ref, NotificationModel item) {
      if (!item.isRead) {
        updateNotification(item.copyWith(isRead: true));
        ref.invalidate(notificationsStreamProvider);
      }
      if (notificationData != null) {
        Navigator.pop(context);
        notificationData!(item);
      }
    }

    String getNotificationIcon(int type) {
      if (type == AppConstants.favoritedNotifacation) {
        return AppConstants.starIcon;
      } else if (type == AppConstants.likeNotifacation) {
        return AppConstants.likeIcon;
      } else if (type == AppConstants.messageNotifacation) {
        return AppConstants.chatIcon;
      } else {
        return AppConstants.matchIcon;
      }
    }

    String getNotificationTitle(int type) {
      if (type == AppConstants.favoritedNotifacation) {
        return " favorited your profile";
      } else if (type == AppConstants.likeNotifacation) {
        return " like your profile";
      } else if (type == AppConstants.messageNotifacation) {
        return "";
      } else {
        return " is your new match";
      }
    }

    final notifications = ref.watch(notificationsStreamProvider);
    return notifications.when(
      data: (data) {
        if (data.isEmpty) {
          return const Center(child: Text('No notifications'));
        } else {
          return ListView.builder(
            itemBuilder: (context, index) {
              NotificationModel item = data[index];

              return InkWell(
                onTap: () {
                  onTapNotification(ref, item);
                },
                onLongPress: () {
                  deleteNotificationDialog(item);
                },
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const SizedBox(
                            width: 8,
                          ),
                          Container(
                            height: 48,
                            width: 48,
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                                color: const Color(0xFFD91181),
                                borderRadius: BorderRadius.circular(30)),
                            child: SvgPicture.asset(
                                getNotificationIcon(item.notificationType)),
                          ),
                          const SizedBox(
                            width: 15,
                          ),
                          Expanded(
                              child: RichText(
                            text: TextSpan(
                              text: item.userName,
                              style: const TextStyle(
                                fontFamily: AppConstants.fontStyleName,
                                color: Colors.black,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                              children: [
                                TextSpan(
                                  text: getNotificationTitle(
                                      item.notificationType),
                                  style: const TextStyle(
                                      fontFamily: AppConstants.fontStyleName,
                                      color: Colors.black,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                      overflow: TextOverflow.ellipsis),
                                ),
                              ],
                            ),
                          )),
                          if (!item.isRead)
                            Container(
                                height: 18,
                                width: 18,
                                padding: const EdgeInsets.all(3),
                                decoration: BoxDecoration(
                                    color: const Color(0xFFDD0000),
                                    borderRadius: BorderRadius.circular(30)),
                                child: const Center(
                                  child: Text(
                                    "1",
                                    style: TextStyle(
                                        fontSize: 10,
                                        color: Colors.white,
                                        fontFamily: "Inter",
                                        fontWeight: FontWeight.w600),
                                  ),
                                )),
                          const SizedBox(
                            width: 20,
                          ),
                        ],
                      ),
                    ),
                    const Divider(
                      height: 2,
                      color: Color(0xFFF2F2F2),
                    )
                  ],
                ),
              );
            },
            itemCount: data.length,
          );
        }
      },
      error: (e, st) {
        return const SizedBox();
      },
      loading: () {
        return const SizedBox();
      },
    );
  }
}

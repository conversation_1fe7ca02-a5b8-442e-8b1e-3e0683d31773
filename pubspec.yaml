name: fringle_app
description: "Fr<PERSON>le App"
publish_to: 'none'

version: 3.0.0+2

environment:
  sdk: ^3.5.3

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  google_fonts: ^6.2.1
  flutter_riverpod: ^2.1.3
  hive_flutter: ^1.1.0
  flutter_easyloading: ^3.0.5
  hive: ^2.2.3
  cached_network_image: ^3.2.3
  photo_view: ^0.14.0
  animations: ^2.0.7
  # google_sign_in: ^6.0.0
  firebase_core: ^3.13.1
  firebase_auth: ^5.5.4
  cloud_firestore: ^5.6.4
  flutter_svg: ^2.0.17
  intl: ^0.19.0
  firebase_storage: ^12.4.6
  image_picker: ^1.1.2
  location: ^6.0.2
  emoji_picker_flutter: ^4.3.0
  custom_pop_up_menu: ^1.2.4
  auto_size_text: ^3.0.0
  geolocator: ^13.0.2
  geolocator_android: 4.6.1
  http: ^1.4.0
  geocoding: ^3.0.0
  swipe_cards: ^2.0.0+1
  url_launcher: ^6.1.7
  # sign_in_with_apple: ^7.0.1
  # flutter_facebook_auth: ^6.0.4
  encrypt: ^5.0.1
  social_media_recorder: ^1.2.1
  voice_message_package: ^2.2.1
  # google_mobile_ads: ^5.3.1
  firebase_messaging: ^15.2.6
  webview_flutter: ^4.0.1
  collection: ^1.16.0
  tutorial_coach_mark: ^1.2.4
  lottie: ^3.2.0
  video_player: ^2.5.1
  # purchases_flutter: ^8.8.1
  carousel_slider: ^5.0.0
  loading_animation_widget: ^1.3.0
  vibration: ^3.1.3
  dropdown_button2: ^2.3.9
  nsfw_detector_flutter: ^1.0.5
  shimmer: ^3.0.0
  shared_preferences: ^2.5.3
  video_thumbnail: ^0.5.6
  simple_gradient_text: ^1.3.0
  path_provider: ^2.1.5
  fast_cached_network_image: 1.2.9
  
dependency_overrides:
  photo_view:
    git:
      url: https://github.com/bluefireteam/photo_view
      ref: main

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  change_app_package_name: ^1.1.0

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/json/
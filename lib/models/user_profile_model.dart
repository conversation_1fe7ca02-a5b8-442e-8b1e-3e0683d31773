// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:flutter/foundation.dart';

import 'package:fringle_app/models/user_account_settings_model.dart';

class UserProfileModel {
  String id;
  String userId;
  String? firstName;
  String? lastName;
  String? name;
  double? latitude;
  double? longitude;
  String? address;

  String? email;
  String? profilePicture;
  String? phoneNumber;
  String gender;
  String? about;
  DateTime? birthDay;
  List<String> mediaFiles;
  List<String> interests;
  List<String>? favouriteIdList = [];
  UserAccountSettingsModel userAccountSettingsModel;
  bool isVerified;
  bool isOnline;
  bool? isNewUser;
  bool? isPremiumUser;
  bool? isPremiumPlusUser;
  bool? isDMOn;
  bool? isFeaturedOn;
  DateTime? featuredTime;
  DateTime? dmDate;
  DateTime? dmStartTime;
  DateTime? dmEndTime;

  String? education;
  String? drinking;
  String? religion;
  String? cannabis;
  List<String>? pronounceList = [];
  List<String>? imageList = [];
  String? bio;
  String? work;
  int? planType;
  String? introVideoDate;
  int? introVideoCount;
  Map<String, dynamic>? preferences;
  Map<String, dynamic>? couplePrefrencePrimary;
  Map<String, dynamic>? couplePrefrenceSecond;
  bool? isCoupleAccount;
  List<String>? joinedActivities;

  UserProfileModel.defaultConstructor()
      : id = '1',
        userId = '1',
        firstName = 'defaultFirstName',
        lastName = 'defaultLastName',
        gender = 'Male',
        birthDay = DateTime.now(),
        mediaFiles = const [],
        interests = const [],
        userAccountSettingsModel = UserAccountSettingsModel(
            maximumAge: 50,
            minimumAge: 1,
            location:
                UserLocation(addressText: "", latitude: 0.0, longitude: 0.0)),
        isVerified = false,
        isOnline = false,
        joinedActivities = const [];

  UserProfileModel(
      {required this.id,
      required this.userId,
      required this.firstName,
      this.lastName,
      this.name,
      this.latitude,
      this.longitude,
      this.email,
      this.profilePicture,
      this.phoneNumber,
      required this.gender,
      this.about,
      required this.birthDay,
      this.featuredTime,
      required this.mediaFiles,
      required this.interests,
      required this.userAccountSettingsModel,
      required this.isVerified,
      this.isOnline = false,
      this.education,
      this.drinking,
      this.religion,
      this.cannabis,
      this.pronounceList,
      required this.imageList,
      this.bio,
      this.work,
      this.isNewUser,
      this.isPremiumUser,
      this.isPremiumPlusUser,
      this.isDMOn,
      this.isFeaturedOn,
      this.favouriteIdList,
      this.preferences,
      this.address,
      this.dmDate,
      this.dmStartTime,
      this.dmEndTime,
      this.planType,
      this.introVideoDate,
      this.introVideoCount,
      this.couplePrefrencePrimary,
      this.couplePrefrenceSecond,
      this.isCoupleAccount,
      this.joinedActivities});

  UserProfileModel copyWith({
    String? id,
    String? userId,
    String? firstName,
    String? lastName,
      String? name,
      String? address,
      double? latitude,
      double? longitude,
    String? email,
    String? profilePicture,
    String? phoneNumber,
    String? gender,
    String? about,
    DateTime? birthDay,
      DateTime? featuredTime,
    List<String>? mediaFiles,
    List<String>? interests,
      List<String>? favouriteIdList,
    UserAccountSettingsModel? userAccountSettingsModel,
    bool? isVerified,
    bool? isOnline,
      String? education,
      String? drinking,
      String? religion,
      String? cannabis,
      List<String>? pronounceList,
      List<String>? imageList,
      String? bio,
      String? work,
      bool? isNewUser,
      bool? isPremiumUser,
      bool? isPremiumPlusUser,
      bool? isDMOn,
      bool? isFeaturedOn,
      Map<String, dynamic>? preferences,
      DateTime? dmDate,
      DateTime? dmStartTime,
      DateTime? dmEndTime,
      String? introVideoDate,
      int? introVideoCount,
      int? planType,
      Map<String, dynamic>? couplePrefrencePrimary,
      Map<String, dynamic>? couplePrefrenceSecond,
      bool? isCoupleAccount,
      List<String>? joinedActivities,
  }) {
    return UserProfileModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
        name: name ?? this.name,
        address: address ?? this.address,
        latitude: latitude ?? this.latitude,
        longitude: longitude ?? this.longitude,
      email: email ?? this.email,
      profilePicture: profilePicture ?? this.profilePicture,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      gender: gender ?? this.gender,
      about: about ?? this.about,
      birthDay: birthDay ?? this.birthDay,
        featuredTime: featuredTime ?? this.featuredTime,
      mediaFiles: mediaFiles ?? this.mediaFiles,
      interests: interests ?? this.interests,
      userAccountSettingsModel:
          userAccountSettingsModel ?? this.userAccountSettingsModel,
      isVerified: isVerified ?? this.isVerified,
      isOnline: isOnline ?? this.isOnline,
        education: education ?? this.education,
        drinking: drinking ?? this.drinking,
        religion: religion ?? this.religion,
        cannabis: cannabis ?? this.cannabis,
        pronounceList: pronounceList ?? this.pronounceList,
        imageList: imageList ?? this.imageList,
        bio: bio ?? this.bio,
        work: work ?? this.work,
        isNewUser: isNewUser ?? this.isNewUser,
        isPremiumUser: isPremiumUser ?? this.isPremiumUser,
        isPremiumPlusUser: isPremiumPlusUser ?? this.isPremiumPlusUser,
        isDMOn: isDMOn ?? this.isDMOn,
        isFeaturedOn: isFeaturedOn ?? this.isFeaturedOn,
        favouriteIdList: favouriteIdList ?? this.favouriteIdList,
        preferences: preferences ?? this.preferences,
        dmDate: dmDate ?? this.dmDate,
        dmStartTime: dmStartTime ?? this.dmStartTime,
        dmEndTime: dmEndTime ?? this.dmEndTime,
        planType: planType ?? this.planType,
        introVideoCount: introVideoCount ?? this.introVideoCount,
        introVideoDate: introVideoDate ?? this.introVideoDate,
        couplePrefrencePrimary:
            couplePrefrencePrimary ?? this.couplePrefrencePrimary,
        couplePrefrenceSecond:
            couplePrefrenceSecond ?? this.couplePrefrenceSecond,
        isCoupleAccount: isCoupleAccount ?? this.isCoupleAccount,
        joinedActivities: joinedActivities ?? this.joinedActivities,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'userId': userId,
      'firstName': firstName,
      'lastName': lastName,
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'email': email,
      'profilePicture': profilePicture,
      'phoneNumber': phoneNumber,
      'gender': gender,
      'about': about,
      'birthDay': birthDay!.millisecondsSinceEpoch,
      'mediaFiles': mediaFiles,
      'interests': interests,
      'userAccountSettingsModel': userAccountSettingsModel.toMap(),
      'isVerified': isVerified,
      'isOnline': isOnline,
      'education': education,
      'drinking': drinking,
      'religion': religion,
      'cannabis': cannabis,
      'pronounceList': pronounceList,
      'imageList': imageList,
      'bio': bio,
      'work': work,
      'isNewUser': isNewUser,
      'isPremiumUser': isPremiumUser,
      'isPremiumPlusUser': isPremiumPlusUser,
      'isDMOn': isDMOn,
      'isFeaturedOn': isFeaturedOn,
      'favouriteIdList': favouriteIdList,
      'preferences': preferences,
      'featuredTime': featuredTime?.millisecondsSinceEpoch,
      'dmDate': dmDate?.millisecondsSinceEpoch,
      'dmStartTime': dmStartTime?.millisecondsSinceEpoch,
      'dmEndTime': dmEndTime?.millisecondsSinceEpoch,
      'planType': planType,
      'introVideoCount': introVideoCount,
      'introVideoDate': introVideoDate,
      'couplePrefrencePrimary': couplePrefrencePrimary,
      'couplePrefrenceSecond': couplePrefrenceSecond,
      'isCoupleAccount': isCoupleAccount,
      'joinedActivities': joinedActivities,
    };
  }

  factory UserProfileModel.fromMap(Map<String, dynamic> map) {
    return UserProfileModel(
        id: map['id'] as String,
        userId: map['userId'] as String,
        firstName: map['firstName'] as String,
        lastName: map['lastName'] as String,
        name: map['name'] != null ? map['name'] as String : null,
        address: map['address'] != null ? map['address'] as String : null,
        latitude: map['latitude'] != null ? map['latitude'] as double : null,
        longitude: map['longitude'] != null ? map['longitude'] as double : null,
        email: map['email'] != null ? map['email'] as String : null,
        profilePicture: map['profilePicture'] != null
            ? map['profilePicture'] as String
            : null,
        phoneNumber:
            map['phoneNumber'] != null ? map['phoneNumber'] as String : null,
        gender: map['gender'] as String,
        about: map['about'] != null ? map['about'] as String : null,
      birthDay: DateTime.fromMillisecondsSinceEpoch(map['birthDay']),
        featuredTime: map['featuredTime'] != null
            ? DateTime.fromMillisecondsSinceEpoch(map['featuredTime'])
            : null,
        dmDate: map['dmDate'] != null
            ? DateTime.fromMillisecondsSinceEpoch(map['dmDate'])
            : null,
        dmStartTime: map['dmStartTime'] != null
            ? DateTime.fromMillisecondsSinceEpoch(map['dmStartTime'])
            : null,
        dmEndTime: map['dmEndTime'] != null
            ? DateTime.fromMillisecondsSinceEpoch(map['dmEndTime'])
            : null,
        mediaFiles: List<String>.from(map['mediaFiles']!.map((x) => x)),
        interests: List<String>.from(map['interests']!.map((x) => x)),
        userAccountSettingsModel: UserAccountSettingsModel.fromMap(
            map['userAccountSettingsModel'] as Map<String, dynamic>),
        isVerified: map['isVerified'] as bool,
        isOnline: map['isOnline'] as bool,
        education: map['education'] != null ? map['education'] as String : null,
        drinking: map['drinking'] != null ? map['drinking'] as String : null,
        religion: map['religion'] != null ? map['religion'] as String : null,
        pronounceList: map['pronounceList'] != null
            ? List<String>.from(map['pronounceList']!.map((x) => x))
            : null,
        imageList: map['imageList'] != null
            ? List<String>.from(map['imageList']!.map((x) => x))
            : null,
        bio: map['bio'] != null ? map['bio'] as String : null,
        work: map['work'] != null ? map['work'] as String : null,
        isNewUser: map['isNewUser'] != null ? map['isNewUser'] as bool : null,
        isPremiumUser:
            map['isPremiumUser'] != null ? map['isPremiumUser'] as bool : null,
        isPremiumPlusUser: map['isPremiumPlusUser'] != null
            ? map['isPremiumPlusUser'] as bool
            : null,
        isDMOn: map['isDMOn'] != null ? map['isDMOn'] as bool : null,
        isFeaturedOn:
            map['isFeaturedOn'] != null ? map['isFeaturedOn'] as bool : null,
        preferences: map['preferences'] != null
            ? map['preferences'] as Map<String, dynamic>?
            : null,
        favouriteIdList: map['favouriteIdList'] != null
            ? List<String>.from(map['favouriteIdList']!.map((x) => x))
            : null,
        planType: map['planType'] != null ? map["planType"] as int : -1,
        introVideoDate: map['introVideoDate'] != null
            ? map["introVideoDate"] as String
            : "",
        introVideoCount:
            map['introVideoCount'] != null ? map["introVideoCount"] as int : 0,
        couplePrefrencePrimary: map['couplePrefrencePrimary'] != null
            ? map['couplePrefrencePrimary'] as Map<String, dynamic>?
            : null,
        couplePrefrenceSecond: map['couplePrefrenceSecond'] != null
            ? map['couplePrefrenceSecond'] as Map<String, dynamic>?
            : null,
        isCoupleAccount: map['isCoupleAccount'] != null
            ? map['isCoupleAccount'] as bool
            : false,
        joinedActivities: map['joinedActivities'] != null
            ? List<String>.from(map['joinedActivities']!.map((x) => x))
            : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory UserProfileModel.fromJson(String source) =>
      UserProfileModel.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'UserProfileModel(id: $id, userId: $userId, firstName: $firstName, lastName: $lastName, email: $email, profilePicture: $profilePicture, phoneNumber: $phoneNumber, gender: $gender, about: $about, birthDay: $birthDay, mediaFiles: $mediaFiles, interests: $interests, userAccountSettingsModel: $userAccountSettingsModel, isVerified: $isVerified, isOnline: $isOnline, education: $education, religion: $religion, cannabis: $cannabis, pronounceList: $pronounceList,imageList : $imageList, bio: $bio, work: $work, isDMOn: $isDMOn, isFeaturedOn: $isFeaturedOn, featuredTime $featuredTime, dmDate $dmDate, dmStartTime $dmStartTime dmEndTime $dmEndTime, planType $planType, introVideoCount $introVideoCount, introVideoDate $introVideoDate, couplePrefrenceSecond $couplePrefrenceSecond, couplePrefrencePrimary $couplePrefrencePrimary, isCoupleAccount $isCoupleAccount)';
  }

  @override
  bool operator ==(covariant UserProfileModel other) {
    if (identical(this, other)) return true;

    return other.id == id &&
        other.userId == userId &&
        other.firstName == firstName &&
        other.lastName == lastName &&
        other.address == address &&
        other.email == email &&
        other.profilePicture == profilePicture &&
        other.phoneNumber == phoneNumber &&
        other.gender == gender &&
        other.about == about &&
        other.birthDay == birthDay &&
        other.featuredTime == featuredTime &&
        listEquals(other.mediaFiles, mediaFiles) &&
        listEquals(other.interests, interests) &&
        other.userAccountSettingsModel == userAccountSettingsModel &&
        other.isVerified == isVerified &&
        other.isOnline == isOnline &&
        other.education == education &&
        other.drinking == drinking &&
        other.religion == religion &&
        other.cannabis == cannabis &&
        listEquals(other.pronounceList, pronounceList) &&
        listEquals(other.imageList, imageList) &&
        other.bio == bio &&
        other.work == work &&
        other.isNewUser == isNewUser &&
        other.isPremiumUser == isPremiumUser &&
        other.isPremiumPlusUser == isPremiumPlusUser &&
        other.isDMOn == isDMOn &&
        other.isFeaturedOn == isFeaturedOn &&
        listEquals(other.favouriteIdList, favouriteIdList) &&
        other.preferences == preferences &&
        other.dmDate == dmDate &&
        other.dmStartTime == dmStartTime &&
        other.planType == planType &&
        other.introVideoDate == introVideoDate &&
        other.introVideoCount == introVideoCount &&
        other.couplePrefrencePrimary == couplePrefrencePrimary &&
        other.couplePrefrenceSecond == couplePrefrenceSecond &&
        other.isCoupleAccount == isCoupleAccount &&
        other.dmEndTime == dmEndTime &&
        listEquals(other.joinedActivities, joinedActivities);
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        firstName.hashCode ^
        lastName.hashCode ^
        address.hashCode ^
        email.hashCode ^
        profilePicture.hashCode ^
        phoneNumber.hashCode ^
        gender.hashCode ^
        about.hashCode ^
        birthDay.hashCode ^
        featuredTime.hashCode ^
        mediaFiles.hashCode ^
        interests.hashCode ^
        userAccountSettingsModel.hashCode ^
        isVerified.hashCode ^
        isOnline.hashCode ^
        education.hashCode ^
        drinking.hashCode ^
        religion.hashCode ^
        pronounceList.hashCode ^
        imageList.hashCode ^
        bio.hashCode ^
        work.hashCode ^
        isNewUser.hashCode ^
        isPremiumUser.hashCode ^
        isPremiumPlusUser.hashCode ^
        isDMOn.hashCode ^
        isFeaturedOn.hashCode ^
        favouriteIdList.hashCode ^
        preferences.hashCode ^
        dmDate.hashCode ^
        dmStartTime.hashCode ^
        planType.hashCode ^
        introVideoDate.hashCode ^
        introVideoCount.hashCode ^
        couplePrefrencePrimary.hashCode ^
        couplePrefrenceSecond.hashCode ^
        isCoupleAccount.hashCode ^
        dmEndTime.hashCode ^
        joinedActivities.hashCode;
  }
}

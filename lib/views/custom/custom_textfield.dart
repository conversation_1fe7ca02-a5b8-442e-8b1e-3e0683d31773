import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../helpers/constants.dart';

class CustomTextField extends StatelessWidget {
  final String? camefromScreen;
  final TextEditingController? controller;
  final TextInputType? keyboardType;
  final bool readOnly;
  final bool obscureText;
  final String? labelText;
  final void Function(String str)? onChangedValue;
  final List<TextInputFormatter>? inputFormatters;
  final int? maxLines;
  final FormFieldValidator<String>? validator;
  final int? inputCharacterLimit;
  final AutovalidateMode? autovalidateMode;
  final TextInputAction? textInputAction;
  final EdgeInsetsGeometry? contentPadding;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final BoxConstraints? suffixIconConstraints;
  final String? prefixText;
  final String? hintText;
  final Color? hintColor;
  final Color? cursorColor;
  final bool? isenabled;
  final InputDecoration? decoration;
  final Function()? onTap;
  final void Function(String str)? onSubmitted;
  final int? maxLength;
  final TextAlign textAlign;
  final Color? lableColor;
  final Color? fillColor;
  final Color borderColor;
  final bool autoValidate;
  const CustomTextField(
      {super.key,
      this.controller,
      this.keyboardType,
      this.readOnly = false,
      this.labelText,
      this.validator,
      this.inputCharacterLimit,
      this.autovalidateMode,
      this.textInputAction,
      this.prefixIcon,
      this.prefixText,
      this.suffixIcon,
      this.suffixIconConstraints,
      this.obscureText = false,
      this.onChangedValue,
      this.inputFormatters,
      this.maxLines = 1,
      this.camefromScreen,
      this.isenabled,
      this.onSubmitted,
      this.onTap,
      this.hintText,
      this.cursorColor,
      this.contentPadding,
      this.decoration,
      this.maxLength,
      this.hintColor,
      this.textAlign = TextAlign.start,
      this.lableColor,
      this.fillColor = Colors.transparent,
      this.borderColor = const Color(0xFFD1D1D1),
      this.autoValidate = false});

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      enabled: isenabled,
      onTap: onTap,
      controller: controller,
      obscureText: obscureText,
      keyboardType: keyboardType ?? TextInputType.text,
      readOnly: readOnly,
      validator: validator,
      textInputAction: textInputAction ?? TextInputAction.next,
      onChanged: onChangedValue,
      cursorColor: cursorColor ?? AppConstants.primaryTextColor,
      inputFormatters: inputFormatters,
      autovalidateMode: autoValidate
          ? AutovalidateMode.onUserInteraction
          : AutovalidateMode.disabled,
      onFieldSubmitted: onSubmitted,
      style: TextStyle(
        color: lableColor ?? AppConstants.primaryTextColor,
        fontSize: 16,
      ),
      maxLines: maxLines,
      onTapOutside: (event) {
        FocusScope.of(context).unfocus();
      },
      decoration: InputDecoration(
        errorStyle: TextStyle(
          fontSize: 12,
          color: Color(0xffff0033),
        ),
        hintText: hintText,
        hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: hintColor ?? Colors.transparent,
              fontSize: 16,
            ),
        suffixIcon: suffixIcon,
        prefixIcon: prefixIcon,
        prefixText: prefixText,
        labelText: labelText,
        isDense: true,
        contentPadding: contentPadding,
        errorMaxLines: 3,
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 1.0),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 1.0),
        ),
        labelStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: lableColor,
              fontSize: 16,
            ),
        filled: true,
        fillColor: fillColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: borderColor, width: 1.0),
        ),
        enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: borderColor, width: 1.0),
            borderRadius: BorderRadius.circular(12.0)),
        focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: borderColor, width: 1.0),
            borderRadius: BorderRadius.circular(12.0)),
      ),
    );
  }
}

class CustomTextField2 extends StatelessWidget {
  final String? camefromScreen;
  final TextEditingController? controller;
  final TextInputType? keyboardType;
  final bool readOnly;
  final bool obscureText;
  final String? labelText;
  final void Function(String str)? onChangedValue;
  final List<TextInputFormatter>? inputFormatters;
  final int? maxLines;
  final int? minLines;
  final FormFieldValidator<String>? validator;
  final int? inputCharacterLimit;
  final AutovalidateMode? autovalidateMode;
  final TextInputAction? textInputAction;
  final EdgeInsetsGeometry? contentPadding;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final BoxConstraints? suffixIconConstraints;
  final String? prefixText;
  final String? hintText;
  final Color? hintColor;
  final Color? cursorColor;
  final bool? isenabled;
  final InputDecoration? decoration;
  final Function()? onTap;
  final void Function(String str)? onSubmitted;
  final int? maxLength;
  final TextAlign textAlign;
  final Color? lableColor;
  final Color? fillColor;
  final Color borderColor;
  final FloatingLabelBehavior? floatingLabelBehavior;
  const CustomTextField2(
      {super.key,
      this.controller,
      this.keyboardType,
      this.readOnly = false,
      this.labelText,
      this.validator,
      this.inputCharacterLimit,
      this.autovalidateMode,
      this.textInputAction,
      this.prefixIcon,
      this.prefixText,
      this.suffixIcon,
      this.suffixIconConstraints,
      this.obscureText = false,
      this.onChangedValue,
      this.inputFormatters,
      this.maxLines,
      this.camefromScreen,
      this.isenabled,
      this.onSubmitted,
      this.onTap,
      this.hintText,
      this.cursorColor,
      this.contentPadding,
      this.decoration,
      this.maxLength,
      this.minLines,
      this.hintColor,
      this.floatingLabelBehavior,
      this.textAlign = TextAlign.start,
      this.lableColor = const Color(0xFF303030),
      this.fillColor = Colors.white,
      this.borderColor = const Color(0xFFD1D1D1)});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (labelText != null)
          Text(
            labelText ?? "",
            style: TextStyle(
              color: AppConstants.primaryTextColor,
              fontSize: 12,
              fontWeight: FontWeight.w400,
              // Additional InputDecoration properties...
            ),
          ),
        const SizedBox(
          height: 6,
        ),
        TextFormField(
          enabled: isenabled,
          onTap: onTap,
          controller: controller,
          obscureText: obscureText,
          keyboardType: keyboardType ?? TextInputType.text,
          readOnly: readOnly,
          validator: validator,
          textInputAction: textInputAction ?? TextInputAction.next,
          onChanged: onChangedValue,
          cursorColor: cursorColor ?? Colors.black,
          inputFormatters: inputFormatters,
          autovalidateMode: autovalidateMode ?? AutovalidateMode.disabled,
          onFieldSubmitted: onSubmitted,
          maxLength: maxLength,
          maxLines: maxLines,
          minLines: minLines,
          style: TextStyle(
            color: lableColor,
          ),
          decoration: InputDecoration(
            floatingLabelBehavior:
                floatingLabelBehavior ?? FloatingLabelBehavior.never,
            hintText: hintText,
            hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: hintColor ?? const Color(0xFFA4A4A4),
                ),
            suffixIcon: suffixIcon,
            suffixIconConstraints: suffixIconConstraints,
            prefixIcon: prefixIcon,
            prefixText: prefixText,
            alignLabelWithHint: true,
            labelText: hintText,
            isDense: true,
            contentPadding: contentPadding,
            errorMaxLines: 3,
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red, width: 1.0),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red, width: 1.0),
            ),
            labelStyle: Theme.of(context)
                .textTheme
                .bodyMedium
                ?.copyWith(color: hintColor ?? const Color(0xFFA4A4A4)),
            filled: true,
            fillColor: fillColor,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: borderColor, width: 1.0),
            ),
            enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: borderColor, width: 1.0),
                borderRadius: BorderRadius.circular(12.0)),
            focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: borderColor, width: 1.0),
                borderRadius: BorderRadius.circular(12.0)),
          ),
        ),
      ],
    );
  }
}

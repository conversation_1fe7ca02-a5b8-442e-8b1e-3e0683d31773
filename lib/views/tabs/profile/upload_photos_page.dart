import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../helpers/constants.dart';
import '../../custom/custom_button.dart';

class UploadPhotosPage extends StatelessWidget {
  final List<String> imageList;
  final void Function() onAddImage;
  final void Function(int index) onRemoveImage;
  final void Function() onContinue;
  const UploadPhotosPage(
      {super.key,
      required this.imageList,
      required this.onAddImage,
      required this.onRemoveImage,
      required this.onContinue});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 24, left: 16, right: 16),
            child: Wrap(
              spacing: 12,
              runSpacing: 12,
              alignment: WrapAlignment.start,
              children: List.generate(
                  imageList.length == 6 ? 6 : imageList.length + 1, (index) {
                String image = '';
                if (imageList.isNotEmpty && imageList.length > index) {
                  image = imageList[index];
                }
                return SizedBox(
                    width: MediaQuery.of(context).size.width * 0.44,
                    height: 220,
                    child: Container(
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                      ),
                      child: Center(
                        child: image.isEmpty
                            ? InkWell(
                                onTap: onAddImage,
                                child: Container(
                                  width: 164,
                                  height: 220,
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    color: AppConstants.grayF1F1F1,
                                    border: Border.all(
                                      color: AppConstants.gray737373,
                                    ),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Center(
                                    // Center it inside the container
                                    child: SvgPicture.asset(
                                      AppConstants.addImage,
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                              )
                            : Uri.parse(image).isAbsolute
                                ? Container(
                                    decoration: const BoxDecoration(
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(10)),
                                        color: Colors.black12),
                                    child: Center(
                                      child: CachedNetworkImage(
                                        imageUrl: image,
                                        placeholder: (context, url) =>
                                            const Center(
                                                child: CircularProgressIndicator
                                                    .adaptive()),
                                        errorWidget: (context, url, error) =>
                                            Center(
                                                child: SvgPicture.asset(
                                                    AppConstants.addImage)),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  )
                                : Stack(children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(10),
                                      child: Image.file(File(image),
                                          width: 164,
                                          height: 220,
                                          fit: BoxFit.cover),
                                    ),
                                    Positioned(
                                      right: 10,
                                      top: 10,
                                      child: InkWell(
                                          onTap: () => onRemoveImage(index),
                                          child: SvgPicture.asset(
                                              AppConstants.closeSmall)),
                                    ),
                                    Positioned(
                                        //<-- SEE HERE
                                        left: 10,
                                        bottom: 10,
                                        child: Container(
                                            alignment: Alignment.center,
                                            height: 28,
                                            width: 28,
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(20),
                                                color: Colors.black
                                                    .withOpacity(0.2)),
                                            child: Text(
                                              (index + 1).toString(),
                                              style: const TextStyle(
                                                  fontWeight: FontWeight.w600,
                                                  fontSize: 10,
                                                  color: Colors.white),
                                            ))),
                                    if (index == 0)
                                      Positioned(
                                        //<-- SEE HERE
                                        right: 10,
                                        bottom: 10,
                                        child: Container(
                                          padding: const EdgeInsets.all(5),
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius:
                                                BorderRadius.circular(25),
                                            border: Border.all(
                                              color: Colors.black,
                                            ),
                                          ),
                                          child: SvgPicture.asset(
                                              height: 24,
                                              width: 24,
                                              AppConstants.accountIconGreen),
                                        ),
                                      ),
                                  ]),
                      ),
                    ));
              }),
            ),
          ),
          Container(
              margin: EdgeInsets.only(left: 16, right: 16, top: 32, bottom: 24),
              child: CustomButton(
                text: "Continue",
                onPressed: onContinue,
              )),
          const SizedBox(height: AppConstants.defaultNumericValue * 2),
        ],
      ),
    );
  }
}

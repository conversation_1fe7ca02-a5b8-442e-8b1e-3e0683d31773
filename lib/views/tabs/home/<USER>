import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/views/tabs/home/<USER>';
import 'package:fringle_app/views/tabs/home/<USER>';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class FavoriteList extends ConsumerStatefulWidget {
  const FavoriteList({super.key});

  @override
  ConsumerState<FavoriteList> createState() => _FavoriteListState();
}

class _FavoriteListState extends ConsumerState<FavoriteList>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_handleTabSelection);
  }

  void _handleTabSelection() {
    setState(() {
      selectedIndex = _tabController.index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: PreferredSize(
          preferredSize:
              const Size.fromHeight(60), // Add 20 pixels for the top distance
          child: AppBar(
            backgroundColor: Colors.white,
            bottom: TabBar(
              controller: _tabController,
              unselectedLabelColor: Colors.black,
              indicatorSize: TabBarIndicatorSize.tab,
              indicator: const UnderlineTabIndicator(
                borderSide: BorderSide(color: Color(0xFFC70973), width: 3.0),
              ),
              tabs: [
                Tab(
                  height: 50,
                  child: Container(
                    child: Align(
                      alignment: Alignment.center,
                      child: Center(
                          child: Text(
                        'Like Me',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: (selectedIndex == 0)
                                ? Colors.black
                                : const Color(0xFF737373)),
                      )),
                    ),
                  ),
                ),
                Tab(
                  height: 50,
                  child: Container(
                    child: Align(
                      child: Text(
                        'Favorites',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontFamily: AppConstants.fontStyleName,
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: (selectedIndex == 1)
                                ? Colors.black
                                : const Color(0xFF737373)),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
        body: GestureDetector(
          onHorizontalDragEnd: (details) {
            if (details.primaryVelocity! > 0) {
              // Swiped from left to right
              if (selectedIndex == 1) {
                // Only switch to the previous tab if the current tab is not the first tab
                _tabController.animateTo(0);
              }
            } else if (details.primaryVelocity! < 0) {
              // Swiped from right to left
              if (selectedIndex == 0) {
                // Only switch to the next tab if the current tab is not the last tab
                _tabController.animateTo(1);
              }
            }
          },
          child: TabBarView(
            controller: _tabController,
            physics:
                const NeverScrollableScrollPhysics(), // Disable swiping between tabs
            children: const [
              TabLikeMe(),
              TabFavourite(),
            ],
          ),
        ),
      ),
    );
  }
}

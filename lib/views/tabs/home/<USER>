import 'dart:async';
import 'dart:ui';

import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/models/user_profile_model.dart';
import 'package:fringle_app/providers/other_users_provider.dart';
import 'package:fringle_app/providers/user_profile_provider.dart';
import 'package:fringle_app/views/others/user_details_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';

class TabFavourite extends ConsumerStatefulWidget {
  const TabFavourite({super.key});

  @override
  ConsumerState<TabFavourite> createState() => _TabFavouriteState();
}

class _TabFavouriteState extends ConsumerState<TabFavourite>
    with WidgetsBindingObserver {
  bool isPrimeUser = false;
  bool isPrimePlusUser = false;
  List<String>? favouriteIdList = [];
  List<String>? usersListIds = [];
  List<UserProfileModel> usersList = [];
  List<UserProfileModel> FavouriteusersList = [];
  var isProgress = true;

  // @override
  // void initState() {
  //       WidgetsBinding.instance.addObserver(this);

  //  // getFavouriteData();

  //   super.initState();
  // }

  //  @override
  // void dispose() {
  //   WidgetsBinding.instance.removeObserver(this);
  //   super.dispose();
  // }

  getFavouriteData() {
    favouriteIdList?.clear();
    usersListIds?.clear();
    usersList.clear();
    FavouriteusersList.clear();

    final otherUsers = ref.watch(otherUsersProvider);

    otherUsers.whenData((value) {
      usersList.addAll(value);

      for (var user in usersList) {
        usersListIds?.add(user.userId);
      }
    });

    final user = ref.read(userProfileFutureProvider);

    return user.when(
      data: (data) {
        if (data != null) {
          isPrimeUser = data.isPremiumUser ?? false;
          isPrimePlusUser = data.isPremiumPlusUser ?? false;
          if (data.favouriteIdList != null) {
            data.favouriteIdList?.asMap().forEach((index, value) {
              for (var _user in usersList) {
                if (_user.userId == value) {
                  FavouriteusersList.add(_user);
                }
              }
            });
          }
        }
      },
      error: (_, __) => const SizedBox(),
      loading: () => const SizedBox(),
    );
  }

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      getFavouriteData();
      ref.invalidate(userProfileFutureProvider);
      ref.invalidate(otherUsersProvider);
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // getFavouriteData();
    Future.delayed(const Duration(seconds: 7), () {
      isProgress = false;
      setState(() {});
    });

    // return isProgress
    //     ? const ShimmerListWithImg()
    //     :
    return Container(
        color: Colors.white,
        child: PageView(
          physics: const NeverScrollableScrollPhysics(),
          children: [
            SingleChildScrollView(
                // padding: const EdgeInsets.all(
                //     AppConstants.defaultNumericValue * 2),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                  // Padding(
                  // padding: const EdgeInsets.symmetric(
                  //  horizontal: AppConstants.defaultNumericValue),
                  const SizedBox(
                    height: 24,
                  ),
                  // Visibility(
                  //   visible: !(isPrimeUser || isPrimePlusUser),
                  //   child: InkWell(
                  //     onTap: () {
                  //       Navigator.of(context)
                  //           .push(MaterialPageRoute(builder: (context) {
                  //         return MembershipScreen(
                  //           isPremiumUser: isPrimeUser,
                  //           isPremiumPlusUser: isPrimePlusUser,
                  //         );
                  //       }));
                  //     },
                  //     child: Container(
                  //       margin: const EdgeInsets.symmetric(
                  //           horizontal: 24, vertical: 16),
                  //       padding: const EdgeInsets.all(10),
                  //       decoration: BoxDecoration(
                  //         borderRadius: BorderRadius.circular(20),
                  //         gradient: const LinearGradient(
                  //           begin: Alignment.topCenter,
                  //           end: Alignment.bottomCenter,
                  //           colors: [Color(0xFFC70973), Color(0xFF46239F)],
                  //         ),
                  //       ),
                  //       child: const Center(
                  //           child: Text(
                  //         "Upgrade to view profile",
                  //         style: TextStyle(
                  //             color: Colors.white,
                  //             fontWeight: FontWeight.w400,
                  //             fontSize: 12),
                  //       )),
                  //     ),
                  //   ),
                  // ),
                  FavouriteusersList.isEmpty
                      ? const Center(child: Text("No Favorites found"))
                      : Container(
                          margin: const EdgeInsets.only(
                              top: 24, left: 24, right: 24),
                          child: Wrap(
                            spacing: 12,
                            runSpacing: 12,
                            alignment: WrapAlignment.start,
                            children: List.generate(FavouriteusersList.length,
                                (index) {
                              String image = '';
                              String name = '';
                              String age = '';
                              //String userId = '';
                              UserProfileModel user = FavouriteusersList[index];

                              if (FavouriteusersList.isNotEmpty &&
                                  FavouriteusersList.length > index) {
                                user = FavouriteusersList[index];
                                //userId = FavouriteusersList[index].userId;
                                if (FavouriteusersList[index].profilePicture !=
                                    null) {
                                  image =
                                      FavouriteusersList[index].profilePicture!;
                                }

                                name = "${FavouriteusersList[index].firstName} ${FavouriteusersList[index].lastName}";

                                if (FavouriteusersList[index].birthDay !=
                                    null) {
                                  age = (DateTime.now()
                                              .difference(
                                                  FavouriteusersList[index]
                                                      .birthDay!)
                                              .inDays ~/
                                          365)
                                      .toString();
                                }
                              }

                              return Container(
                                child: SizedBox(
                                    width:
                                        MediaQuery.of(context).size.width * 0.4,
                                    height: 220,
                                    child: Container(
                                      decoration: const BoxDecoration(
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(10)),
                                      ),
                                      child: Center(
                                        child: Stack(children: [
                                          Container(
                                            child: ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              child: InkWell(
                                                onTap: () {
                                                  if ((isPrimeUser == true) ||
                                                      (isPrimePlusUser ==
                                                          true)) {
                                                    Navigator.of(context).push(
                                                      MaterialPageRoute(
                                                          builder: (context) =>
                                                              UserDetailsPage(
                                                                user: user,
                                                                isInterected:
                                                                    true,
                                                                likeDislike:
                                                                    "favourited",
                                                                isPrimeUser:
                                                                    isPrimeUser,
                                                                isPrimePlusUser:
                                                                    isPrimePlusUser,
                                                              )),
                                                    );
                                                  } else {
                                                    // showDialog(
                                                    //     context: context,
                                                    //     builder: (BuildContext
                                                    //         mContext) {
                                                    //       return CustomConfirmationDialog(
                                                    //         title:
                                                    //             "Unlock Premium Features",
                                                    //         desciption: AppConstants
                                                    //             .subscriptionDesc,
                                                    //         onButtonClick: () {
                                                    //           Navigator.of(
                                                    //                   context)
                                                    //               .push(MaterialPageRoute(
                                                    //                   builder:
                                                    //                       (context) {
                                                    //             return MembershipScreen(
                                                    //               isPremiumUser:
                                                    //                   isPrimeUser,
                                                    //               isPremiumPlusUser:
                                                    //                   isPrimePlusUser,
                                                    //             );
                                                    //           }));
                                                    //         },
                                                    //       );
                                                    //     });
                                                  }
                                                },
                                                child: Image.network(
                                                  image,
                                                  width: 166,
                                                  height: 220,
                                                  fit: BoxFit.cover,
                                                  loadingBuilder: (context,
                                                      child, loadingProgress) {
                                                    if (loadingProgress == null)
                                                      return child;
                                                    return const Center(
                                                        child:
                                                            CircularProgressIndicator
                                                                .adaptive());
                                                  },
                                                ),
                                              ),
                                            ),
                                          ),
                                          Positioned(
                                            top: 12,
                                            right: 12,
                                            child: SvgPicture.asset(
                                              FavouriteusersList[index]
                                                          .isDMOn ??
                                                      false
                                                  ? AppConstants.dmOffIcon
                                                  : AppConstants.dmOnIcon,
                                              // height: 15,
                                            ),
                                          ),
                                          Positioned(
                                              //<-- SEE HERE
                                              left: 10,
                                              right: 10,
                                              bottom: 15,
                                              child: Container(
                                                  alignment: Alignment.center,
                                                  height: 40,
                                                  width: 136,
                                                  decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              10),
                                                      color:
                                                          Colors.transparent),
                                                  child: ClipRRect(
                                                    borderRadius:
                                                        const BorderRadius.all(
                                                      Radius.circular(8),
                                                    ),
                                                    child: BackdropFilter(
                                                        filter:
                                                            ImageFilter.blur(
                                                                sigmaX: 8.0,
                                                                sigmaY: 8.0),
                                                        child: Container(
                                                            width:
                                                                double.infinity,
                                                            decoration:
                                                                const BoxDecoration(
                                                                    gradient:
                                                                        LinearGradient(
                                                              colors: [
                                                                Colors
                                                                    .transparent,
                                                                Colors
                                                                    .transparent,
                                                                Colors
                                                                    .transparent,
                                                              ],
                                                              begin: Alignment
                                                                  .centerLeft,
                                                              end: Alignment
                                                                  .centerRight,
                                                            )),
                                                            child: Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .all(8.0),
                                                              child: Row(
                                                                children: [
                                                                  Text(
                                                                    name.split(
                                                                        " ")[0],
                                                                    style: const TextStyle(
                                                                        fontWeight:
                                                                            FontWeight
                                                                                .w600,
                                                                        fontSize:
                                                                            14,
                                                                        color: Colors
                                                                            .white),
                                                                  ),
                                                                  const SizedBox(
                                                                      width: 5),
                                                                  Text(
                                                                    age,
                                                                    style: const TextStyle(
                                                                        fontWeight:
                                                                            FontWeight
                                                                                .w600,
                                                                        fontSize:
                                                                            14,
                                                                        color: Colors
                                                                            .white),
                                                                  ),
                                                                ],
                                                              ),
                                                            ))),
                                                  )))
                                        ]),
                                      ),
                                    )),
                              );
                            }),
                          ),
                        ),
                ])),
          ],
        ));
  }
}

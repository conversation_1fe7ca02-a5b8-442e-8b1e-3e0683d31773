// ignore_for_file: use_build_context_synchronously
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../helpers/constants.dart';
import '../../../helpers/custom_cache_image.dart';
import '../../../helpers/string_extensions.dart';
import '../../../providers/auth_providers.dart';
import '../../../providers/user_profile_provider.dart';
import '../../auth/login_page.dart';
import '../../custom/custom_button.dart';
import '../../others/user_details_page.dart';

class ProfilePage extends ConsumerStatefulWidget {
  const ProfilePage({super.key});

  @override
  ConsumerState<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends ConsumerState<ProfilePage> {
  int _currentIndex = 0;

  Widget _buildDots(List<String> imageList) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List<Widget>.generate(
        imageList.length,
        (int index) {
          return Container(
            width: 48,
            height: 3,
            margin: const EdgeInsets.symmetric(horizontal: 3),
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(10)),
              shape: BoxShape.rectangle,
              color: _currentIndex == index
                  ? Colors.white
                  : const Color.fromARGB(91, 0, 0, 0),
            ),
          );
        },
      ),
    );
  }

  Widget _iconTile({
    required String text,
    String? iconPath,
    double iconSize = 20,
    Widget? iconWidget,
    double? gap,
    Color? iconColor,
    Color? textColor,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (iconWidget != null) ...[
          iconWidget,
        ] else if (iconPath != null) ...[
          SvgPicture.asset(
            iconPath,
            height: iconSize,
            width: iconSize,
            color: iconColor,
          ),
        ],
        if (gap != null && (iconWidget != null || iconPath != null)) ...[
          SizedBox(width: gap),
        ],
        Text(
          text,
          style: GoogleFonts.manrope(
            color: textColor ?? AppConstants.primaryTextColor,
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final userProfileRef = ref.watch(userProfileFutureProvider);
    return Scaffold(
      backgroundColor: Colors.white,
      body: userProfileRef.when(
        data: (user) {
          if (user == null) {
            return Center(child: Text("Error loading user profile", style: GoogleFonts.manrope()));
          }
          final String genderIcon = switch (user.gender.toLowerCase()) {
            'male' => AppConstants.maleSharpIcon,
            'female' => AppConstants.femaleIcon,
            'other' => AppConstants.transgenderIcon,
            'non-binary' => AppConstants.nonbinaryIcon,
            _ => AppConstants.maleIcon,
          };
          return SingleChildScrollView(
            child: Column(
              children: [
                Stack(
                  children: [
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(14),
                        topRight: Radius.circular(14),
                      ),
                      child: SizedBox(
                        height: MediaQuery.of(context).size.height / 1.5,
                        width: MediaQuery.of(context).size.width,
                        child: Stack(
                          children: [
                            if (user.imageList != null && user.imageList!.isNotEmpty && _currentIndex < user.imageList!.length)
                              CustomCacheImage(
                                key: Key(user.imageList?[_currentIndex] ?? ""),
                                imageUrl: user.imageList?[_currentIndex] ?? "",
                                height: MediaQuery.of(context).size.height / 1.5,
                                width: MediaQuery.of(context).size.width,
                                fit: BoxFit.cover,
                                errorWidget: const Center(child: Icon(CupertinoIcons.photo)),
                              ),
                            SizedBox(
                              height: MediaQuery.of(context).size.height / 2,
                              width: MediaQuery.of(context).size.width,
                              child: Stack(
                                children: [
                                  Positioned(
                                    bottom: 16,
                                    left: 0,
                                    right: 0,
                                    child: _buildDots(user.imageList ?? []),
                                  )
                                ],
                              ),
                            ),
                            Positioned.fill(
                              child: Row(
                                children: [
                                  Expanded(
                                    child: GestureDetector(
                                      onTap: () {
                                        if (_currentIndex > 0) {
                                          setState(() {
                                            _currentIndex--;
                                          });
                                        }
                                      },
                                      child: Container(
                                        color: Colors.transparent,
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: GestureDetector(
                                      onTap: () {
                                        if (_currentIndex < (user.imageList?.length ?? 0) - 1) {
                                          setState(() {
                                            _currentIndex++;
                                          });
                                        }
                                      },
                                      child: Container(
                                        color: Colors.transparent,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                      left: AppConstants.defaultNumericValue,
                      top: MediaQuery.of(context).padding.top + 10,
                      right: AppConstants.defaultNumericValue,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          GestureDetector(
                            onTap: () => Navigator.pop(context),
                            child: Container(
                              padding: EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Color(0xFF000000).withOpacity(0.15),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.arrow_back_ios_new_rounded,
                                color: Colors.white,
                                size: 18,
                              ),
                            ),
                          ),
                          PopupMenuButton<UserOptions>(
                            icon: Container(
                            padding: EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Color(0xFF000000).withOpacity(0.15),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.more_vert_rounded,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                            color: Colors.white,
                            position: PopupMenuPosition.under,
                            padding: EdgeInsets.zero,
                            menuPadding: EdgeInsets.zero,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            onSelected: (value) {
                              // TODO: Fix this
                            },
                            itemBuilder: (context) => [
                              PopupMenuItem(
                                padding: EdgeInsets.symmetric(horizontal: 16),
                                value: UserOptions.report,
                                child: _iconTile(
                                  text: "Account Settings",
                                  iconPath: AppConstants.settingsIcon,
                                  gap: 10,
                                ),
                              ),
                              PopupMenuItem(
                                padding: EdgeInsets.symmetric(horizontal: 16),
                                value: UserOptions.block,
                                child: _iconTile(
                                  text: "Edit Profile",
                                  iconPath: AppConstants.editProfile,
                                  gap: 10,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(
                        top: MediaQuery.of(context).size.height / 2,
                      ),
                      width: MediaQuery.of(context).size.width,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        // color: Color(0xFFFFFCF5),
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(24),
                          topRight: Radius.circular(24),
                        ),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultNumericValue),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: 20),
                          Text(
                            "${user.firstName?.toUpperCase()}, ${DateTime.now().difference(user.birthDay!).inDays ~/ 365}",
                            style: GoogleFonts.manrope(
                              color: AppConstants.primaryTextColor,
                              fontWeight: FontWeight.w800,
                              fontSize: 24,
                            ),
                          ),
                          SizedBox(height: 10),
                          _iconTile(
                            iconPath: AppConstants.occupationIcon,
                            text: user.work ?? "Not Set!",
                            gap: 10,
                          ),
                          SizedBox(height: 10),
                          _iconTile(
                            iconPath: AppConstants.locationOutline,
                            text: user.address ?? "Not Set!",
                            gap: 10,
                          ),
                          SizedBox(height: 20),
                          Divider(
                            color: Color(0xB0D9D9D9),
                            thickness: 1,
                          ),
                          SizedBox(height: 20),
                          Row(
                            children: [
                              _iconTile(
                                iconPath: AppConstants.pronounIcon,
                                text: user.pronounceList != null && user.pronounceList!.isNotEmpty
                                    ? user.pronounceList!.first
                                    : "Not Set!",
                                gap: 10,
                              ),
                              SizedBox(
                                height: 20,
                                width: 40,
                                child: VerticalDivider(
                                  color: Color(0xFFC7C7C7),
                                  thickness: 1,
                                ),
                              ),
                              _iconTile(
                                iconPath: genderIcon,
                                text: user.gender.isEmpty
                                    ? "Not Set!"
                                    : user.gender.capitalizeFirst(),
                                gap: 10,
                                iconSize: 24,
                                iconColor: AppConstants.primaryTextColor,
                              ),
                              SizedBox(
                                height: 20,
                                width: 40,
                                child: VerticalDivider(
                                  color: Color(0xFFC7C7C7),
                                  thickness: 1,
                                ),
                              ),
                              _iconTile(
                                iconPath: AppConstants.drinksIcon,
                                text: user.drinking ?? "Not Set!",
                                gap: 10,
                              ),
                            ],
                          ),
                          SizedBox(height: 20),
                          Row(
                            children: [
                              _iconTile(
                                // iconPath: AppConstants.drinksIcon,
                                text: user.cannabis ?? "Not Set!",
                                iconWidget: Image.asset(
                                  AppConstants.cannabisIcon,
                                  height: 20,
                                  width: 20,
                                ),
                                gap: 10,
                              ),
                              SizedBox(
                                height: 20,
                                width: 40,
                                child: VerticalDivider(
                                  color: Color(0xFFC7C7C7),
                                  thickness: 1,
                                ),
                              ),
                              _iconTile(
                                iconPath: AppConstants.religionIcon,
                                text: user.religion ?? "Not Set!",
                                gap: 10,
                              ),
                            ],
                          ),
                          SizedBox(height: 20),
                          Divider(
                            color: Color(0xB0D9D9D9),
                            thickness: 1,
                          ),
                          SizedBox(height: 16),
                          _iconTile(
                            iconPath: AppConstants.emailOutline,
                            text: user.email ?? "Not Set!",
                            gap: 10,
                          ),
                          SizedBox(height: 16),
                          Divider(
                            color: Color(0xB0D9D9D9),
                            thickness: 1,
                          ),
                          SizedBox(height: 16),
                          Text(
                            "About",
                            style: GoogleFonts.manrope(
                              color: Color(0xFF717171),
                              fontSize: 12,
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            user.bio == null ? "Not Set!" : user.bio!,
                            style: GoogleFonts.manrope(
                              color: AppConstants.primaryTextColor,
                              fontSize: 14,
                            ),
                          ),
                          SizedBox(height: 16),
                          Divider(
                            color: Color(0xB0D9D9D9),
                            thickness: 1,
                          ),
                          SizedBox(height: 16),
                          Text(
                            "Interests",
                            style: GoogleFonts.manrope(
                              color: Color(0xFF717171),
                              fontSize: 12,
                            ),
                          ),
                          SizedBox(height: 8),
                          Wrap(
                            spacing: 16,
                            runSpacing: 16,
                            crossAxisAlignment: WrapCrossAlignment.center,
                            children: [
                              if (user.interests.isNotEmpty) ...[
                                ...List.generate(
                                    user.interests.length * 2 - 1,
                                    (index) {
                                  if (index.isEven) {
                                    // Interest text
                                    final interest = user.interests[index ~/ 2];
                                    return Text(
                                      interest,
                                      style: GoogleFonts.manrope(
                                        color: AppConstants.primaryTextColor,
                                        fontSize: 14,
                                      ),
                                    );
                                  } else {
                                    // Dot separator
                                    return Container(
                                      height: 10,
                                      width: 10,
                                      decoration: BoxDecoration(
                                        color: AppConstants.primaryTextColor,
                                        shape: BoxShape.circle,
                                      ),
                                    );
                                  }
                                })
                              ] else ...[
                                Text(
                                  "Not Set!",
                                  style: GoogleFonts.manrope(
                                    color: AppConstants.primaryTextColor,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ],
                          )
                        ],
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.defaultNumericValue,
                    vertical: 16,
                  ),
                  child: CustomButton(
                    onPressed: () async {
                      await ref.read(authProvider).signOut();
                      Navigator.pushAndRemoveUntil(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const LoginPage(),
                        ),
                        (_) => false,
                      );
                    },
                    text: 'Logout',
                  ),
                ),
              ],
            ),
          );

        },
        error: (error, _) {
          return Center(child: Text("Error: $error"));
        },
        loading: () {
          return const Center(child: CircularProgressIndicator.adaptive());
        },
      ),
    );
  }

  // @override
  // Widget build(BuildContext context, ref) {
  //   WidgetsBinding.instance.addPostFrameCallback((_) {
  //     ref.invalidate(otherUsersProvider);
  //     ref.watch(otherUsersProvider);
  //   });

  //   final userProfileRef = ref.watch(userProfileFutureProvider);
  //   final dateFormat = DateFormat('MM/dd/yyyy');
  //   initPrefrence();
  //   return userProfileRef.when(
  //     data: (data) {
  //       interestsList.clear();
  //       proList.clear();
  //       for (var item in data?.interests ?? []) {
  //         interestsList.add(CustomCipsModel(
  //             id: (interestsList.length + 1),
  //             name: item.toString(),
  //             isSelected: true));
  //       }

  //       for (var item in data?.pronounceList ?? []) {
  //         proList.add(CustomCipsModel(
  //             id: (proList.length + 1),
  //             name: item.toString(),
  //             isSelected: true));
  //       }

  //       return data == null
  //           ? const Center(child: Text("Not Available"))
  //           : CustomStatusBarTheme(
  //               isLightTheme: true,
  //               child: SingleChildScrollView(
  //                 //padding: const EdgeInsets.only(top: 30),
  //                 child: Column(
  //                   children: [
  //                     Stack(children: [
  //                       ClipRRect(
  //                         borderRadius: const BorderRadius.only(
  //                             topLeft: Radius.circular(14),
  //                             topRight: Radius.circular(14)),
  //                         child: CustomGradientWidget(
  //                           child: Container(
  //                               padding: const EdgeInsets.only(top: 50),
  //                               height:
  //                                   MediaQuery.of(context).size.height / 1.5,
  //                               width: MediaQuery.of(context).size.width,
  //                               child: Column(
  //                                 // crossAxisAlignment: CrossAxisAlignment.center,
  //                                 children: [
  //                                   Stack(
  //                                     //mainAxisAlignment: MainAxisAlignment.center,
  //                                     children: [
  //                                       SizedBox(
  //                                         width:
  //                                             MediaQuery.of(context).size.width,
  //                                         height: 70,
  //                                         child: const Text("Profile",
  //                                             textAlign: TextAlign.center,
  //                                             style: TextStyle(
  //                                                 fontSize: 24,
  //                                                 fontWeight: FontWeight.w600,
  //                                                 color: Color.fromRGBO(
  //                                                     255, 255, 255, 1))),
  //                                       ),
  //                                       Positioned(
  //                                         left: MediaQuery.of(context)
  //                                                 .size
  //                                                 .width -
  //                                             70,
  //                                         right: 0,
  //                                         top: 0,
  //                                         child: InkWell(
  //                                           onTap: () {
  //                                             Navigator.push(
  //                                                 context,
  //                                                 MaterialPageRoute(
  //                                                     builder: (context) =>
  //                                                         AccountSettingsPage(
  //                                                           isPremiumPlusUser:
  //                                                               true,
  //                                                           isPremiumUser: true,
  //                                                         ),
  //                                                     fullscreenDialog: true));
  //                                           },
  //                                             child: Icon(Icons.settings)
  //                                         ),
  //                                       ),
  //                                     ],
  //                                   ),
  //                                   Stack(
  //                                     children: [
  //                                       Container(
  //                                         padding: const EdgeInsets.only(
  //                                             top: 70,
  //                                             left: 38,
  //                                             right: 38,
  //                                             bottom: 40),
  //                                         child: Container(
  //                                             width: MediaQuery.of(context)
  //                                                 .size
  //                                                 .width,
  //                                             //height: 152,
  //                                             padding: const EdgeInsets.only(
  //                                                 top: 100),
  //                                             decoration: BoxDecoration(
  //                                                 color: const Color(0xff080808)
  //                                                     .withAlpha(30),
  //                                                 borderRadius:
  //                                                     BorderRadius.circular(
  //                                                         14)),
  //                                             child: Padding(
  //                                               padding: const EdgeInsets.only(
  //                                                   bottom: 32),
  //                                               child: Text(
  //                                                   "${data.firstName} ${data.lastName}",
  //                                                   textAlign: TextAlign.center,
  //                                                   style: const TextStyle(
  //                                                       fontWeight:
  //                                                           FontWeight.w600,
  //                                                       fontSize: 24,
  //                                                       color: Colors.white)),
  //                                             )),
  //                                       ),
  //                                       Positioned(
  //                                           top: 20,
  //                                           left: 0,
  //                                           right: 0,
  //                                           child: Row(
  //                                               crossAxisAlignment:
  //                                                   CrossAxisAlignment.center,
  //                                               mainAxisAlignment:
  //                                                   MainAxisAlignment.center,
  //                                               children: [
  //                                                 Container(
  //                                                   height: 124,
  //                                                   width: 124,
  //                                                   decoration: BoxDecoration(
  //                                                       color: Colors.white,
  //                                                       borderRadius:
  //                                                           BorderRadius
  //                                                               .circular(100)),
  //                                                   child: Padding(
  //                                                     padding:
  //                                                         const EdgeInsets.all(
  //                                                             5.0),
  //                                                     child: CircleAvatar(
  //                                                       backgroundImage:
  //                                                           data.profilePicture !=
  //                                                                   null
  //                                                               ? CachedNetworkImageProvider(
  //                                                                   data.profilePicture!,
  //                                                                 )
  //                                                               : null,
  //                                                       child:
  //                                                           data.profilePicture ==
  //                                                                   null
  //                                                               ? const Icon(
  //                                                                   Icons
  //                                                                       .person,
  //                                                                   color: Colors
  //                                                                       .white54)
  //                                                               : null,
  //                                                     ),
  //                                                   ),
  //                                                 ),
  //                                               ])),
  //                                       Positioned(
  //                                           top: 100,
  //                                           left: 80,
  //                                           right: 0,
  //                                           child: InkWell(
  //                                             onTap: () {
  //                                               if (_prefs.getBool(AppConstants
  //                                                       .coupleAccount) ==
  //                                                   true) {
  //                                                 EasyLoading.showToast(
  //                                                     "You do not have the required authorization.");
  //                                                 return;
  //                                               }
  //                                               Navigator.push(
  //                                                   context,
  //                                                   MaterialPageRoute(
  //                                                       builder: (context) =>
  //                                                           EditProfilePage(
  //                                                               userProfileModel:
  //                                                                   data),
  //                                                       fullscreenDialog:
  //                                                           true));
  //                                             },
  //                                             child: SvgPicture.asset(
  //                                               AppConstants.editProfile,
  //                                               height: 60,
  //                                               width: 60,
  //                                             ),
  //                                           ))
  //                                     ],
  //                                   )
  //                                 ],
  //                               )

  //                               // Image.network(
  //                               //   data.profilePicture.toString(),
  //                               //   height:
  //                               //       MediaQuery.of(context).size.height / 1.5,
  //                               //   width: MediaQuery.of(context).size.width,
  //                               //   fit: BoxFit.cover,
  //                               //   loadingBuilder:
  //                               //       (context, child, loadingProgress) {
  //                               //     if (loadingProgress == null) return child;
  //                               //     return const Center(
  //                               //         child:
  //                               //             CircularProgressIndicator.adaptive());
  //                               //   },
  //                               //   errorBuilder: (context, error, stackTrace) {
  //                               //     return const Center(
  //                               //         child: Icon(CupertinoIcons.photo));
  //                               //   },
  //                               // ),

  //                               ),
  //                         ),
  //                       ),
  //                       // Positioned(
  //                       //     left: 10,
  //                       //     top: 10,
  //                       //     child: SvgPicture.asset(AppConstants.greenBack)),
  //                       Container(
  //                         margin: EdgeInsets.only(
  //                           top: MediaQuery.of(context).size.height / 2,
  //                         ),
  //                         width: MediaQuery.of(context).size.width,
  //                         decoration: const BoxDecoration(
  //                           // color: Theme.of(context).scaffoldBackgroundColor,
  //                           color: Colors.white,
  //                           borderRadius: BorderRadius.all(
  //                             Radius.circular(AppConstants.defaultNumericValue),
  //                           ),
  //                         ),
  //                         child: Column(
  //                             crossAxisAlignment: CrossAxisAlignment.start,
  //                             children: [
  //                               Padding(
  //                                 padding:
  //                                     const EdgeInsets.only(left: 40, top: 32),
  //                                 child: Column(
  //                                   crossAxisAlignment:
  //                                       CrossAxisAlignment.start,
  //                                   children: [
  //                                     GradientText(
  //                                       "Personal Information",
  //                                       gradientDirection:
  //                                           GradientDirection.ttb,
  //                                       colors: const [
  //                                         Color(0xFFC70973),
  //                                         Color(0xFF46239F),
  //                                       ],
  //                                       style: const TextStyle(
  //                                           fontSize: 18,
  //                                           fontWeight: FontWeight.w600),
  //                                     ),
  //                                   ],
  //                                 ),
  //                               ),
  //                               const SizedBox(
  //                                 height: 20,
  //                               ),
  //                               Padding(
  //                                 padding: const EdgeInsets.only(left: 24),
  //                                 child: Column(
  //                                     crossAxisAlignment:
  //                                         CrossAxisAlignment.start,
  //                                     children: [
  //                                       ListTile(
  //                                         title: const Text("Email",
  //                                             style: TextStyle(
  //                                                 color: Colors.black,
  //                                                 fontWeight: FontWeight.w500,
  //                                                 fontSize: 18)),
  //                                         subtitle: Text(
  //                                             data.email == null
  //                                                 ? "Not Set!"
  //                                                 : data.email!,
  //                                             style: const TextStyle(
  //                                                 color: Color(0xff737373),
  //                                                 fontWeight: FontWeight.w400,
  //                                                 fontSize: 14)),
  //                                       ),
  //                                       ListTile(
  //                                         title: const Text("Bio",
  //                                             style: TextStyle(
  //                                                 color: Colors.black,
  //                                                 fontWeight: FontWeight.w500,
  //                                                 fontSize: 18)),
  //                                         subtitle: Text(data.bio ?? "Not Set!",
  //                                             style: const TextStyle(
  //                                                 color: Color(0xff737373),
  //                                                 fontWeight: FontWeight.w400,
  //                                                 fontSize: 14)),
  //                                       ),
  //                                       ListTile(
  //                                         title: const Text("Date Of Birth",
  //                                             style: TextStyle(
  //                                                 color: Colors.black,
  //                                                 fontWeight: FontWeight.w500,
  //                                                 fontSize: 18)),
  //                                         subtitle: Text(
  //                                             data.birthDay == null
  //                                                 ? "Not Set!"
  //                                                 : dateFormat
  //                                                     .format(data.birthDay!),
  //                                             style: const TextStyle(
  //                                                 color: Color(0xff737373),
  //                                                 fontWeight: FontWeight.w400,
  //                                                 fontSize: 14)),
  //                                       ),
  //                                       ListTile(
  //                                         title: const Text("Gender",
  //                                             style: TextStyle(
  //                                                 color: Colors.black,
  //                                                 fontWeight: FontWeight.w500,
  //                                                 fontSize: 18)),
  //                                         subtitle: Text(
  //                                             data.gender ?? "Not Set!",
  //                                             style: const TextStyle(
  //                                                 color: Color(0xff737373),
  //                                                 fontWeight: FontWeight.w400,
  //                                                 fontSize: 14)),
  //                                       ),
  //                                       ListTile(
  //                                         title: const Text("Location",
  //                                             style: TextStyle(
  //                                                 color: Colors.black,
  //                                                 fontWeight: FontWeight.w500,
  //                                                 fontSize: 18)),
  //                                         subtitle: Text(
  //                                             data.address == null
  //                                                 ? "Not Set!"
  //                                                 : data.address!,
  //                                             style: const TextStyle(
  //                                                 color: Color(0xff737373),
  //                                                 fontWeight: FontWeight.w400,
  //                                                 fontSize: 14)),
  //                                       ),
  //                                       ListTile(
  //                                           title: const Text("Interests",
  //                                               style: TextStyle(
  //                                                   color: Colors.black,
  //                                                   fontWeight: FontWeight.w500,
  //                                                   fontSize: 18)),
  //                                           subtitle: (interestsList.isEmpty ==
  //                                                   false)
  //                                               ? null
  //                                               : const Text("Not Set!",
  //                                                   style: TextStyle(
  //                                                       color:
  //                                                           Color(0xff737373),
  //                                                       fontWeight:
  //                                                           FontWeight.w400,
  //                                                       fontSize: 14))),
  //                                       Padding(
  //                                         padding:
  //                                             const EdgeInsets.only(left: 10),
  //                                         child: Wrap(
  //                                           runSpacing: 8,
  //                                           spacing: 8,
  //                                           children: List.generate(
  //                                               interestsList.length, (index) {
  //                                             return ClipItemWidget(
  //                                               item: interestsList[index],
  //                                               borderRadious: 30,
  //                                               onSelectionChanged:
  //                                                   (isSelected) {},
  //                                             );
  //                                           }),
  //                                         ),
  //                                       ),
  //                                       ListTile(
  //                                         title: const Text("Work",
  //                                             style: TextStyle(
  //                                                 color: Colors.black,
  //                                                 fontWeight: FontWeight.w500,
  //                                                 fontSize: 18)),
  //                                         subtitle: Text(
  //                                             (data.work == null ||
  //                                                     data.work?.isEmpty ==
  //                                                         true)
  //                                                 ? "Not Set!"
  //                                                 : data.work!,
  //                                             style: const TextStyle(
  //                                                 color: Color(0xff737373),
  //                                                 fontWeight: FontWeight.w400,
  //                                                 fontSize: 14)),
  //                                       ),
  //                                       ListTile(
  //                                         title: const Text("Education",
  //                                             style: TextStyle(
  //                                                 color: Colors.black,
  //                                                 fontWeight: FontWeight.w500,
  //                                                 fontSize: 18)),
  //                                         subtitle: Text(
  //                                             data.education == null
  //                                                 ? "Not Set!"
  //                                                 : data.education!,
  //                                             style: const TextStyle(
  //                                                 color: Color(0xff737373),
  //                                                 fontWeight: FontWeight.w400,
  //                                                 fontSize: 14)),
  //                                       ),
  //                                       ListTile(
  //                                         title: const Text("Ethnicity",
  //                                             style: TextStyle(
  //                                                 color: Colors.black,
  //                                                 fontWeight: FontWeight.w500,
  //                                                 fontSize: 18)),
  //                                         subtitle: Text(
  //                                             data.religion == null
  //                                                 ? "Not Set!"
  //                                                 : data.religion!,
  //                                             style: const TextStyle(
  //                                                 color: Color(0xff737373),
  //                                                 fontWeight: FontWeight.w400,
  //                                                 fontSize: 14)),
  //                                       ),
  //                                       ListTile(
  //                                         title: const Text("Cannabis",
  //                                             style: TextStyle(
  //                                                 color: Colors.black,
  //                                                 fontWeight: FontWeight.w500,
  //                                                 fontSize: 18)),
  //                                         subtitle: Text(
  //                                             data.cannabis == null
  //                                                 ? "Not Set!"
  //                                                 : data.cannabis!,
  //                                             style: const TextStyle(
  //                                                 color: Color(0xff737373),
  //                                                 fontWeight: FontWeight.w400,
  //                                                 fontSize: 14)),
  //                                       ),
  //                                       ListTile(
  //                                           title: const Text("Pronouns",
  //                                               style: TextStyle(
  //                                                   color: Colors.black,
  //                                                   fontWeight: FontWeight.w500,
  //                                                   fontSize: 18)),
  //                                           subtitle: (proList.isEmpty == false)
  //                                               ? null
  //                                               : const Text("Not Set!",
  //                                                   style: TextStyle(
  //                                                       color:
  //                                                           Color(0xff737373),
  //                                                       fontWeight:
  //                                                           FontWeight.w400,
  //                                                       fontSize: 14))),
  //                                       Padding(
  //                                         padding: const EdgeInsets.only(
  //                                             left: 10, bottom: 15),
  //                                         child: Wrap(
  //                                           runSpacing: 8,
  //                                           spacing: 8,
  //                                           children: List.generate(
  //                                               proList.length, (index) {
  //                                             return ClipItemWidget(
  //                                               item: proList[index],
  //                                               borderRadious: 30,
  //                                               onSelectionChanged:
  //                                                   (isSelected) {},
  //                                             );
  //                                           }),
  //                                         ),
  //                                       ),
  //                                     ]),
  //                               ),
  //                             ]),
  //                       ),
  //                     ]),
  //                   ],
  //                 ),
  //               ),
  //             );
  //     },
  //     error: (_, e) => const Center(child: Text("Something went wrong!")),
  //     loading: () => const Center(
  //       child: CircularProgressIndicator.adaptive(),
  //     ),
  //   );
  // }
}

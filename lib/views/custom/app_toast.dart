import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vibration/vibration.dart';

import '../../helpers/constants.dart';
import '../../main.dart';

enum CustomToastGravity { top, bottom }

class CustomToast {
  static showToast({
    required String message,
    bool isSuccess = false,
    bool autoDismiss = true,
    CustomToastGravity gravity = CustomToastGravity.top,
    int durationInSec = 3,
    double position = 0,
    Color? errorColor,
    Widget? messageWidget,
    bool hapticEnabled=true,
    ///position from baseline
  }) async {
    bool showFromBottom = (gravity == CustomToastGravity.bottom);
    final overlay = navigationKey.currentState?.overlay;
    late OverlayEntry overlayEntry;
    if (overlay == null) return;
    overlayEntry = OverlayEntry(
      builder: (context) =>
          Positioned(
            left: 0,
            right: 0,
            top: showFromBottom ? null : 10,
            bottom: showFromBottom ? 10 : null,
            child: Material(
              color: Colors.transparent,
              child: SafeArea(
                child: _SlideToast(
                  message: message,
                  showFromBottom: showFromBottom,
                  durationInSec: durationInSec,
                  autoDismiss: autoDismiss,
                  isSuccess: isSuccess,
                  errorColor: errorColor,
                  onDismiss: () {
                    overlayEntry.remove();
                  }, messageWidget: messageWidget,
                ),
              ),
            ),
          ),
    );
    overlay.insert(overlayEntry);
    if ((await Vibration.hasVibrator()) && hapticEnabled) {
      Vibration.vibrate();
    }
    }
}

class _SlideToast extends StatefulWidget {
  final String message;
  final bool isSuccess;
  final bool autoDismiss;
  final bool showFromBottom;
  final int durationInSec;
  final VoidCallback onDismiss;
  final Color? errorColor;
  final Widget? messageWidget;

  const _SlideToast(
      {required this.message,
        required this.onDismiss,
        required this.isSuccess,
        required this.autoDismiss,
        required this.durationInSec,
        required this.showFromBottom, this.errorColor,
        required this.messageWidget,
      });

  @override
  State<_SlideToast> createState() => _SlideToastState();
}

class _SlideToastState extends State<_SlideToast>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _animationController.forward();

    Future.delayed(Duration(seconds: widget.durationInSec), () async {
      if (!_animationController.isDismissed && widget.autoDismiss) {
        try {
          _animationController.reverse().then((value) {
            widget.onDismiss();
          });
        } catch (error) {
          if (kDebugMode) print("The ToastAnimationController is disposed!");
        }
      }
    });
  }

    void _dismissToast() {
    _animationController.reverse().then((_) {
      widget.onDismiss();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: Offset(0, widget.showFromBottom ? 1 : -1),
        end: const Offset(0, 0),
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      )),
      child: GestureDetector(
        onVerticalDragEnd: (details) => _dismissToast(),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(12),
          margin: EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            border: Border.all(
              color: widget.isSuccess
              ? AppConstants.greenB5E1B1
              : AppConstants.redEFD0C7,
            ),
    borderRadius: BorderRadius.circular(8),
      color: widget.isSuccess
              ? AppConstants.greenF0FFF0
              : AppConstants.redFBEFEB,
    ),
          child: Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              SvgPicture.asset(
                widget.isSuccess
                    ? AppConstants.checkGreenIcon
                    : AppConstants.infoRedIcon,
                fit: BoxFit.contain,
              ),
              const SizedBox(width: 8),
              Flexible(
                fit: FlexFit.tight,
                child: widget.messageWidget ?? Text(
                  widget.message,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';

class CustomAppButtonRounded extends StatefulWidget {
  final Function() onTap;
  final String buttonTxt;

  const CustomAppButtonRounded(
      {super.key, required this.onTap, required this.buttonTxt});

  @override
  State<CustomAppButtonRounded> createState() => _CustomAppButtonRoundedState();
}

class _CustomAppButtonRoundedState extends State<CustomAppButtonRounded> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        widget.onTap();
      },
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(20)),
          gradient: LinearGradient(
            colors: [
              Color.fromRGBO(5, 114, 128, 1.0),
              Color.fromRGBO(54, 158, 43, 1.0),
              Color.fromRGBO(1, 146, 146, 1.0),
            ],
            stops: [
              0.00,
              0.00,
              1.00,
            ],
            begin: FractionalOffset(-0.4, -0.29),
            end: FractionalOffset(1.34, 1.29),
          ),
        ),

        // onPressed: _login,
        child: Center(
          child: Text(
            widget.buttonTxt,
            style: const TextStyle(
                color: Colors.white, fontSize: 14, fontWeight: FontWeight.w600),
          ),
        ),
      ),
    );
  }
}

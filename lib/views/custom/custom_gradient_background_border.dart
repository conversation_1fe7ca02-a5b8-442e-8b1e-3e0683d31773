import 'package:flutter/material.dart';

class CustomGradientBorderWidget extends StatelessWidget {
  final Widget child;
  final AlignmentGeometry begin;
  final AlignmentGeometry end;
  final double leftTopRadius, rightTopRadius, rightBottomRadius, leftBottomRadius ;

  const CustomGradientBorderWidget({
    Key? key,
    required this.child,
    this.begin = Alignment.topLeft,
    this.end = Alignment.bottomRight,
    this.leftTopRadius = 0,
    this.rightTopRadius = 0,
    this.rightBottomRadius = 0,
    this.leftBottomRadius = 0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(bottomLeft: Radius.circular(leftBottomRadius), bottomRight: Radius.circular(rightBottomRadius),
        topLeft: Radius.circular(leftTopRadius), topRight: Radius.circular(rightTopRadius)),
        gradient: LinearGradient(
          begin: begin,
          end: end,
          colors: const [
            Color(0xFFC70973),
            Color(0xFF46239F),
          ],
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.only(bottomLeft: Radius.circular(leftBottomRadius), bottomRight: Radius.circular(rightBottomRadius),
        topLeft: Radius.circular(leftTopRadius), topRight: Radius.circular(rightTopRadius)),
        child: child,
      ),
    );
  }
}

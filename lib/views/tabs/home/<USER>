// ignore_for_file: use_build_context_synchronously
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fringle_app/views/custom/app_toast.dart';

import '../../../helpers/constants.dart';
import '../../../helpers/custom_cache_image.dart';
import '../../../models/irl_activity_model.dart';
import '../../../models/user_profile_model.dart';
import '../../../providers/irl_activities_provider.dart';
import '../../../providers/user_profile_provider.dart';
import '../../custom/join_activity_group_dialog.dart';
import 'irl_activity_screen.dart';

class IrlActivitiesTab extends ConsumerStatefulWidget {
  const IrlActivitiesTab({super.key});

  @override
  ConsumerState<IrlActivitiesTab> createState() => _IrlActivitiesTabState();
}

class _IrlActivitiesTabState extends ConsumerState<IrlActivitiesTab> {

  final combinedActivitiesProvider = FutureProvider<
      ({
        List<IrlActivityModel> activities,
        UserProfileModel? userProfile
      })>((ref) async {
    final activities = await ref.watch(irlActivitiesProvider.future);
    final userProfile = await ref.watch(userProfileFutureProvider.future);

    return (activities: activities, userProfile: userProfile);
  });

  @override
  Widget build(BuildContext context) {
    final asyncActivities = ref.watch(combinedActivitiesProvider);

    return asyncActivities.when(
      data: (data) {
        if (data.activities.isEmpty) {
          return const Center(child: Text('No activities found.'));
        }
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultNumericValue),
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 10.0,
              mainAxisSpacing: 10.0,
              childAspectRatio: 3 / 4,
            ),
            itemCount: data.activities.length,
            itemBuilder: (context, index) {
              final activity = data.activities[index];
              return ActivityCard(
                activity: activity,
                userProfile: data.userProfile,
                widgetRef: ref,
              );
            },
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator.adaptive()),
      error: (error, stackTrace) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 60),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text('Error loading activities: $error'),
            ),
            ElevatedButton(
              onPressed: () => ref.refresh(irlActivitiesProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }
}

class ActivityCard extends StatelessWidget {
  final IrlActivityModel activity;
  final UserProfileModel? userProfile;
  final WidgetRef? widgetRef;

  const ActivityCard({
    super.key,
    required this.activity,
    this.userProfile,
    this.widgetRef,
  });

  Future<void> _onJoinActivity(BuildContext context, IrlActivityModel activity) async {
    EasyLoading.show(status: 'Joining activity...');

    // Get the current user profile from the FutureProvider's AsyncValue
    final currentProfileAsyncValue = widgetRef?.read(userProfileFutureProvider);
    final currentProfile = currentProfileAsyncValue?.maybeWhen(
      data: (profile) => profile,
      orElse: () => null,
    );

    if (currentProfile != null) {
      final joinedActivities = {...(currentProfile.joinedActivities ?? <String>[]), activity.id}.toList()..sort(); // Prevent duplicates and sort
      final updatedProfile = currentProfile.copyWith(joinedActivities: joinedActivities);

      final result = await widgetRef?.read(userProfileNotifier).updateUserProfile(updatedProfile);

      EasyLoading.dismiss();

      if (result == true) {
        widgetRef?.invalidate(userProfileFutureProvider);
        CustomToast.showToast(message: 'Activity group joined successfully', isSuccess: true);
      } else {
        CustomToast.showToast(message: 'Failed to join activity group');
      }
    } else {
      EasyLoading.dismiss();
      EasyLoading.showError('Failed to join activity group');
    }
    Navigator.pop(context);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => IrlActivityScreen(
          activity: activity,
          onExitGroup: () => _onLeaveActivity(context, activity),
        ),
      ),
    ).then((value) {
      if (value == true) {
        widgetRef?.invalidate(userProfileFutureProvider);
      }
    });
  }

  Future<void> _onLeaveActivity(BuildContext context, IrlActivityModel activity) async {
    EasyLoading.show(status: 'Leaving activity...');

    final currentProfileAsyncValue = widgetRef?.read(userProfileFutureProvider);
    final currentProfile = currentProfileAsyncValue?.maybeWhen(
      data: (profile) => profile,
      orElse: () => null,
    );

    if (currentProfile != null) {
      final joinedActivities = (currentProfile.joinedActivities ?? <String>[])
          .whereType<String>()
          .toList()
        ..remove(activity.id)
        ..sort();

      final updatedProfile = currentProfile.copyWith(joinedActivities: joinedActivities);

      final result = await widgetRef?.read(userProfileNotifier).updateUserProfile(updatedProfile);

      EasyLoading.dismiss();

      if (result == true) {
        widgetRef?.invalidate(userProfileFutureProvider);
        CustomToast.showToast(message: 'Activity group left successfully', isSuccess: true);
      } else {
        CustomToast.showToast(message: 'Failed to leave activity group');
      }

      Navigator.pop(context, true);
    } else {
      EasyLoading.dismiss();
      EasyLoading.showError('Failed to leave activity group');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(14.0),
      ),
      elevation: 3.0,
      child: Stack(
        fit: StackFit.expand,
        children: [
          CustomCacheImage(
            imageUrl: activity.imageUrl,
            fit: BoxFit.cover,
            height: 220,
            width: 166,
            errorWidget: Container(
              color: Colors.grey[300],
              child: const Center(
                child: Icon(Icons.broken_image, color: Colors.grey, size: 40),
              ),
            ),
          ),
          // Gradient overlay for better text visibility (optional)
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.black.withOpacity(0.0),
                  Colors.black.withOpacity(0.4)
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
          // Activity Name
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  activity.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 32,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
                if (activity.subtitle != null)
                  Text(
                    activity.subtitle!,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
              ],
            ),
          ),
          // InkWell for tap effect (optional)
          Positioned.fill(
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  if (userProfile?.joinedActivities?.contains(activity.id) ?? false) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => IrlActivityScreen(
                          activity: activity,
                          onExitGroup: () => _onLeaveActivity(context, activity),
                        ),
                      ),
                    );
                    return;
                  }
                  showDialog(
                    context: context,
                    builder: (context) => JoinActivityGroupDialog(
                      activity: activity,
                      onJoin: () => _onJoinActivity(context, activity),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:flutter/foundation.dart';

import 'package:fringle_app/models/user_account_settings_model.dart';

class UserProfileModelNew {
  String id;
  String userId;
  String? firstName;
  String? lastName;
  String? email;
  String? profilePicture;
  String? phoneNumber;
  String? gender;
  String? about;
  DateTime? birthDay;
  List<String>? mediaFiles;
  List<String>? interests;
  UserAccountSettingsModel? userAccountSettingsModel;
  bool isVerified;
  bool? isOnline;
  bool? isNewUser;
  bool? isPremiumUser;
  String? education;
  String? drinking;
  String? religion;
  String? cannabis;
  List<String>? pronounceList = [];
  List<String>? imageList = [];
  String? bio;
  String? work;
  String? address;
  double? latitude;
  double? longitude;
  bool? isDMOn;
  bool? isFeaturedOn;
  DateTime? featuredTime;
  DateTime? dmDate;
  DateTime? dmStartTime;
  DateTime? dmEndTime;

  UserProfileModelNew({
    required this.id,
    required this.userId,
    this.firstName,
    this.lastName,
    this.email,
    this.profilePicture,
    this.phoneNumber,
    this.gender,
    this.about,
    this.birthDay,
    this.mediaFiles,
    this.interests,
    this.userAccountSettingsModel,
    required this.isVerified,
    this.isOnline = false,
    this.education,
    this.drinking,
    this.religion,
    this.cannabis,
    this.pronounceList,
    this.imageList,
    this.bio,
    this.work,
    this.isNewUser,
    this.isPremiumUser,
    this.address,
    this.latitude,
    this.longitude,
    this.dmDate,
    this.dmStartTime,
    this.dmEndTime,
    this.isDMOn,
    this.featuredTime,
    this.isFeaturedOn,
  });

  UserProfileModelNew copyWith({
    String? id,
    String? userId,
    String? firstName,
    String? lastName,
    double? latitude,
    double? longitude,
    String? email,
    String? profilePicture,
    String? phoneNumber,
    String? gender,
    String? about,
    DateTime? birthDay,
    List<String>? mediaFiles,
    List<String>? interests,
    UserAccountSettingsModel? userAccountSettingsModel,
    bool? isVerified,
    bool? isOnline,
    String? education,
    String? drinking,
    String? religion,
    String? cannabis,
    List<String>? pronounceList,
    List<String>? imageList,
    String? bio,
    String? work,
    String? height,
    bool? isNewUser,
    bool? isPremiumUser,
    String? address,
  }) {
    return UserProfileModelNew(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      profilePicture: profilePicture ?? this.profilePicture,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      gender: gender ?? this.gender,
      about: about ?? this.about,
      birthDay: birthDay ?? this.birthDay,
      mediaFiles: mediaFiles ?? this.mediaFiles,
      interests: interests ?? this.interests,
      userAccountSettingsModel:
          userAccountSettingsModel ?? this.userAccountSettingsModel,
      isVerified: isVerified ?? this.isVerified,
      isOnline: isOnline ?? this.isOnline,
      education: education ?? this.education,
      drinking: drinking ?? this.drinking,
      religion: religion ?? this.religion,
      cannabis: cannabis ?? this.cannabis,
      pronounceList: pronounceList ?? this.pronounceList,
      imageList: imageList ?? this.imageList,
      bio: bio ?? this.bio,
      work: work ?? this.work,
      isNewUser: isNewUser ?? this.isNewUser,
      isPremiumUser: isPremiumUser ?? this.isPremiumUser,
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'userId': userId,
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'profilePicture': profilePicture,
      'phoneNumber': phoneNumber,
      'gender': gender,
      'about': about,
      'birthDay': birthDay?.millisecondsSinceEpoch,
      'mediaFiles': mediaFiles,
      'interests': interests,
      'userAccountSettingsModel': userAccountSettingsModel?.toMap(),
      'isVerified': isVerified,
      'isOnline': isOnline,
      'education': education,
      'drinking': drinking,
      'religion': religion,
      'cannabis': cannabis,
      'pronounceList': pronounceList,
      'imageList': imageList,
      'bio': bio,
      'work': work,
      'isNewUser': isNewUser,
      'isPremiumUser': isPremiumUser,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'isDMOn': isDMOn,
      'isFeaturedOn': isFeaturedOn,
      'featuredTime': featuredTime?.millisecondsSinceEpoch,
      'dmDate': dmDate?.millisecondsSinceEpoch,
      'dmStartTime': dmStartTime?.millisecondsSinceEpoch,
      'dmEndTime': dmEndTime?.millisecondsSinceEpoch,
    };
  }

  factory UserProfileModelNew.fromMap(Map<String, dynamic> map) {
    return UserProfileModelNew(
      id: map['id'] as String,
      userId: map['userId'] as String,
      firstName: map['firstName'] as String,
      lastName: map['lastName'] as String,
      email: map['email'] != null ? map['email'] as String : null,
      profilePicture: map['profilePicture'] != null
          ? map['profilePicture'] as String
          : null,
      phoneNumber:
          map['phoneNumber'] != null ? map['phoneNumber'] as String : null,
      gender: map['gender'] as String,
      about: map['about'] != null ? map['about'] as String : null,
      isDMOn: map['isDmOn'] != null ? map['isDmOn'] as bool : null,
      isFeaturedOn:
          map['isFeaturedOn'] != null ? map['isFeaturedOn'] as bool : null,
      birthDay: DateTime.fromMillisecondsSinceEpoch(map['birthDay'] as int),
      featuredTime:
          DateTime.fromMillisecondsSinceEpoch(map['featuredTime'] as int),
      dmDate: DateTime.fromMillisecondsSinceEpoch(map['dmDate'] as int),
      dmStartTime:
          DateTime.fromMillisecondsSinceEpoch(map['dmStartTime'] as int),
      dmEndTime: DateTime.fromMillisecondsSinceEpoch(map['dmEndTime'] as int),
      mediaFiles: List<String>.from(map['mediaFiles']!.map((x) => x)),
      interests: List<String>.from(map['interests']!.map((x) => x)),
      userAccountSettingsModel: UserAccountSettingsModel.fromMap(
          map['userAccountSettingsModel'] as Map<String, dynamic>),
      isVerified: map['isVerified'] as bool,
      isOnline: map['isOnline'] as bool,
      education: map['education'] != null ? map['education'] as String : null,
      drinking: map['drinking'] != null ? map['drinking'] as String : null,
      religion: map['religion'] != null ? map['religion'] as String : null,
      cannabis: map['cannabis'] != null ? map['cannabis'] as String : null,
      pronounceList: map['pronounceList'] != null
          ? List<String>.from(map['pronounceList']!.map((x) => x))
          : null,
      imageList: map['imageList'] != null
          ? List<String>.from(map['imageList']!.map((x) => x))
          : null,
      bio: map['bio'] != null ? map['bio'] as String : null,
      work: map['work'] != null ? map['work'] as String : null,
      isNewUser: map['isNewUser'] != null ? map['isNewUser'] as bool : null,
      isPremiumUser:
          map['isPremiumUser'] != null ? map['isPremiumUser'] as bool : null,
      address: map['address'] != null ? map['address'] as String : null,
      latitude: map['latitude'] != null ? map['latitude'] as double : null,
      longitude: map['longitude'] != null ? map['longitude'] as double : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory UserProfileModelNew.fromJson(String source) =>
      UserProfileModelNew.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'UserProfileModel(id: $id, userId: $userId, firstName: $firstName, lastName: $lastName, email: $email, profilePicture: $profilePicture, phoneNumber: $phoneNumber, gender: $gender, about: $about, birthDay: $birthDay, mediaFiles: $mediaFiles, interests: $interests, userAccountSettingsModel: $userAccountSettingsModel, isVerified: $isVerified, isOnline: $isOnline, education: $education, religion: $religion, cannabis: $cannabis, pronounceList: $pronounceList,imageList : $imageList, bio: $bio, work: $work, address: $address, latitude : $latitude, longitude: $longitude, isDmOn: $isDMOn, isFeaturedOn: $isFeaturedOn, featuredTime $featuredTime, dmDate $dmDate, dmStartTime $dmStartTime dmEndTime $dmEndTime)';
  }

  @override
  bool operator ==(covariant UserProfileModelNew other) {
    if (identical(this, other)) return true;

    return other.id == id &&
        other.userId == userId &&
        other.firstName == firstName &&
        other.lastName == lastName &&
        other.email == email &&
        other.profilePicture == profilePicture &&
        other.phoneNumber == phoneNumber &&
        other.gender == gender &&
        other.about == about &&
        other.birthDay == birthDay &&
        listEquals(other.mediaFiles, mediaFiles) &&
        listEquals(other.interests, interests) &&
        other.userAccountSettingsModel == userAccountSettingsModel &&
        other.isVerified == isVerified &&
        other.isOnline == isOnline &&
        other.education == education &&
        other.drinking == drinking &&
        other.religion == religion &&
        other.cannabis == cannabis &&
        listEquals(other.pronounceList, pronounceList) &&
        listEquals(other.imageList, imageList) &&
        other.bio == bio &&
        other.work == work &&
        other.isNewUser == isNewUser &&
        other.isPremiumUser == isPremiumUser &&
        other.address == address &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.dmDate == dmDate &&
        other.dmStartTime == dmStartTime &&
        other.featuredTime == featuredTime &&
        other.isDMOn == isDMOn &&
        other.isFeaturedOn == isFeaturedOn &&
        other.dmEndTime == dmEndTime;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        firstName.hashCode ^
        lastName.hashCode ^
        email.hashCode ^
        profilePicture.hashCode ^
        phoneNumber.hashCode ^
        gender.hashCode ^
        about.hashCode ^
        birthDay.hashCode ^
        mediaFiles.hashCode ^
        interests.hashCode ^
        userAccountSettingsModel.hashCode ^
        isVerified.hashCode ^
        isOnline.hashCode ^
        education.hashCode ^
        drinking.hashCode ^
        religion.hashCode ^
        cannabis.hashCode ^
        pronounceList.hashCode ^
        imageList.hashCode ^
        bio.hashCode ^
        work.hashCode ^
        isNewUser.hashCode ^
        isPremiumUser.hashCode ^
        address.hashCode ^
        latitude.hashCode ^
        longitude.hashCode ^
        dmDate.hashCode ^
        dmStartTime.hashCode ^
        featuredTime.hashCode ^
        dmEndTime.hashCode ^
        isDMOn.hashCode ^
        isFeaturedOn.hashCode;
  }
}

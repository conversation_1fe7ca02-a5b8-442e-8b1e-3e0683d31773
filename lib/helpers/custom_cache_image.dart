import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';


class CustomCacheImage extends StatelessWidget {
  final String imageUrl;
  final double? height;
  final double? width;
  final BoxFit? fit;
  final double scale;
  final int? memCacheWidth;
  final int? memCacheHeight;
  final Widget? errorWidget;
  final Duration? fadeInDuration;

  const CustomCacheImage({
    super.key,
    required this.imageUrl,
    this.height,
    this.width,
    this.fit = BoxFit.cover,
    this.scale = 1.0,
    this.memCacheWidth,
    this.memCacheHeight,
    this.errorWidget,
    this.fadeInDuration,
  });

  @override
  Widget build(BuildContext context) {
    final cacheHeight = memCacheHeight ?? height?.toInt();
    final roundedCacheHeight = cacheHeight != null
        ? (cacheHeight * MediaQuery.of(context).devicePixelRatio).round()
        : null;
    return FastCachedImage(
      url: imageUrl,
      height: height,
      width: width,
      fit: fit,
      scale: scale,
      cacheHeight: roundedCacheHeight,
      loadingBuilder: (_, __) => Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Container(
          height: height ?? 80.0,
          width: width ?? 80.0,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      errorBuilder: (_, __, ___) => errorWidget ?? SizedBox.shrink(),
      fadeInDuration: fadeInDuration ?? const Duration(milliseconds: 300),
    );
  }
}
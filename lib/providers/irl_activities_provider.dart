import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../helpers/constants.dart';
import '../models/irl_activity_model.dart';

final irlActivitiesProvider = FutureProvider.autoDispose<List<IrlActivityModel>>((ref) async {
  final irlActivitiesCollection = FirebaseFirestore.instance.collection(FirebaseConstants.irlActivitiesCollection);

  return await irlActivitiesCollection.orderBy('order').get().then((snapshot) {
    final List<IrlActivityModel> irlActivities = [];
    for (var doc in snapshot.docs) {
      irlActivities.add(IrlActivityModel.fromFirestore(doc));
    }
    return irlActivities;
  });
});

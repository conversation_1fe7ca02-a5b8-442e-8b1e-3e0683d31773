import 'package:fringle_app/models/user_profile_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../helpers/constants.dart';
import '../../providers/user_profile_provider.dart';
import '../custom/custom_button.dart';
import '../custom/custom_dropdown_new.dart';
import '../custom/custom_new_app_bar.dart';

class CouplePrefrences extends ConsumerStatefulWidget {
  final bool isFromFilter;
  const CouplePrefrences({super.key, this.isFromFilter = false});

  @override
  ConsumerState<CouplePrefrences> createState() => _CoupleAccountDetialsState();
}

class _CoupleAccountDetialsState extends ConsumerState<CouplePrefrences> {
  String? selectedIamFirst;
  String? selectedSeekingFirst;
  String? selectedIamSecond;
  String? selectedSeekingSecond;
  late SharedPreferences _prefs;
  bool isSecondaryAccount = false;
  UserProfileModel? userProfileModel;
  Map<String, dynamic>? secondaryPref = {};
  Map<String, dynamic>? primaryPref = {};

  @override
  void initState() {
    super.initState();
    initPrefrence();
  }

  Future<void> initPrefrence() async {
    _prefs = await SharedPreferences.getInstance();
    isSecondaryAccount = _prefs.getBool(AppConstants.coupleAccount) ?? false;
    final user = ref.read(userProfileFutureProvider);
    user.when(
      data: (data) {
        if (data != null) {
          userProfileModel = data;
          var preferences = isSecondaryAccount
              ? data.couplePrefrenceSecond
              : data.couplePrefrencePrimary;

          secondaryPref = data.couplePrefrenceSecond;
          primaryPref = data.couplePrefrencePrimary;
          selectedIamFirst = preferences?['couplePrimery'];
          selectedIamSecond = preferences?['coupleSecondary'];
          selectedSeekingFirst = preferences?['seekingHer'];
          selectedSeekingSecond = preferences?['seekingHim'];
          setState(() {});
        }
      },
      error: (error, stackTrace) {},
      loading: () {},
    );
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFFFCF5),
      appBar: GradientAppBar(
        title: "Couple Preferences".toUpperCase(),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(20, 34, 20, 0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              "Couple",
              style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black),
            ),
            const SizedBox(
              height: 15,
            ),
            CustomDropdownNew(
              items: listWhoIs,
              selectedValue: selectedIamFirst,
              onChanged: (value) {
                setState(() {
                  selectedIamFirst = value;
                });
              },
              labelText: 'Primary *',
              hintText: 'Select',
              isEnable: !isSecondaryAccount,
            ),
            const SizedBox(
              height: 20,
            ),
            CustomDropdownNew(
              items: listWhoIs,
              selectedValue: selectedIamSecond,
              onChanged: (value) {
                setState(() {
                  selectedIamSecond = value;
                });
              },
              labelText: 'Secondary *',
              hintText: 'Select',
            ),
            const SizedBox(
              height: 40,
            ),
            const Text(
              "Seeking",
              style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black),
            ),
            const SizedBox(
              height: 24,
            ),
            CustomDropdownNew(
              items: List.from(listWhoIs)
                ..add('Any')
                ..add('None'),
              selectedValue: selectedSeekingFirst,
              onChanged: (value) {
                setState(() {
                  selectedSeekingFirst = value;
                });
              },
              labelText: 'Her *',
              hintText: 'Select',
            ),
            const SizedBox(
              height: 20,
            ),
            CustomDropdownNew(
              items: List.from(listWhoIs)
                ..add('Any')
                ..add('None'),
              selectedValue: selectedSeekingSecond,
              onChanged: (value) {
                setState(() {
                  selectedSeekingSecond = value;
                });
              },
              labelText: 'Him *',
              hintText: 'Select',
            ),
            const Spacer(),
            Padding(
              padding: const EdgeInsets.only(bottom: 24),
              child: SizedBox(
                height: 52,
                child: CustomButton(
                  onPressed: () {
                    if ((selectedIamFirst?.isEmpty == true) ||
                        (selectedIamSecond?.isEmpty == true) ||
                        (selectedSeekingFirst?.isEmpty == true) ||
                        (selectedSeekingSecond?.isEmpty == true)) {
                      EasyLoading.showToast(
                          "Please fill in all the required fields.");
                      return;
                    }
                    if (userProfileModel != null) {
                      Map<String, dynamic>? selectedPreferences = {};
                      selectedPreferences["couplePrimery"] = selectedIamFirst;
                      selectedPreferences["coupleSecondary"] =
                          selectedIamSecond;
                      selectedPreferences["seekingHer"] = selectedSeekingFirst;
                      selectedPreferences["seekingHim"] = selectedSeekingSecond;
                      if (isSecondaryAccount) {
                        primaryPref?["coupleSecondary"] = selectedIamSecond;
                        ref.read(userProfileNotifier).updateUserProfile(
                            userProfileModel!.copyWith(
                                isCoupleAccount: true,
                                couplePrefrencePrimary: primaryPref,
                                couplePrefrenceSecond: selectedPreferences));
                      } else {
                        secondaryPref?['couplePrimery'] = selectedIamFirst;
                        secondaryPref?["coupleSecondary"] = selectedIamSecond;
                        ref.read(userProfileNotifier).updateUserProfile(
                            userProfileModel!.copyWith(
                                isCoupleAccount: true,
                                couplePrefrenceSecond: secondaryPref,
                                couplePrefrencePrimary: selectedPreferences));
                      }
                      ref.invalidate(userProfileFutureProvider);
                      EasyLoading.showToast("Preferences applied successfully");
                      Navigator.pop(context);
                    }
                  },
                  text: "Apply",
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/helpers/validators.dart';
import 'package:fringle_app/providers/auth_providers.dart';
import 'package:fringle_app/views/auth/email_verification_page.dart';
import 'package:fringle_app/views/custom/custom_textfield.dart';

import '../../providers/user_profile_provider.dart';
import '../custom/app_toast.dart';
import '../custom/custom_app_loader.dart';
import '../custom/custom_button.dart';
import '../custom/gradient_text.dart';

class SignupScreen extends ConsumerStatefulWidget {
  const SignupScreen({super.key});

  @override
  ConsumerState<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends ConsumerState<SignupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool autoValidate = false;
  bool isChecked = false;
  bool isPasswordObscured = true;
  bool isConfirmPasswordObscured = true;

  void toggleCheckbox() {
    setState(() {
      isChecked = !isChecked;
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _signup() async {
    // Navigator.push(
    //   context,
    //   MaterialPageRoute(builder: (_) => const EmailVerificationPage()),
    // );
    // return;
    if (_formKey.currentState!.validate()) {
      // Perform signup logic here
      if (!isChecked) {
        CustomToast.showToast(message: "Please accept Terms & conditions and Privacy policy.");
        return;
      }
      String email = _emailController.text.trim();
      String password = _passwordController.text.trim();
      CustomAppLoader.showCustomLoader("Signing Up...");
      await ref.read(authProvider)
          .signUpWithEmailAndPassword(email, password)
          .then((value) async {
        CustomAppLoader.dismissCustomLoader();
        if (value != null) {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (_) => const EmailVerificationPage()),
          );
          // final UserProfileModel userProfileModel = UserProfileModel(
          //     id: userId,
          //     userId: userId,
          //     fullName: _nameController.text,
          //     email: _emailController.text,
          //     userAccountSettingsModel: null);

          //  final userId = ref.watch(currentUserStateProvider)?.uid;

          await ref.read(userProfileNotifier).createProfile(value.uid, {
            "isProfileCompleted": false,
          });
        }
      });
    } else {
      setState(() {
        autoValidate = true;
      });
    }
  }

  String? _validateConfirmPassword(String? value) {
    if (value != _passwordController.text) {
      return 'Passwords do not match';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        clipBehavior: Clip.none,
        alignment: Alignment.bottomCenter,
        children: [
          SvgPicture.asset(
            AppConstants.logoBgImage,
          ),
          Scaffold(
            resizeToAvoidBottomInset: false,
            backgroundColor: Colors.transparent,
            // resizeToAvoidBottomInset: false,
            body: SafeArea(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(24),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        // physics: NeverScrollableScrollPhysics(),
                        // shrinkWrap: true,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          InkWell(
                            onTap: () => Navigator.of(context).pop(),
                            child: SvgPicture.asset(
                              AppConstants.backIcon,
                            ),
                          ),
                          const SizedBox(
                            height: 6,
                          ),
                          Padding(
                              padding: EdgeInsets.only(left: 5.0),
                              child: GradientText(
                                text: "Hi, Welcome to Fringle",
                                gradient: AppConstants.defaultGradient,
                                style: TextStyle(
                                    fontFamily: AppConstants.fontStyleName,
                                    fontSize: 24,
                                    fontWeight: FontWeight.w700),
                              )),
                          const SizedBox(
                            height: 14,
                          ),
                          Padding(
                              padding: EdgeInsets.only(left: 5.0),
                              child: Text(
                                "Enter below details to create account",
                                style: TextStyle(
                                    fontFamily: AppConstants.fontStyleName,
                                    color: AppConstants.primaryTextColor,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w400),
                              )),
                          const SizedBox(
                            height: 22,
                          ),
                          CustomTextField(
                            autoValidate: autoValidate,
                            controller: _emailController,
                            labelText: 'Email',
                            hintText: 'Enter email',
                            validator: Validators.validateEmail,
                            keyboardType: TextInputType.emailAddress,
                            inputFormatters: [
                              FilteringTextInputFormatter.deny(RegExp(r'\s')),
                            ],
                          ),
                          const SizedBox(height: 24),
                          CustomTextField(
                            autoValidate: autoValidate,
                            controller: _passwordController,
                            labelText: 'Password',
                            hintText: 'Enter password',
                            obscureText: isPasswordObscured,
                            validator: Validators.validatePassword,
                            suffixIcon: InkWell(
                              onTap: () {
                                isPasswordObscured = !isPasswordObscured;
                                setState(() {});
                              },
                              child: isPasswordObscured
                                  ? const Icon(
                                      Icons.visibility_off,
                                      color: Color(0xFFC0C0C0),
                                    )
                                  : const Icon(Icons.visibility,
                                      color: Color(0xFFC0C0C0)),
                            ),
                            onTap: () {},
                          ),
                          const SizedBox(height: 24),
                          CustomTextField(
                            autoValidate: autoValidate,
                            controller: _confirmPasswordController,
                            labelText: 'Confirm Password',
                            hintText: 'Enter confirm password',
                            obscureText: isConfirmPasswordObscured,
                            validator: _validateConfirmPassword,
                            suffixIcon: InkWell(
                              onTap: () {
                                isConfirmPasswordObscured =
                                    !isConfirmPasswordObscured;
                                setState(() {});
                              },
                              child: isConfirmPasswordObscured
                                  ? const Icon(
                                      Icons.visibility_off,
                                      color: Color(0xFFC0C0C0),
                                    )
                                  : const Icon(Icons.visibility,
                                      color: Color(0xFFC0C0C0)),
                            ),
                            onTap: () {},
                          ),
                          // const SizedBox(height: 24),
                          // CustomTextField(
                          //   controller: _nameController,
                          //   labelText: 'Name',
                          //   hintText: 'Enter name',
                          //   validator: Validators.validateName,
                          // ),
                          // const SizedBox(height: 24),
                          // CustomTextField(
                          //   onTap: () async {
                          //     UserLocation? location = await Navigator.push(
                          //       context,
                          //       MaterialPageRoute(
                          //         builder: (context) => const SetUserLocation(),
                          //         fullscreenDialog: true,
                          //       ),
                          //     );
                          //     _addressController.text = "";
      
                          //     if (location != null) {
                          //       userLocation = location;
                          //       _addressController.text = location.addressText;
                          //       setState(() {});
                          //     }
                          //   },
                          //   controller: _addressController,
                          //   labelText: 'Address',
                          //   hintText: 'Enter Address',
                          //   validator: Validators.validateAddress,
                          // ),
                        ],
                      ),
                    ),
                  ),
                  Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              GestureDetector(
                                onTap: toggleCheckbox,
                                child: SvgPicture.asset(
                                  isChecked
                                      ? AppConstants.checkedIcon
                                      : AppConstants.uncheckedIcon,
                                ),
                              ),
                              const SizedBox(
                                width: 8,
                              ),
                              RichText(
                                text: TextSpan(
                                  text: "I accept all the ",
                                  style: TextStyle(
                                    fontFamily: AppConstants.fontStyleName,
                                    color: AppConstants.primaryTextColor,
                                    fontSize: 13,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  children: [
                                    TextSpan(
                                      text: "Terms & Conditions",
                                      style: TextStyle(
                                          fontFamily: AppConstants.fontStyleName,
                                          color: AppConstants.primaryLightColor,
                                          fontSize: 13,
                                          fontWeight: FontWeight.w700,
                                          decoration: TextDecoration.underline),
                                      // Add onTap handler for signup action
                                      // For example, navigate to signup screen
                                      recognizer: TapGestureRecognizer()
                                        ..onTap = () {
                                          _launchURL(
                                              'https://fluster-fb957.web.app/terms-and-conditions.html');
                                        },
                                    ),
                                    TextSpan(
                                      text: " and\n",
                                      style: TextStyle(
                                        fontFamily: AppConstants.fontStyleName,
                                        color: AppConstants.primaryTextColor,
                                        fontSize: 13,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                    TextSpan(
                                      text: "Privacy Policy",
                                      style: TextStyle(
                                          fontFamily: AppConstants.fontStyleName,
                                          color: AppConstants.primaryLightColor,
                                          fontSize: 13,
                                          fontWeight: FontWeight.w700,
                                          decoration: TextDecoration.underline),
                                      // Add onTap handler for signup action
                                      // For example, navigate to signup screen
                                      recognizer: TapGestureRecognizer()
                                        ..onTap = () {
                                          _launchURL(
                                              'https://fluster-fb957.web.app/privacy-policy.html');
                                        },
                                    ),
                                    TextSpan(
                                      text: " of the Fringle.",
                                      style: TextStyle(
                                        fontFamily: AppConstants.fontStyleName,
                                        color: AppConstants.primaryTextColor,
                                        fontSize: 13,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(
                            height: 36,
                          ),
                          CustomButton(
                            onPressed: () {
                              _signup();
                            },
                            text: "Continue",
                          ),
                          const SizedBox(
                            height: 8,
                          )
                        ],
                      )),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  _launchURL(String urlPath) async {
    final Uri url = Uri.parse(urlPath);
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }
}

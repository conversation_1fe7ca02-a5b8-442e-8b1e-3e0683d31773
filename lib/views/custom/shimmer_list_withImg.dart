import 'package:fringle_app/helpers/constants.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ShimmerListWithImg extends StatelessWidget {
  final double? height;

  const ShimmerListWithImg({super.key, this.height});
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
        itemCount: 10,
        shrinkWrap: true,
        scrollDirection: Axis.vertical,
        itemBuilder: (BuildContext buildContext, int index) {
          return _LoadingItem(
            height: height,
          );
        });
  }
}

class _LoadingItem extends StatelessWidget {
  final double? height;

  const _LoadingItem({super.key, this.height});
  @override
  Widget build(BuildContext context) {
    return Wrap(children: <Widget>[
      Shimmer.fromColors(
        baseColor: Colors.black26,
        highlightColor: Colors.white,
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              // Padding(
              // padding: const EdgeInsets.symmetric(
              //  horizontal: AppConstants.defaultNumericValue),
              const SizedBox(
                height: 24,
              ),
              InkWell(
                onTap: () {},
                child: Container(
                  margin:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    color: AppConstants.primaryColor,
                  ),
                  child: const Center(
                      child: Text(
                    "Upgrade to view profile",
                    style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w400,
                        fontSize: 12),
                  )),
                ),
              ),
              Container(
                margin: const EdgeInsets.only(top: 24, left: 24, right: 24),
                child: Wrap(
                  spacing: 12,
                  runSpacing: 12,
                  alignment: WrapAlignment.start,
                  children: List.generate(10, (index) {
                    String image = '';
                    String name = '';
                    //String userId = '';

                    return Container(
                      child: SizedBox(
                          width: MediaQuery.of(context).size.width * 0.4,
                          height: 220,
                          child: Container(
                            decoration: const BoxDecoration(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10)),
                            ),
                            child: Center(
                              child: Stack(children: <Widget>[
                                Container(
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(10),
                                    child: InkWell(
                                      onTap: () {},
                                      child: Image.asset(AppConstants.dummy,
                                          width: 166,
                                          height: 220,
                                          fit: BoxFit.cover),
                                    ),
                                  ),
                                ),
                              ]),
                            ),
                          )),
                    );
                  }),
                ),
              ),
            ]),
      )
    ]);
  }
}

// import 'dart:io' show Platform;
// import 'package:fringle_app/config/config.dart';
// import 'package:purchases_flutter/purchases_flutter.dart';

// Future<void> initPlatformStateForPurchases(String? userId) async {
//   PurchasesConfiguration configuration;
//   if (Platform.isIOS) {
//     configuration = PurchasesConfiguration(SubscriptionConstants.appleApiKey);

//     if (userId != null && userId != "") {
//       configuration.appUserID = userId;
//     }

//     await Purchases.configure(configuration);

//     if (userId != null && userId != "") {
//       await Purchases.logIn(userId);
//     }
//   } else if (Platform.isAndroid) {
//     configuration = PurchasesConfiguration(SubscriptionConstants.googleApiKey);

//     if (userId != null) {
//       configuration.appUserID = userId;
//     }

//     await Purchases.configure(configuration);

//     if (userId != null && userId.isNotEmpty) {
//       await Purchases.logIn(userId);
//     }
//   }
// }

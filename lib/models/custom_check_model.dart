class CustomCheckModel {
  final int id;
  final String name;
  final String selectedIcon;
  final String unselectedIcon;
  bool isSelected;

  CustomCheckModel({
    required this.id,
    required this.name,
    required this.selectedIcon,
    required this.unselectedIcon,
    this.isSelected = false,
  });

  CustomCheckModel copyWith({bool? isSelected}) {
    return CustomCheckModel(
      id: id,
      name: name,
      selectedIcon: selectedIcon,
      unselectedIcon: unselectedIcon,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}

class CustomCipsModel {
  final int id;
  final String name;
  bool isSelected;

  CustomCipsModel({
    required this.id,
    required this.name,
    this.isSelected = false,
  });

  CustomCipsModel copyWith({bool? isSelected}) {
    return CustomCipsModel(
      id: id,
      name: name,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}

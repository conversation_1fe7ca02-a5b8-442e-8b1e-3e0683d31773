import 'dart:ui';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geolocator/geolocator.dart';
import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/models/user_profile_model.dart';
import 'package:fringle_app/providers/user_profile_provider.dart';
import 'package:fringle_app/views/others/user_details_page.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:swipe_cards/swipe_cards.dart';

class UserCardWidget extends StatefulWidget {
  final UserProfileModel user;
  final VoidCallback onTapCross;
  final VoidCallback onTapHeart;
  final VoidCallback onTapBolt;
  final int? totalInteraction;
  final MatchEngine? matchEngine;

  const UserCardWidget({
    Key? key,
    required this.user,
    required this.onTapCross,
    required this.onTapHeart,
    required this.onTapBolt,
    this.totalInteraction,
    this.matchEngine,
  }) : super(key: key);

  @override
  State<UserCardWidget> createState() => _UserCardWidgetState();
}

class _UserCardWidgetState extends State<UserCardWidget> {
  final List<String> _images = [];
  var currentPage = 0;

  @override
  void initState() {
    if (widget.user.imageList != null &&
        widget.user.imageList?.isNotEmpty == true) {
      _images.addAll(widget.user.imageList!);
    }
    // for (var image in widget.user.mediaFiles) {
    //   _images.add(image);
    // }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GridTile(
      // header: widget.user.isOnline
      //     ? const Align(
      //         alignment: Alignment.topRight,
      //         child: Padding(
      //           padding: EdgeInsets.all(0),
      //           child: OnlineStatus(),
      //         ),
      //       )
      //     :
      // SizedBox(),
      footer: GestureDetector(
        onTap: () {
          Navigator.push(
              context,
              CupertinoPageRoute(
                  builder: (context) => UserDetailsPage(
                        user: widget.user,
                        totalInteraction: widget.totalInteraction,
                        //matchEngine: widget.matchEngine
                      )));
        },
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(AppConstants.defaultNumericValue),
            bottomRight: Radius.circular(AppConstants.defaultNumericValue),
          ),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
            child: Container(
              width: double.infinity,
              decoration: const BoxDecoration(
                  gradient: LinearGradient(
                colors: [
                  Colors.transparent,
                  Color.fromARGB(255, 0, 0, 0),
                  Color.fromARGB(255, 0, 0, 0)
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              )),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: AppConstants.defaultNumericValue),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultNumericValue),
                        child: Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text: "${widget.user.firstName} ${widget.user.lastName}",
                                style: GoogleFonts.varela(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w700,
                                  fontSize: 24,
                                ),
                              ),
                              if (widget.user.userAccountSettingsModel.showAge != false) ...[
                                TextSpan(
                                  text: " ${DateTime.now().difference(widget.user.birthDay!).inDays ~/ 365}",
                                  style: GoogleFonts.varela(
                                    color: Colors.white,
                                    fontSize: 20,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),
                      widget.user.isVerified
                          ? GestureDetector(
                              onTap: () {
                                EasyLoading.showToast('Verified User!');
                              },
                              child: const Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal:
                                        AppConstants.defaultNumericValue),
                                child: Icon(Icons.verified_user,
                                    color: CupertinoColors.activeGreen),
                              ),
                            )
                          : const SizedBox(),
                    ],
                  ),
                  Consumer(
                    builder: (context, ref, child) {
                      final myProfile = ref.watch(userProfileFutureProvider);
                      return myProfile.when(
                          data: (data) {
                            if (data != null) {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  const SizedBox(
                                      height:
                                          AppConstants.defaultNumericValue / 4),
                                  Wrap(
                                    alignment: WrapAlignment.start,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            left: AppConstants
                                                    .defaultNumericValue /
                                                1.2),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Image.asset(
                                                AppConstants.locationIcon),
                                            // SvgPicture.asset(
                                            //     AppConstants.location),
                                            const SizedBox(
                                                width: AppConstants
                                                        .defaultNumericValue /
                                                    4),

                                            Text(
                                              '${((Geolocator.distanceBetween(data.latitude ?? 0, data.longitude ?? 0, widget.user.latitude ?? 0, widget.user.longitude ?? 0) / 1000)* 0.621371).toStringAsFixed(2)} miles away',
                                              style: GoogleFonts.manrope(
                                                color: Colors.white,
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                      // Padding(
                                      //   padding: const EdgeInsets.only(
                                      //       left: AppConstants
                                      //               .defaultNumericValue /
                                      //           1.2),
                                      //   child: InterestsSimilarityWidget(
                                      //     otherUser: widget.user,
                                      //     myProfile: data,
                                      //   ),
                                      // )
                                    ],
                                  ),
                                  const SizedBox(
                                      height: AppConstants.defaultNumericValue),
                                  UserLikeActions(
                                    onTapCross: widget.onTapCross,
                                    onTapBolt: widget.onTapBolt,
                                    onTapHeart: widget.onTapHeart,
                                    showShadow: true,
                                  ),
                                  const SizedBox(
                                      height: AppConstants.defaultNumericValue),
                                ],
                              );
                            } else {
                              return const SizedBox();
                            }
                          },
                          error: (_, __) => const SizedBox(),
                          loading: () => const SizedBox());
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      child: _images.isEmpty
          ? Center(child: Image.asset(AppConstants.dummy))
          : Stack(
              fit: StackFit.expand,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius:
                        BorderRadius.circular(AppConstants.defaultNumericValue),
                  ),
                  child: ClipRRect(
                    borderRadius:
                        BorderRadius.circular(AppConstants.defaultNumericValue),
                    child: CachedNetworkImage(
                      // imageUrl:
                      //     _images[currentPage], // Replace with your image URL
                      imageUrl: widget.user.imageList != null && widget.user.imageList!.isNotEmpty && currentPage < widget.user.imageList!.length
                          ? widget.user.imageList![currentPage]
                          : "",
                      fit: BoxFit.cover,
                      progressIndicatorBuilder:
                          (context, url, downloadProgress) {
                        // Show a loading indicator while the image is loading.
                        return Center(
                          child: CircularProgressIndicator(
                            value: downloadProgress.progress,
                          ),
                        );
                      },
                      errorWidget: (context, url, error) {
                        // Show an error icon if there's an issue loading the image.
                        return const Center(
                          child: Icon(CupertinoIcons.photo),
                        );
                      },
                    ),
                  ),
                ),
                Positioned(
                  top: 16,
                  left: 0,
                  right: 0,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List<Widget>.generate(
                      _images.length, // Replace with the actual number of pages
                      (int index) {
                        return Container(
                          width: 48,
                          height: 3,
                          margin: const EdgeInsets.symmetric(horizontal: 3),
                          decoration: BoxDecoration(
                            borderRadius:
                                const BorderRadius.all(Radius.circular(10)),
                            shape: BoxShape.rectangle,
                            color: currentPage == index
                                ? Colors.white
                                : const Color.fromARGB(91, 0, 0, 0),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                Positioned.fill(
                  child: Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            if (currentPage > 0) {
                              setState(() {
                                currentPage--;
                              });
                            }
                          },
                          child: Container(
                            color: Colors.transparent,
                          ),
                        ),
                      ),
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            Navigator.push(
                              context,
                              CupertinoPageRoute(
                                builder: (context) => UserDetailsPage(
                                  user: widget.user,
                                  totalInteraction: widget.totalInteraction,
                                  //matchEngine: widget.matchEngine
                                ),
                              ),
                            );
                          },
                          child: Container(
                            color: Colors.transparent,
                          ),
                        ),
                      ),
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            if (currentPage < _images.length - 1) {
                              setState(() {
                                currentPage++;
                              });
                            }
                          },
                          child: Container(
                            color: Colors.transparent,
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
    );
  }
}

class InterestsSimilarityWidget extends StatelessWidget {
  final UserProfileModel otherUser;
  final UserProfileModel myProfile;
  final Color? color;
  const InterestsSimilarityWidget({
    super.key,
    required this.otherUser,
    required this.myProfile,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final myInterests = myProfile.interests;
    final otherInterests = otherUser.interests;

    double similarity = 0;
    for (final interest in myInterests) {
      if (otherInterests.contains(interest)) {
        similarity++;
      }
    }

    double percentage = (similarity / myInterests.length) * 100;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.join_inner,
          size: 16,
          color: color ?? Colors.white,
        ),
        const SizedBox(width: AppConstants.defaultNumericValue / 4),
        Text(
          '${percentage.toStringAsFixed(0)}% similarity',
          style: TextStyle(
            color: color ?? Colors.white,
            fontWeight: FontWeight.bold,
          ),
        )
      ],
    );
  }
}

class UserLikeActions extends StatelessWidget {
  final VoidCallback onTapCross;
  final VoidCallback onTapBolt;
  final VoidCallback onTapHeart;
  final bool showShadow;
  const UserLikeActions({
    Key? key,
    required this.onTapCross,
    required this.onTapBolt,
    required this.onTapHeart,
    this.showShadow = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    List<BoxShadow> boxShadow = showShadow
        ? const [
            BoxShadow(
              color: Colors.black45,
              spreadRadius: 4,
              blurRadius: 8,
              offset: Offset(0, 2), // changes position of shadow
            ),
          ]
        : [];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultNumericValue),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          _buildIconButton(
            iconPath: AppConstants.dislike,
            iconSize: 24,
            ringColor: Color(0xFFFF4C61),
            onTap: onTapCross,
          ),
          // SizedBox(width: 32),
          _buildIconButton(
            iconPath: AppConstants.favoriteBlue,
            iconSize: 24,
            ringColor: Color(0xFF1990D7),
            onTap: onTapBolt,
          ),
          // SizedBox(width: 32),
          _buildIconButton(
            iconPath: AppConstants.likeGreen,
            iconSize: 24,
            ringColor: Color(0xFF00C48C),
            onTap: onTapHeart,
          ),
        ],
      ),
    );
  }

  Widget _buildIconButton({
    required String iconPath,
    required double iconSize,
    required Color ringColor,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.black,
          shape: BoxShape.circle,
        ),
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: ringColor),
          ),
          child: SvgPicture.asset(
            iconPath,
            height: iconSize,
            width: iconSize,
          ),
        ),
      ),
    );
  }
}

class OnlineStatus extends StatelessWidget {
  const OnlineStatus({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        color: const Color.fromARGB(255, 3, 200, 10),
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            spreadRadius: 0,
            blurRadius: 2,
            offset: Offset(0, 1), // changes position of shadow
          ),
        ],
      ),
      child: Text(
        'Online',
        style: Theme.of(context).textTheme.bodySmall!.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 11,
          shadows: [
            const Shadow(
              blurRadius: 2.0,
              color: Colors.black26,
              offset: Offset(1.0, 1.0),
            ),
          ],
        ),
      ),
    );
  }
}

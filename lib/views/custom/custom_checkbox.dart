import 'package:fringle_app/providers/interaction_provider.dart';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class CustomCheckbox extends ConsumerStatefulWidget {
  final bool initialValue;

  const CustomCheckbox({
    super.key,
    required this.initialValue,
  });

  @override
  _CustomCheckboxState createState() => _CustomCheckboxState();
}

class _CustomCheckboxState extends ConsumerState<CustomCheckbox> {
  bool _isChecked = false;

  @override
  void initState() {
    super.initState();
    _isChecked = widget.initialValue;
  }

  showConfirmationDialog(BuildContext context) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Confirmation'),
        content: Consumer(builder: (context, watch, _) {
          final isChecked = ref.watch(checkboxStateProvider);
          //  return const Text('Are you sure you want to change the value?');
          return Text(isChecked
              ? 'Are you sure you want to change the value?'
              : 'Are you sure you want to change the value?');
        }),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Navigator.of(context).push(MaterialPageRoute(builder: (context){
              //   return MembershipScreen();
              // }));
              // setState(() {
              //   _isChecked = !_isChecked;
              // });
              // Handle the state change using Riverpod
              // ref.read(checkboxStateProvider.notifier).state=!ref.read(checkboxStateProvider.notifier).state;
              // ref.read(checkboxStateProvider.notifier).state = _isChecked;
              // Navigator.of(context).pop();
              Navigator.of(context).pop();
              // Navigator.of(context).push(MaterialPageRoute(builder: (context){
              //   return MembershipScreen();
              // }));
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _toggleCheckbox() async {
    await showConfirmationDialog(context);
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: _toggleCheckbox,
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: _isChecked ? Colors.blue : Colors.grey,
            width: 2,
          ),
        ),
        child: ref.read(checkboxStateProvider.notifier).state
            ? Icon(
                Icons.check,
                size: 16,
                color: Colors.blue,
              )
            : null,
      ),
    );
  }
}

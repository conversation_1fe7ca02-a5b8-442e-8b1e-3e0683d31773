import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fringle_app/providers/account_delete_request_provider.dart';
import 'package:fringle_app/providers/device_token_provider.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../helpers/constants.dart';
import '../views/custom/app_toast.dart';
import '../views/custom/custom_app_loader.dart';
import 'user_profile_provider.dart';
import 'package:http/http.dart' as http;

class FirebaseUser {
  final String uid;

  User? user;

  FirebaseUser({
    required this.uid,
    required this.user,
  });
}

final authStateProvider = StreamProvider<User?>((ref) {
  return FirebaseAuth.instance.authStateChanges().map((user) {
    var userUid = "";
    final userCollection =
        FirebaseFirestore.instance.collection(FirebaseConstants.coupleAccount);
    userCollection
        .where("userId", isEqualTo: user?.uid)
        .get()
        .then((data) async {
      var prefs = await SharedPreferences.getInstance();

      if (data.docs.isNotEmpty) {
        userUid = data.docs.first.data()['coupleId'];
        ref.read(currentUserStateProvider.notifier).state =
            FirebaseUser(user: user, uid: userUid);
        if (user?.uid != null) {
          userCollection.doc(userUid).update({
            'coupleId': userUid,
            'userId': user?.uid,
          });
          prefs.setBool(AppConstants.coupleAccount, true);
          await DeviceTokenProvider().saveDeviceToken(userUid);
        }
      } else {
        userUid = user?.uid ?? "";
        if (prefs.getBool(AppConstants.coupleAccount) ?? false) {
          ref
              .read(userProfileNotifier)
              .updateOnlineStatus(isOnline: false, userId: userUid);
          ref.read(authProvider).signOut();
          prefs.setBool(AppConstants.coupleAccount, false);
          return;
        }
        ref.read(currentUserStateProvider.notifier).state =
            FirebaseUser(user: user, uid: userUid);
        prefs.setBool(AppConstants.coupleAccount, false);
      }
    });
    if (user != null) {
      AccountDeleteProvider.getAccountDeleteRequest(user.uid).then((value) {
        if (value != null) {
          AccountDeleteProvider.cancelAccountDeleteRequest(user.uid);
        }
      });
    }
    return user;
  });
});

final currentUserStateProvider = StateProvider<FirebaseUser?>((ref) {
  return null;
});

final authProvider = Provider<AuthProvider>((ref) {
  return AuthProvider();
});

final isEmailVerifiedProvider = FutureProvider<bool>((ref) async {
  final user = ref.watch(currentUserStateProvider);
  if (user == null) {
    return false;
  }
  await user.user?.reload();
  return user.user?.emailVerified ?? false;
});

class AuthProvider {
  final _deviceTokenProvider = DeviceTokenProvider();

  Future<User?> signUpWithEmailAndPassword(
      String email, String password) async {
    try {
      final userCred =
          await FirebaseAuth.instance.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      // await _deviceTokenProvider.saveDeviceToken(userCred.user!.uid);
      return userCred.user;
    } on FirebaseAuthException catch (e) {
      String error = AuthProvider.getFirebaseAuthErrorMessage(e);
      CustomToast.showToast(message: error);
      return null;
    } catch (e) {
      CustomToast.showToast(message: 'Something went wrong.');
      return null;
    }
  }

  static Future<bool> sendEmailVerification(User user) async {
    try {
      await user.sendEmailVerification();
      return true;
    } on FirebaseAuthException catch (e) {
      String error = AuthProvider.getFirebaseAuthErrorMessage(e);
      CustomToast.showToast(message: error);
      return false;
    } catch (e) {
      CustomToast.showToast(message: 'Something went wrong.');
      return false;
    }
  }

  Future<User?> signInWithEmailAndPassword(
      String email, String password) async {
    try {
      final userCred = await FirebaseAuth.instance.signInWithEmailAndPassword(email: email, password: password);

      // await _deviceTokenProvider.saveDeviceToken(userCred.user!.uid);
      return userCred.user;
    } on FirebaseAuthException catch (e) {
      String error = AuthProvider.getFirebaseAuthErrorMessage(e);
      CustomToast.showToast(message: error);
      return null;
    } catch (e) {
      CustomToast.showToast(message: 'Something went wrong.');
    }
    return null;
  }

  static Future<bool> forgotPassword({required String email}) async {
    try {
      CustomAppLoader.showCustomLoader("Sending reset password email...");
      await FirebaseAuth.instance.sendPasswordResetEmail(email: email);
      CustomToast.showToast(message: 'Reset password link sent to $email');
      return true;
    } on FirebaseAuthException catch (e) {
      String error = AuthProvider.getFirebaseAuthErrorMessage(e);
      CustomToast.showToast(message: error);
      return false;
    } catch (e) {
      CustomToast.showToast(message: 'Something went wrong.');
      return false;
    }
  }

  Future<void> signOut() async {
    try {
    // Purchases.logOut();
      // await _deviceTokenProvider.deleteDeviceToken();
    // await GoogleSignIn().signOut();
    //await FacebookAuth.instance.logOut();
    await FirebaseAuth.instance.signOut();
    await Hive.box(HiveConstants.hiveBox).clear();
    await Hive.box(HiveConstants.hiveBox).put(HiveConstants.onboardingCompleted, true);
    } on FirebaseAuthException catch (e) {
      String error = AuthProvider.getFirebaseAuthErrorMessage(e);
      CustomToast.showToast(message: error);
    } catch (e) {
      CustomToast.showToast(message: 'Something went wrong.');
    }
  }

  Future<bool> deleteUser(String uid) async {
    final response = await http.post(
      Uri.parse(
          'https://us-central1-fluster-76cd2.cloudfunctions.net/deleteUser?uid=$uid'),
      body: '',
    );
    return (response.statusCode == 200);
  }

  Future<String> createNewAccount(String email, String password) async {
    final response = await http.post(
      Uri.parse(
          'https://us-central1-fluster-76cd2.cloudfunctions.net/createUser'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'email': email, 'pass': password}),
    );

    if (response.statusCode == 200) {
      // Parse the JSON response
      final responseBody = jsonDecode(response.body);
      // Extract the message and data
      //final String message = responseBody['message'];
      final String data = responseBody['data'];
      return data;
    } else {
      print('Failed to create user');
      return "";
    }
  }

  static String getFirebaseAuthErrorMessage(FirebaseAuthException e) {
    switch (e.code) {
      case 'invalid-email':
        return 'The email address is not valid.';
      case 'user-disabled':
        return 'This user account has been disabled.';
      case 'user-not-found':
        return 'No account found for that email.';
      case 'wrong-password':
        return 'Incorrect password.';
      case 'email-already-in-use':
        return 'The email is already in use by another account.';
      case 'operation-not-allowed':
        return 'This sign-in method is not allowed. Please contact support.';
      case 'weak-password':
        return 'The password is too weak.';
      case 'too-many-requests':
        return 'Too many attempts. Please try again later.';
      case 'network-request-failed':
        return 'Network error. Please check your internet connection.';
      case 'requires-recent-login':
        return 'Please log in again to complete this action.';
      case 'account-exists-with-different-credential':
        return 'An account already exists with the same email but different sign-in credentials.';
      case 'invalid-credential':
        return 'The credential is invalid or has expired.';
      default:
        return 'An unexpected error occurred. [${e.code}]';
    }
  }
}

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart' as http;

import 'package:fringle_app/config/config.dart';
import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/helpers/get_location_prediction.dart';
import 'package:fringle_app/models/location_prediction_model.dart';
import 'package:fringle_app/models/user_account_settings_model.dart';
import 'package:fringle_app/providers/country_codes_provider.dart';
import 'package:fringle_app/providers/get_current_location_provider.dart';
import 'package:fringle_app/views/others/error_page.dart';

import '../custom/custom_app_loader.dart';
import '../custom/custom_status_bar_theme.dart';

class SetUserLocation extends ConsumerStatefulWidget {
  const SetUserLocation({super.key});

  @override
  ConsumerState<SetUserLocation> createState() => _SetUserLocationState();
}

class _SetUserLocationState extends ConsumerState<SetUserLocation> {
  final _searchController = TextEditingController();

  final List<Prediction> _predictions = [];

  @override
  void initState() {
    _searchController.addListener(() async {
      if (_searchController.text.isNotEmpty &&
          _searchController.text.length > 2) {
        final results =
            await getLocationPrediction(_searchController.text.trim());
        if (results != null) {
          setState(() {
            _predictions.clear();
            _predictions.addAll(results);
          });
        }
      } else {
        _predictions.clear();
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentLocationProviderProvider =
        ref.watch(getCurrentLocationProviderProvider);

    final countryCodesData = ref.watch(countryCodesProvider);

    return CustomStatusBarTheme(
      isLightTheme: false,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          automaticallyImplyLeading: false,
          elevation: 2,
          shadowColor: Colors.white.withOpacity(0.6),
          title: Text(
            "Set Your Location",
            style: GoogleFonts.manrope(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          leading: IconButton(
            icon: SvgPicture.asset(
              AppConstants.backIcon,
              color: Colors.black,
              height: 24,
              width: 24,
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ),
        body: countryCodesData.when(
            data: (data) {
              return currentLocationProviderProvider.when(
                  data: (location) {
                    return SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // if (location != null)
                          //   const Padding(
                          //     padding: EdgeInsets.only(left: 24),
                          //     child: Text(
                          //       "Set Your Location",
                          //       style: TextStyle(
                          //           fontFamily: AppConstants.fontStyleName,
                          //           fontSize: 20,
                          //           fontWeight: FontWeight.w500,
                          //           color: Colors.black),
                          //     ),
                          //   ),
                          // if (location != null)
                          //   const SizedBox(
                          //     height: 14,
                          //   ),
                          // if (location != null)
                          //   ListTile(
                          //     onTap: () {
                          //       Navigator.of(context).pop(location);
                          //     },
                          //     title: const Text("Current Location"),
                          //     subtitle: Text(location.addressText),
                          //     leading: const Icon(Icons.location_on),
                          //     minLeadingWidth: 0,
                          //   ),
                          const SizedBox(height: 14),
                          if (location != null)
                            Padding(
                              padding: const EdgeInsets.only(left: 24),
                              child: Text(
                                "Location",
                                style: GoogleFonts.manrope(
                                  fontWeight: FontWeight.w400,
                                  fontSize: 12,
                                  color: Color(0xFF6A6A6A),
                                ),
                              ),
                            ),
                          if (location == null) const SizedBox(height: 6),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 24),
                            child: TextField(
                              controller: _searchController,
                              decoration: InputDecoration(
                                filled: true,
                                fillColor: Colors.white,
                                hintText: "Search for a location",
                                prefixIcon: Container(
                                  height: 20,
                                  width: 20,
                                  padding: const EdgeInsets.all(14),
                                  child: SvgPicture.asset(
                                    AppConstants.searchIcon,
                                  ),
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: const BorderSide(
                                      color: Color(0xFFB5B4B4), width: 1.0),
                                ),
                                enabledBorder: OutlineInputBorder(
                                    borderSide: const BorderSide(
                                        color: Color(0xFFB5B4B4), width: 1.0),
                                    borderRadius: BorderRadius.circular(12.0)),
                                focusedBorder: OutlineInputBorder(
                                    borderSide: const BorderSide(
                                        color: Color(0xFFB5B4B4), width: 1.0),
                                    borderRadius: BorderRadius.circular(12.0)),
                              ),
                            ),
                          ),
                          if (_predictions.isEmpty &&
                              _searchController.text.isNotEmpty)
                            const SizedBox(
                              height: 300,
                              child: Center(
                                child: Text("No results found"),
                              ),
                            ),
                          if (_predictions.isEmpty &&
                              _searchController.text.isEmpty)
                            const SizedBox(
                              height: 300,
                              child: Center(
                                child: Text("Find a location"),
                              ),
                            ),
                          ..._predictions.map(
                            (e) {
                              return e.description != null
                                  ? Padding(
                                      padding: const EdgeInsets.only(
                                          left: 14, right: 14),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          ListTile(
                                            onTap: () async {
                                              CustomAppLoader.showCustomLoader("Please wait...");
                                              
                                              await getLocationFromPlaceID(
                                                      e.placeId!)
                                                  .then((value) {
                                                if (value != null) {
                                                  final userLocation =
                                                      UserLocation(
                                                    addressText: e.description!,
                                                    latitude: value.lat,
                                                    longitude: value.long,
                                                  );
                                                  EasyLoading.dismiss();

                                                  Navigator.of(context)
                                                      .pop(userLocation);
                                                } else {
                                                  EasyLoading.dismiss();
                                                  Navigator.of(context).pop();
                                                }
                                              });
                                            },
                                            title: Text(
                                              e.description!,
                                              style: const TextStyle(
                                                  color: Color(0xFF303030),
                                                  fontFamily: AppConstants
                                                      .fontStyleName,
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w400),
                                            ),
                                          ),
                                          const Padding(
                                            padding: EdgeInsets.only(
                                                left: 12, right: 12),
                                            child: Divider(
                                                height: 1,
                                                color: Color(0xFFD1D1D1)),
                                          ),
                                        ],
                                      ),
                                    )
                                  : const SizedBox();
                            },
                          ).toList()
                        ],
                      ),
                    );
                  },
                  error: (_, e) {
                    return const ErrorPage();
                  },
                  loading: () => Container(
                        color: Colors.white,
                        height: MediaQuery.of(context).size.height,
                        child: const Center(
                          child: Text(
                            "No results found\nPlease try after sometime.",
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ));
            },
            error: (_, e) {
              return const ErrorPage();
            },
            loading: () => Container(
                  color: Colors.white,
                  height: MediaQuery.of(context).size.height,
                  child: const Center(
                    child: Text(
                      "No results found\nPlease try after sometime.",
                      textAlign: TextAlign.center,
                    ),
                  ),
                )),
      ),
    );
  }
}

Future<LocationComponents?> getLocationFromPlaceID(String placeId) async {
  final url = Uri.parse(
      "https://maps.googleapis.com/maps/api/place/details/json?placeid=$placeId&key=$locationApiKey");

  var response = await http.get(url, headers: {"Accept": "application/json"});

  if (response.statusCode == 200) {
    var data = json.decode(response.body);

    if (data["status"] != "OK") {
      return null;
    } else {
      double? lat = data["result"]["geometry"]["location"]["lat"];
      double? long = data["result"]["geometry"]["location"]["lng"];

      if (lat != null && long != null) {
        return LocationComponents(lat: lat, long: long);
      }
    }
  } else {
    return null;
  }
  return null;
}

class LocationComponents {
  double lat;
  double long;
  LocationComponents({
    required this.lat,
    required this.long,
  });
}

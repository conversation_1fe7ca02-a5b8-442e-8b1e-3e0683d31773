import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';

Future<String?> pickMedia({bool isVideo = false, bool isCamera = false,}) async {
  final ImagePicker picker = ImagePicker();

  if (isVideo) {
    // Use image_picker for video as it handles camera/gallery selection well
    final XFile? pickedFile = await picker.pickVideo(
      source: isCamera ? ImageSource.camera : ImageSource.gallery,
      maxDuration: isCamera ? const Duration(seconds: 8) : const Duration(hours: 2)
    );
    return pickedFile?.path;
  } else if (isCamera) {
    // Use image_picker for camera as it handles camera access well
    final XFile? pickedFile = await picker.pickImage(source: ImageSource.camera);
    return pickedFile?.path;
  } else {
    // Use file_picker for gallery images to support HEIF and other formats
    final FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'heic', 'heif'],
      allowMultiple: false,
    );

    if (result != null && result.files.single.path != null) {
      return result.files.single.path;
    }
    return null;
  }
}

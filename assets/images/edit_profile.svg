<svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1345_612)">
<rect x="12" y="8" width="32" height="32" rx="10.6667" fill="white"/>
<path d="M34.6359 20.2211C34.9331 19.924 34.9331 19.4287 34.6359 19.1468L32.8531 17.364C32.5712 17.0668 32.0759 17.0668 31.7788 17.364L30.3769 18.7583L33.234 21.6154M21.1426 28.0002V30.8573H23.9997L32.4264 22.423L29.5692 19.5659L21.1426 28.0002Z" fill="url(#paint0_linear_1345_612)"/>
</g>
<defs>
<filter id="filter0_d_1345_612" x="0" y="0" width="56" height="56" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.292684 0 0 0 0 0.0434612 0 0 0 0 0.359679 0 0 0 0.46 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1345_612"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1345_612" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1345_612" x1="19.4783" y1="15.0186" x2="23.0905" y2="33.9372" gradientUnits="userSpaceOnUse">
<stop stop-color="#C70973"/>
<stop offset="1" stop-color="#46239F"/>
</linearGradient>
</defs>
</svg>

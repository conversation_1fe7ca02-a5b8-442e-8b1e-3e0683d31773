import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class CustomFeatureRow extends StatelessWidget {
  final String iconPath;
  final String name;
  final Color color;
  const CustomFeatureRow(
      {super.key,
      required this.iconPath,
      required this.name,
      this.color = Colors.black});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SvgPicture.asset(iconPath),
          const SizedBox(
            width: 12,
          ),
          Text(
            name,
            style: TextStyle(
                color: color, fontSize: 14, fontWeight: FontWeight.w500),
          )
        ],
      ),
    );
  }
}
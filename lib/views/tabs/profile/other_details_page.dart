import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../helpers/constants.dart';
import '../../../models/custom_check_model.dart';
import '../../../models/user_profile_model_new.dart';
import '../../custom/app_toast.dart';
import '../../custom/custom_button.dart';
import '../../custom/custom_dropdown_new.dart';
import '../../custom/custom_radio_button.dart';
import '../../custom/custom_textfield.dart';

class OtherDetailsScreen extends StatefulWidget {
  final UserProfileModelNew userProfileModelNew;
  final Function(UserProfileModelNew) onSelected;

  const OtherDetailsScreen({
    super.key,
    required this.userProfileModelNew,
    required this.onSelected,
  });

  @override
  State<OtherDetailsScreen> createState() => _OtherDetailsScreenState();
}

class _OtherDetailsScreenState extends State<OtherDetailsScreen> {
  // TextEditingController workController = TextEditingController();
  // String? selectedEducation;
  // String? selectedReligion;
  // String? selectedExercise;
  // String? selectedDrinking;
  // String? selectedSmoking;
  // String? selectedCannabis;
  // String? selectedKids;
  // String? selectedZodiacSign;

  TextEditingController workController = TextEditingController();
  String? selectedDrinking;
  String? selectedCannabis;
  String? selectedReligion;

  List<CustomCheckModel> drinkingList = AppConstants.createRadioList(AppConstants.drinkingAndCannabisList);
  List<CustomCheckModel> cannabisList = AppConstants.createRadioList(AppConstants.drinkingAndCannabisList);

  final _formKey = GlobalKey<FormState>();

  // void _handleSmokingSelectionChanged(int itemId, bool isSelected) {
  //   setState(() {
  //     smokingList = smokingList.map<CustomCheckModel>((item) {
  //       if (item.id == itemId) {
  //         selectedSmoking = item.name;
  //         return item.copyWith(isSelected: isSelected);
  //       } else {
  //         return item.copyWith(isSelected: false);
  //       }
  //     }).toList();
  //   });
  // }

  void _handleDrinkSelectionChanged(int itemId, bool isSelected) {
    setState(() {
      drinkingList = drinkingList.map<CustomCheckModel>((item) {
        if (item.id == itemId) {
          if (isSelected) {
            selectedDrinking = item.name;
          } else {
            selectedDrinking = null;
          }
          return item.copyWith(isSelected: isSelected);
        } else {
          return item.copyWith(isSelected: false);
        }
      }).toList();
    });
  }

  void _handleCannabisSelectionChanged(int itemId, bool isSelected) {
    setState(() {
      cannabisList = cannabisList.map<CustomCheckModel>((item) {
        if (item.id == itemId) {
          if (isSelected) {
            selectedCannabis = item.name;
          } else {
            selectedCannabis = null;
          }
          return item.copyWith(isSelected: isSelected);
        } else {
          return item.copyWith(isSelected: false);
        }
      }).toList();
    });
  }

  @override
  void initState() {
    super.initState();
    var data = widget.userProfileModelNew;
    workController.text = data.work ?? "";
    // selectedEducation = data.education;
    selectedReligion = data.religion;
    selectedCannabis = data.cannabis;
    // for (var smokingItem in smokingList) {
    //   if (smokingItem.name == selectedSmoking) {
    //     smokingItem.isSelected = true;
    //   } else {
    //     smokingItem.isSelected = false;
    //   }
    // }
    for (var drinkingItem in drinkingList) {
      if (drinkingItem.name == selectedDrinking) {
        drinkingItem.isSelected = true;
      } else {
        drinkingItem.isSelected = false;
      }
    }
  }


  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomTextField2(
                controller: workController,
                labelText: 'Profession',
                hintText: 'Type here...',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your profession';
                  }
                  return null;
                },
                autovalidateMode: AutovalidateMode.onUserInteraction,
              ),
              const SizedBox(height: 24),
              Text(
                "Drinking",
                style: GoogleFonts.manrope(
                  color: Color(0xff6A6A6A),
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(height: 7),
              Wrap(
                spacing: 7,
                runSpacing: 19,
                children: drinkingList.map((item) {
                  return SizedBox(
                    width: (MediaQuery.of(context).size.width - 55) / 2,
                    child: RadioButtonItemWidget(
                      item: item,
                      onSelectionChanged: (isSelected) {
                        _handleDrinkSelectionChanged(item.id, isSelected);
                      },
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(height: 24),
              Text(
                "Cannabis",
                style: GoogleFonts.manrope(
                  color: Color(0xff6A6A6A),
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(height: 7),
              Wrap(
                spacing: 7,
                runSpacing: 19,
                children: cannabisList.map((item) {
                  return SizedBox(
                    width: (MediaQuery.of(context).size.width - 55) / 2,
                    child: RadioButtonItemWidget(
                      item: item,
                      onSelectionChanged: (isSelected) {
                        _handleCannabisSelectionChanged(item.id, isSelected);
                      },
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(height: 24),
              CustomDropdownNew(
                items: listEthnicity,
                selectedValue: selectedReligion,
                onChanged: (newValue) {
                  setState(() {
                    selectedReligion = newValue;
                  });
                },
                labelText: 'Religion',
                hintText: 'Select',
              ),
              const SizedBox(height: 24),
              Container(
                margin: const EdgeInsets.only(top: 24),
                child: CustomButton(
                  text: "Continue",
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      if (selectedDrinking == null) {
                        CustomToast.showToast(message: "Please select drinking option");
                        return;
                      }
                      if (selectedCannabis == null) {
                        CustomToast.showToast(message: "Please select cannabis option");
                        return;
                      }
                      if (selectedReligion == null) {
                        CustomToast.showToast(message: "Please select a religion");
                        return;
                      }
                      var userData = widget.userProfileModelNew.copyWith(
                        work: workController.text,
                        drinking: selectedDrinking,
                        cannabis: selectedCannabis,
                        religion: selectedReligion,
                      );
                      widget.onSelected(userData);
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // @override
  // Widget build(BuildContext context) {
  //   return Form(
  //       key: _formKey,
  //       child: Column(
  //         crossAxisAlignment: CrossAxisAlignment.stretch,
  //         children: [
  //           Column(
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: [
  //               CustomDropdownNew(
  //                 items: listEducation,
  //                 selectedValue: selectedEducation,
  //                 onChanged: (newValue) {
  //                   // Update the selected value
  //                   setState(() {
  //                     selectedEducation = newValue;
  //                   });
  //                 },
  //                 labelText: 'Education',
  //                 hintText: 'Select',
  //               ),
  //               const SizedBox(
  //                 height: 24,
  //               ),
  //               CustomTextField2(
  //                 controller: workController,
  //                 labelText: 'Work',
  //                 hintText: 'Type here...',
  //               ),
  //               const SizedBox(
  //                 height: 24,
  //               ),
  //               CustomDropdownNew(
  //                 items: listKids,
  //                 selectedValue: selectedKids,
  //                 onChanged: (newValue) {
  //                   // Update the selected value
  //                   setState(() {
  //                     selectedKids = newValue;
  //                   });
  //                 },
  //                 labelText: 'Kids',
  //                 hintText: 'Select',
  //                 // validator: (value) {
  //                 //   if (value == null || value.isEmpty) {
  //                 //     return 'Please select an option';
  //                 //   }
  //                 //   return null; // Return null for no validation error
  //                 // },
  //               ),
  //               const SizedBox(
  //                 height: 24,
  //               ),
  //               CustomDropdownNew(
  //                 items: listEthnicity,
  //                 selectedValue: selectedReligion,
  //                 onChanged: (newValue) {
  //                   // Update the selected value
  //                   setState(() {
  //                     selectedReligion = newValue;
  //                   });
  //                 },
  //                 labelText: 'Ethnicity',
  //                 hintText: 'Select',
  //                 // validator: (value) {
  //                 //   if (value == null || value.isEmpty) {
  //                 //     return 'Please select an option';
  //                 //   }
  //                 //   return null; // Return null for no validation error
  //                 // },
  //               ),
  //               const SizedBox(
  //                 height: 24,
  //               ),
  //               CustomDropdownNew(
  //                 items: listZodiacSign,
  //                 selectedValue: selectedZodiacSign,
  //                 onChanged: (newValue) {
  //                   // Update the selected value
  //                   setState(() {
  //                     selectedZodiacSign = newValue;
  //                   });
  //                 },
  //                 labelText: 'Zodiac Sign',
  //                 hintText: 'Select',
  //                 // validator: (value) {
  //                 //   if (value == null || value.isEmpty) {
  //                 //     return 'Please select an option';
  //                 //   }
  //                 //   return null; // Return null for no validation error
  //                 // },
  //               ),
  //               const SizedBox(
  //                 height: 24,
  //               ),
  //               CustomDropdownNew(
  //                 items: listCannabis,
  //                 selectedValue: selectedCannabis,
  //                 onChanged: (newValue) {
  //                   // Update the selected value
  //                   setState(() {
  //                     selectedCannabis = newValue;
  //                   });
  //                 },
  //                 labelText: 'Cannabis',
  //                 hintText: 'Select',
  //                 // validator: (value) {
  //                 //   if (value == null || value.isEmpty) {
  //                 //     return 'Please select an option';
  //                 //   }
  //                 //   return null; // Return null for no validation error
  //                 // },
  //               ),
  //               const SizedBox(
  //                 height: 24,
  //               ),
  //               CustomDropdownNew(
  //                 items: listExercise,
  //                 selectedValue: selectedExercise,
  //                 onChanged: (newValue) {
  //                   // Update the selected value
  //                   setState(() {
  //                     selectedExercise = newValue;
  //                   });
  //                 },
  //                 labelText: 'Exercise',
  //                 hintText: 'Select',
  //                 // validator: (value) {
  //                 //   if (value == null || value.isEmpty) {
  //                 //     return 'Please select an option';
  //                 //   }
  //                 //   return null; // Return null for no validation error
  //                 // },
  //               ),
  //               const SizedBox(
  //                 height: 24,
  //               ),
  //               const Text(
  //                 "Smoking",
  //                 style: TextStyle(
  //                   fontFamily: AppConstants.fontStyleName,
  //                   color: Color(0xff6A6A6A),
  //                   fontSize: 12,
  //                   fontWeight: FontWeight.w400,
  //                   // Additional InputDecoration properties...
  //                 ),
  //               ),
  //               const SizedBox(
  //                 height: 7,
  //               ),
  //               GridView.builder(
  //                 shrinkWrap: true,
  //                 physics: const NeverScrollableScrollPhysics(),
  //                 gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
  //                     crossAxisCount: 2,
  //                     mainAxisSpacing: 19,
  //                     childAspectRatio: 3.0,
  //                     crossAxisSpacing: 7),
  //                 itemCount: smokingList.length,
  //                 itemBuilder: (context, index) {
  //                   return RadioButtonItemWidget(
  //                     item: smokingList[index],
  //                     onSelectionChanged: (isSelected) {
  //                       _handleSmokingSelectionChanged(
  //                           smokingList[index].id, isSelected);
  //                     },
  //                   );
  //                 },
  //               ),
  //               const SizedBox(
  //                 height: 24,
  //               ),
  //               const Text(
  //                 "Drinking",
  //                 style: TextStyle(
  //                   fontFamily: AppConstants.fontStyleName,
  //                   color: Color(0xff6A6A6A),
  //                   fontSize: 12,
  //                   fontWeight: FontWeight.w400,
  //                   // Additional InputDecoration properties...
  //                 ),
  //               ),
  //               const SizedBox(
  //                 height: 7,
  //               ),
  //               GridView.builder(
  //                 shrinkWrap: true,
  //                 physics: const NeverScrollableScrollPhysics(),
  //                 gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
  //                     crossAxisCount: 2,
  //                     mainAxisSpacing: 19,
  //                     childAspectRatio: 3.0,
  //                     crossAxisSpacing: 7),
  //                 itemCount: drinkingList.length,
  //                 itemBuilder: (context, index) {
  //                   return RadioButtonItemWidget(
  //                     item: drinkingList[index],
  //                     onSelectionChanged: (isSelected) {
  //                       _handleDrinkSelectionChanged(
  //                           drinkingList[index].id, isSelected);
  //                     },
  //                   );
  //                 },
  //               ),
  //               const SizedBox(
  //                 height: 24,
  //               ),
  //               Container(
  //                   margin: const EdgeInsets.only(
  //                     top: 24,
  //                   ),
  //                   child: CustomButton(
  //                     text: "Continue",
  //                     onPressed: () {
  //                       if (_formKey.currentState!.validate()) {
  //                         var userData = widget.userProfileModelNew;
  //                         userData.work = workController.text;
  //                         userData.education = selectedEducation;
  //                         userData.religion = selectedReligion;
  //                         userData.cannabis = selectedCannabis;
  //                         widget.onSelected(userData);
  //                       } else {
  //                         EasyLoading.showToast(
  //                             "Please fill in all the mandatory fields to continue",
  //                             toastPosition: EasyLoadingToastPosition.center);
  //                       }
  //                     },
  //                   )),
  //             ],
  //           ),
  //         ],
  //       ));
  // }
}

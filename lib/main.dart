import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
//import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:fringle_app/views/auth/onboarding_page.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:fringle_app/config/config.dart';
import 'package:fringle_app/helpers/config_loading.dart';
import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/providers/auth_providers.dart';
import 'package:fringle_app/views/others/error_page.dart';
import 'package:fringle_app/views/others/loading_page.dart';
import 'package:fringle_app/views/tabs/bottom_nav_bar_page.dart';
// import 'package:purchases_flutter/purchases_flutter.dart';

import 'firebase_options.dart';
import 'views/auth/login_page.dart';

GlobalKey<NavigatorState> navigationKey = GlobalKey();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  //if (isAdmobAvailable) {
  //  MobileAds.instance.initialize();
  //}

  await Firebase.initializeApp(
    //! Generate Firebase options from https://console.firebase.google.com/ and paste it here. You can also use the default options below.
    options: DefaultFirebaseOptions.currentPlatform,
  );
  FirebaseMessaging.onBackgroundMessage(_handleBackgroundNotification);

  await Hive.initFlutter();
  await Hive.openBox(HiveConstants.hiveBox);
  await FastCachedImageConfig.init(clearCacheAfter: const Duration(days: 15));

  configLoading(
    isDarkMode: false,
    foregroundColor: AppConstants.primaryColor,
    backgroundColor: Colors.white,
  );

  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    alert: true,
    badge: true,
    sound: true,
  );
  final NotificationSettings settings =
      await FirebaseMessaging.instance.requestPermission(
    alert: true,
    announcement: false,
    badge: true,
    carPlay: false,
    criticalAlert: false,
    provisional: false,
    sound: true,
  );

  if (settings.authorizationStatus == AuthorizationStatus.authorized) {
  } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
  } else {}

  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations(
        [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);

    return MaterialApp(
      navigatorKey: navigationKey,
      title: AppConfig.appName,
      debugShowCheckedModeBanner: false,
      builder: EasyLoading.init(),
      theme: ThemeData(
        fontFamily: 'Manrope',
        primarySwatch: _primarySwatch,
        textTheme: GoogleFonts.manropeTextTheme(
          Theme.of(context).textTheme,
        ),
        appBarTheme: AppBarTheme(
            elevation: 0,
            centerTitle: true,
            backgroundColor: AppConstants.primaryColor),
      ),
      // home: const SignupScreen(),
      home: const SplashScreen(),
      // home: const FirstTimeUserProfilePage(),

      // home: const LendingPage(),
      // home: const LoadingPage(),
    );
  }
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    Future.delayed(const Duration(seconds: 2), () {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => const LandingWidget(),
        ),
      );
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: LoadingPage());
  }
}

class LandingWidget extends ConsumerStatefulWidget {
  const LandingWidget({super.key});

  @override
  ConsumerState<LandingWidget> createState() => _LandingWidgetState();
}

class _LandingWidgetState extends ConsumerState<LandingWidget> {
  @override
  void initState() {
    // initPlatformStateForPurchases(
    //     ref.read(currentUserStateProvider)?.uid ?? "");
    // WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
    //   Purchases.addCustomerInfoUpdateListener((customerInfo) {
        // final bool isPremiumUser = customerInfo.entitlements
        //         .all[SubscriptionConstants.entitlementId]?.isActive ??
        //     false;
        // final bool isPremiumPlusUser = customerInfo
        //         .entitlements
        //         .all[SubscriptionConstants.entitlementIdPremiumPlus]
        //         ?.isActive ??
        // false;
    //     ref.refresh(isPremiumUserProvider);
    //     ref.refresh(isPremiumPlusUserProvider);
    //   });
    // });
    // if (mounted) {

    // }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authStateProvider);
    return authState.when(
      data: (data) {
        if (data != null) {
          if (FirebaseAuth.instance.currentUser!.emailVerified) {
            return BottomNavBarPage(
                isPremiumPlusUser: true,
                isPremiumUser: true,
                userId: ref.watch(currentUserStateProvider)?.uid ?? "");
          } else {
            return const LoginPage();
          }
        } else {
          final onboardingCompleted = Hive.box(HiveConstants.hiveBox).get(HiveConstants.onboardingCompleted, defaultValue: false) as bool;
          if (onboardingCompleted) {
            return const LoginPage();
          } else {
            return const OnboardingPage();
          }
        }
      },
      error: (_, e) {
        return const ErrorPage();
      },
      loading: () => const LoadingPage(),
    );
  }
}

Future<void> _handleBackgroundNotification(RemoteMessage message) async {
  await Firebase.initializeApp();
}

final _primarySwatch = MaterialColor(AppConstants.primaryColor.value, _swatch);
final _swatch = {
  50: AppConstants.primaryColor.withOpacity(0.1),
  100: AppConstants.primaryColor.withOpacity(0.2),
  200: AppConstants.primaryColor.withOpacity(0.3),
  300: AppConstants.primaryColor.withOpacity(0.4),
  400: AppConstants.primaryColor.withOpacity(0.5),
  500: AppConstants.primaryColor.withOpacity(0.6),
  600: AppConstants.primaryColor.withOpacity(0.7),
  700: AppConstants.primaryColor.withOpacity(0.8),
  800: AppConstants.primaryColor.withOpacity(0.9),
  900: AppConstants.primaryColor.withOpacity(1),
};

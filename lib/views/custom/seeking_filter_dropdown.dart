import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:fringle_app/helpers/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SeekingFilterDropdown extends StatefulWidget {
  final List<String> items;
  final String? selectedValue;
  final ValueChanged<String> onChanged;
  final String labelText;
  final String hintText;
  final bool isEnable;
  final FormFieldValidator<String>? validator;

  const SeekingFilterDropdown({
    super.key,
    required this.items,
    required this.selectedValue,
    required this.onChanged,
    required this.labelText,
    required this.hintText,
    this.isEnable = true,
    this.validator,
  });

  @override
  State<SeekingFilterDropdown> createState() => _SeekingFilterDropdownState();
}

class _SeekingFilterDropdownState extends State<SeekingFilterDropdown> {
  bool isClickable = false;
  @override
  Widget build(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.labelText,
            style: const TextStyle(
              fontFamily: AppConstants.fontStyleName,
              color: Color(0xff6A6A6A),
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(
            height: 6,
          ),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.all(Radius.circular(12)),
              border: Border.all(color: const Color(0xFFB5B4B4), width: 1),
            ),
            padding: const EdgeInsets.fromLTRB(5, 5, 15, 5),
            width: double.infinity,
            child: DropdownButton2<String>(
              iconStyleData: IconStyleData(
                icon: SvgPicture.asset(
                  AppConstants.dropdownIcon,
                  height: 24,
                  width: 24,
                ),
              ),
              value: widget.selectedValue,
              isExpanded: true,
              hint: Text(
                widget.hintText,
                style: const TextStyle(
                  fontFamily: AppConstants.fontStyleName,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: Colors.black,
                ),
                textAlign: TextAlign.start,
              ),
              items: seekingFilterListWithPremiumPlus.map((String item) {
                // isClickable =
                isClickable = widget.items.contains(item);
                return DropdownMenuItem<String>(
                  value: item,
                  onTap: isClickable
                      ? () {
                          if (widget.isEnable) {
                            widget.onChanged(item);
                            // Navigator.of(context).pop();
                          }
                        }
                      : null,
                  child: Text(
                    (item == "Couples")
                        ? "Couples (Premium)"
                        : (item == "Kinksters")
                            ? "Kinksters (Premium)"
                            : (item == "Collaborators")
                                ? "Collaborators (Premium+)"
                                : item,
                    style: TextStyle(
                      fontSize: 14,
                      color: isClickable ? Colors.black : Colors.grey,
                    ),
                  ),
                );
              }).toList(),
              onChanged: widget.isEnable
                  ? (String? value) {
                      if (widget.items.contains(value)) {
                        widget.onChanged(value ?? "");
                      }

                      // Navigator.of(context).pop();
                    }
                  : null,
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../helpers/constants.dart';
import '../../models/custom_check_model.dart';

class RadioButtonItemWidget extends StatelessWidget {
  final CustomCheckModel item;
  final ValueChanged<bool> onSelectionChanged;

  const RadioButtonItemWidget({
    super.key,
    required this.item,
    required this.onSelectionChanged,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onSelectionChanged(!item.isSelected);
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: item.isSelected ? null : Colors.white,
          border: item.isSelected
              ? Border.all(
                  color: Colors.transparent, // Border color
                  width: 1, // Border width
                )
              : Border.all(
                  color: const Color(0xFFB5B4B4), // Border color
                  width: 1, // Border width
                ),
          gradient: item.isSelected
              ? AppConstants.defaultGradient
              : null,
        ),
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Center(
            child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              item.isSelected ? item.selectedIcon : item.unselectedIcon,
            ),
            const SizedBox(width: 32),
            Text(
              item.name,
              style: GoogleFonts.manrope(
                color: item.isSelected ? Colors.white : const Color(0xFF222222),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        )),
      ),
    );
  }
}

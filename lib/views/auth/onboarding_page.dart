import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../../helpers/constants.dart';
import '../custom/custom_button.dart';
import 'login_page.dart';

class OnboardingPage extends StatefulWidget {
  const OnboardingPage({super.key});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> {
  final PageController _pageController = PageController();
  final CarouselSliderController imageCarouselController =
      CarouselSliderController();
  int _currentPage = 0;

  final List<Map<String, String>> onboardingData = [
    {
      "image":
          "assets/images/onboard1.png", // Replace with your local asset paths
      "title": "Find real friends, not just matches.",
      "subtitle": "Connect with like-minded people nearby who share your vibe.",
    },
    {
      "image": "assets/images/onboard2.png",
      "title": "Match through shared interests.",
      "subtitle":
          "Discover friends who love what you love—music, travel, books, and more.",
    },
    {
      "image": "assets/images/onboard3.png",
      "title": "Nearby\nConnections",
      "subtitle": "Connect with potential friends in your area online or IRL, Instantly.",
    },
  ];

  Future<void> _nextPage() async {
    if (_currentPage < onboardingData.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.ease,
      );
      imageCarouselController.animateToPage(
        _currentPage + 1,
        duration: const Duration(milliseconds: 300),
        curve: Curves.ease,
      );
      setState(() => _currentPage = _currentPage + 1);
    } else {
      await Hive.box(HiveConstants.hiveBox).put(HiveConstants.onboardingCompleted, true);
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => const LoginPage(),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final isLastPage = _currentPage == onboardingData.length - 1;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            const SizedBox(height: 24),
            CarouselSlider(
              items: onboardingData
                  .map((e) => SizedBox(
                        width: 264,
                        height: 360,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.asset(e["image"]!),
                        ),
                      ))
                  .toList(),
              carouselController: imageCarouselController,
              options: CarouselOptions(
                autoPlay: false,
                viewportFraction: 0.6,
                enlargeFactor: 0.2,
                enlargeCenterPage: true,
                height: 360,
              ),
            ),
            Expanded(
              child: PageView.builder(
                clipBehavior: Clip.none,
                controller: _pageController,
                itemCount: onboardingData.length,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                  imageCarouselController.animateToPage(
                    index,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.ease,
                  );
                },
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 40.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          onboardingData[index]["title"]!,
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          onboardingData[index]["subtitle"]!,
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 16),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                onboardingData.length,
                (index) => AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  width: _currentPage == index ? 10 : 6,
                  height: 6,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: _currentPage == index
                        ? LinearGradient(
                            colors: [
                              AppConstants.primaryColor,
                              AppConstants.primaryLightColor,
                            ],
                          )
                        : LinearGradient(
                            colors: [
                              Colors.grey.shade400,
                              Colors.grey.shade400,
                            ],
                          ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: CustomButton(
                onPressed: _nextPage,
                text: "Next",
              ),
            ),
            const SizedBox(height: 10),
            if (isLastPage)
              SizedBox(
                height: 48,
              )
            else
              TextButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const LoginPage(),
                    ),
                  );
                },
                child: const Text("Skip"),
              ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}

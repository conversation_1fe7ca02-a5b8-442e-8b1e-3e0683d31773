import 'package:flutter/material.dart';

class PremiumChipWidget extends StatelessWidget {
  final List<String> chipList;
  const PremiumChipWidget({super.key, required this.chipList});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Wrap(
          spacing: 8,
          runSpacing: 8,
          alignment: WrapAlignment.start,
          children: List.generate(
          chipList.length,
            (index) => Container(
              padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(50),
                  border: Border.all(
                    color: Colors.white.withOpacity(.5),
                  )),
              child: Text(
                chipList[index],
                style: const TextStyle(
                    color: Colors.white,
                    fontSize: 11,
                    fontWeight: FontWeight.w500),
              ),
            ),
          )),
    );
  }
}

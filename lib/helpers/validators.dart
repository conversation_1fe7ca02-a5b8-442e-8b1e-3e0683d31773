class Validators {
  static String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your name';
    }
    return null;
  }

  static String? validateBio(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your bio';
    } else if (value.length < 10) {
      return 'Please enter greater then  10 words';
    }
    return null;
  }

  static String? validateAddress(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your address';
    }
    return null;
  }

  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your email';
    }
    // Email validation using a regular expression
    // Adapted from https://stackoverflow.com/a/16800540/1934480
    final emailRegex = RegExp(
        r'^[\w-]+(\.[\w-]+)*@[a-zA-Z\d-]+(\.[a-zA-Z\d-]+)*\.[a-zA-Z\d-]{2,}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  static String? validateCoupleName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your couple name';
    }
    return null;
  }

  static String? validateCoupleEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your couple email';
    }
    // Email validation using a regular expression
    // Adapted from https://stackoverflow.com/a/16800540/1934480
    final emailRegex = RegExp(
        r'^[\w-]+(\.[\w-]+)*@[a-zA-Z\d-]+(\.[a-zA-Z\d-]+)*\.[a-zA-Z\d-]{2,}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  static String? validatePassword(String? value) {
    bool isValidPassword = RegExp(
            r"""^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~]).{8,}$""")
        .hasMatch(value ?? "");

    if (value == null || value.isEmpty) {
      return 'Please enter a password';
    } else if (!isValidPassword) {
      return 'Password must have 8 characters including at least 1 digit, 1 special character and 1 uppercase letter.';
      //  return 'Password must be  Minimum 8 digits long, One numeric digit, One special character,  One Uppercase letter.';
      // return 'Password must be  \nMinimum 8 digits long \nOne numeric digit \nOne special character  \nOne Uppercase letter.';
    }
    // You can add more specific password validation if needed
    return null;
  }

  static String? validateConfirmPassword(String? value, String? password) {
    if (value != password) {
      return 'Passwords do not match';
    }
    return null;
  }
}

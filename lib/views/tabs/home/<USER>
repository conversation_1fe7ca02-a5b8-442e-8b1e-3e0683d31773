import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../helpers/constants.dart';
import '../../../models/irl_activity_model.dart';
import '../../../providers/other_users_provider.dart';
import 'home_page.dart';

class IrlActivityScreen extends ConsumerWidget {
  final IrlActivityModel activity;
  final VoidCallback onExitGroup;

  const IrlActivityScreen({
    super.key,
    required this.activity,
    required this.onExitGroup,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          activity.name,
          style: GoogleFonts.manrope(
            fontSize: 18,
            fontWeight: FontWeight.w500,
            color: AppConstants.primaryTextColor,
          ),
        ),
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        automaticallyImplyLeading: false,
        elevation: 2,
        shadowColor: Colors.white.withOpacity(0.6),
        leading: Padding(
          padding: const EdgeInsets.only(left: 16.0),
          child: IconButton(
            icon: SvgPicture.asset(
              AppConstants.backIcon,
              color: Colors.black,
              height: 24,
              width: 24,
            ),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 28),
          Expanded(
            child: Consumer(
              builder: (context, ref, child) {
                final filteredUsers = ref.watch(filteredOtherUsersProvider);
            
                return filteredUsers.when(
                  data: (data) {
                    return Column(
                      children: [
                        Expanded(
                          child: data.isEmpty
                              ? const HomePageNoUsersFoundWidget()
                              : FilterInteraction(
                                  isPremiumUser: true,
                                  isPremiumPlusUser: true,
                                  users: data,
                                  // filterData: filter
                                  isRefresh: true,
                                  isGridView: false,
                                ),
                        ),
                        Center(
                          child: TextButton(
                            onPressed: () => onExitGroup.call(),
                            child: Text(
                              "Exit group",
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: AppConstants.primaryTextColor,
                                decoration: TextDecoration.underline,
                                decorationColor: AppConstants.primaryTextColor,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 20),
                      ],
                    );
                  },
                  error: (_, __) => const Center(
                    child: Text("Something Went Wrong!"),
                  ),
                  loading: () => const Center(
                    child: CircularProgressIndicator.adaptive(),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

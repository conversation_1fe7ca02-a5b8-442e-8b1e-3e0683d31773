<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_393_3553)">
<path d="M3.41217 9.46384C3.41217 5.1574 6.90323 1.66634 11.2097 1.66634V1.66634C13.9522 1.66634 15.3238 1.66634 16.2763 2.33134C16.5464 2.51999 16.7886 2.74583 16.9955 3.00218C17.708 3.89051 17.708 5.16884 17.708 7.72718L17.708 9.84801C17.708 12.3172 17.708 13.5522 17.3138 14.538C16.6797 16.1238 15.3388 17.3738 13.6388 17.9655C12.583 18.333 11.258 18.333 8.61051 18.333C7.09717 18.333 6.34134 18.333 5.73717 18.123C4.76551 17.7847 3.99967 17.0705 3.63717 16.1647C3.41217 15.6013 3.41217 14.8955 3.41217 13.4847L3.41217 9.46384Z" stroke="#717171" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M17.708 9.99968C17.708 11.533 16.453 12.7772 14.9047 12.7772C14.3447 12.7772 13.6847 12.6805 13.1405 12.8247C12.9033 12.8866 12.6867 13.0101 12.5126 13.1826C12.3384 13.3551 12.213 13.5706 12.1488 13.8072C12.0038 14.3463 12.1013 15.0005 12.1013 15.5555C12.1013 17.0888 10.8463 18.333 9.29967 18.333" stroke="#717171" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_393_3553">
<rect width="20" height="20" fill="white" transform="translate(20 20) rotate(-180)"/>
</clipPath>
</defs>
</svg>

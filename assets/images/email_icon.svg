<svg width="56" height="46" viewBox="0 0 56 46" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1085_7117)">
<path d="M46.666 0.333496H9.33268C6.76602 0.333496 4.68935 2.4335 4.68935 5.00016L4.66602 33.0002C4.66602 35.5668 6.76602 37.6668 9.33268 37.6668H46.666C49.2327 37.6668 51.3327 35.5668 51.3327 33.0002V5.00016C51.3327 2.4335 49.2327 0.333496 46.666 0.333496ZM46.666 9.66683L27.9994 21.3335L9.33268 9.66683V5.00016L27.9994 16.6668L46.666 5.00016V9.66683Z" fill="url(#paint0_linear_1085_7117)"/>
</g>
<defs>
<filter id="filter0_d_1085_7117" x="0.666016" y="0.333496" width="54.666" height="45.3335" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1085_7117"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1085_7117" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1085_7117" x1="-0.996208" y1="-5.44373" x2="6.97009" y2="46.7102" gradientUnits="userSpaceOnUse">
<stop stop-color="#C70973"/>
<stop offset="1" stop-color="#46239F"/>
</linearGradient>
</defs>
</svg>

import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart' show FirebaseAuth;
import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/main.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';

import '../custom/custom_app_loader.dart';
import '../../providers/auth_providers.dart' as authProvider;

class EmailVerificationPage extends ConsumerStatefulWidget {
  const EmailVerificationPage({super.key});

  @override
  ConsumerState<EmailVerificationPage> createState() =>
      _EmailVerificationPageState();
}

class _EmailVerificationPageState extends ConsumerState<EmailVerificationPage> {
  bool isEmailVerified = false;
  Timer? timer;
  @override
  void initState() {
    if (FirebaseAuth.instance.currentUser != null) {
      isEmailVerified = FirebaseAuth.instance.currentUser!.emailVerified;
    }

    if (!isEmailVerified) {
      sendEmailVerification();

      Timer.periodic(Duration(seconds: 3), (timer) {
        if (FirebaseAuth.instance.currentUser != null) {
          checlEmailVerification();
        }
      });
    }

    // TODO: implement initState
    super.initState();
  }

  Future checlEmailVerification() async {
    await FirebaseAuth.instance.currentUser!.reload();

    setState(() {
      isEmailVerified = FirebaseAuth.instance.currentUser!.emailVerified;
    });

    if (isEmailVerified) timer?.cancel();
  }

  Future sendEmailVerification() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        CustomAppLoader.showCustomLoader("Sending verification email...");
        await authProvider.AuthProvider.sendEmailVerification(user)
            .then((value) {
          if (value) {
            EasyLoading.showSuccess("Verification email sent!");
          }
        });
      }
    } catch (e) {
      print("Spmething went wrong");
    }
  }

  @override
  Widget build(BuildContext context) {
    return isEmailVerified
        ? LandingWidget()
        : Scaffold(
          backgroundColor: Colors.white,
          appBar: AppBar(
            title: Text(
              'Verify Email',
              style: GoogleFonts.manrope(
                color: AppConstants.primaryTextColor,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            automaticallyImplyLeading: false,
            elevation: 2,
            shadowColor: Colors.white.withOpacity(0.6),
              leading: Padding(
                padding: const EdgeInsets.only(left: 16.0),
                child: IconButton(
                  icon: SvgPicture.asset(
                    AppConstants.backIcon,
                    color: Colors.black,
                    height: 24,
                    width: 24,
                  ),
                  onPressed: () {
                    Navigator.pop(context);
                  },
                ),
              ),
            ),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                            color: AppConstants.primaryLightColor
                                .withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8)),
                        padding: const EdgeInsets.fromLTRB(20, 20, 20, 20),
                        child: SvgPicture.asset(
                          AppConstants.emailIcon,
                          // height: 15,
                        ),
                      ),
                      const SizedBox(
                        height: 24,
                      ),
                      Text(
                        "Success!!!",
                        style: TextStyle(
                            fontFamily: AppConstants.fontStyleName,
                            fontSize: 24,
                            fontWeight: FontWeight.w600,
                            color: AppConstants.primaryTextColor),
                      ),
                      const SizedBox(
                        height: 9,
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 60),
                        child: Text(
                          "Please check your email for a link to verify your email",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              fontFamily: AppConstants.fontStyleName,
                              color: AppConstants.primaryTextColor,
                              fontSize: 14,
                              fontWeight: FontWeight.w400),
                        ),
                      )
                    ],
                  ),
                ),
                // Padding(
                //   padding: const EdgeInsets.symmetric(
                //       horizontal: 29, vertical: 24),
                //   child: CustomButton(
                //       onTap: () {
                //         Navigator.of(context).pop();
                //       },
                //       buttonTxt: "Back to login screen"),
                // )
              ],
            ),
          ),
        );
  }
}

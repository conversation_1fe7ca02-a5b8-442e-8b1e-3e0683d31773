import 'package:flutter/material.dart';

class CustomCheckBoxDropdown extends StatefulWidget {
  final List<String> items;
  final List<String> selectedValues;
  final ValueChanged<List<String>> onChanged;
  final String labelText;
  final String hintText;
  final FormFieldValidator<List<String>>? validator;

  const CustomCheckBoxDropdown({
    required this.items,
    required this.selectedValues,
    required this.onChanged,
    required this.labelText,
    required this.hintText,
    this.validator,
  });

  @override
  _CustomCheckBoxDropdownState createState() => _CustomCheckBoxDropdownState();
}

class _CustomCheckBoxDropdownState extends State<CustomCheckBoxDropdown> {
  late List<bool> _selectedState;

  @override
  void initState() {
    super.initState();
    _selectedState = List<bool>.filled(widget.items.length, false);
    for (final value in widget.selectedValues) {
      final index = widget.items.indexOf(value);
      if (index != -1) {
        _selectedState[index] = true;
      }
    }
  }

  String getSelectedValuesString() {
    return widget.selectedValues.join(", ");
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Text(
            widget.labelText,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                getSelectedValuesString(),
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                ),
              ),
              DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: null,
                  onChanged: (value) {},
                  items: widget.items.map((String item) {
                    final index = widget.items.indexOf(item);
                    return DropdownMenuItem<String>(
                      value: item,
                      child: Row(
                        children: [
                          Checkbox(
                            value: _selectedState[index],
                            onChanged: (checked) {
                              setState(() {
                                _selectedState[index] = checked ?? false;
                                final selectedValues = <String>[];
                                for (var i = 0;
                                    i < _selectedState.length;
                                    i++) {
                                  if (_selectedState[i]) {
                                    selectedValues.add(widget.items[i]);
                                  }
                                }
                                widget.onChanged(selectedValues);
                              });
                            },
                          ),
                          Text(item),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),
        if (widget.validator != null)
          Text(
            widget.validator!(widget.selectedValues) ?? '',
            style: const TextStyle(
              color: Colors.red,
              fontSize: 12,
            ),
          ),
      ],
    );
  }
}

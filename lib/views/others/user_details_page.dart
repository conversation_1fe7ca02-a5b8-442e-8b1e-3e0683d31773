// ignore_for_file: use_build_context_synchronously
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

import '../../helpers/constants.dart';
import '../../helpers/custom_cache_image.dart';
import '../../helpers/media_picker_helper.dart';
import '../../helpers/string_extensions.dart';
import '../../models/custom_check_model.dart';
import '../../models/match_model.dart';
import '../../models/notification_model.dart';
import '../../models/user_interaction_model.dart';
import '../../models/user_profile_model.dart';
import '../../providers/auth_providers.dart';
import '../../providers/block_user_provider.dart';
import '../../providers/interaction_provider.dart';
import '../../providers/match_provider.dart';
import '../../providers/notifiaction_provider.dart';
import '../../providers/other_users_provider.dart';
import '../../providers/swipe_count_provider.dart';
import '../../providers/user_profile_provider.dart';
import '../custom/block_user_dialog.dart';
import '../custom/custom_confirmation_dialog.dart';
import '../tabs/home/<USER>';
import '../tabs/messages/components/chat_page.dart';
import 'report_page.dart';

enum UserOptions {
  report,
  block,
}

class UserDetailsPage extends ConsumerStatefulWidget {
  final UserProfileModel user;
  final PageController? pageController;
  final String? matchId;
  final int? totalInteraction;
  //final MatchEngine? matchEngine;
  final bool? isPrimeUser;
  final bool? isPrimePlusUser;
  final bool? isInterected;
  final String? likeDislike;

  const UserDetailsPage(
      {Key? key,
      required this.user,
      this.matchId,
      this.pageController,
      this.totalInteraction,
      //this.matchEngine,
      this.isPrimeUser,
      this.isPrimePlusUser,
      this.isInterected,
      this.likeDislike = ""})
      : super(key: key);

  @override
  ConsumerState<UserDetailsPage> createState() => _UserDetailsPageState();
}

class _UserDetailsPageState extends ConsumerState<UserDetailsPage> {
  bool isPrimeUser = false;
  bool isPrimePlusUser = false;
  bool isInterected = false;
  String likeDislike = "";
  int swipeCount = 0;
  UserProfileModel? myData;
  List<CustomCipsModel> pronounsList = [];
  int _currentIndex = 0;
  bool isBottomShow = false;

  @override
  void initState() {
    super.initState();
    isPrimeUser = widget.isPrimeUser ?? false;
    isPrimePlusUser = widget.isPrimePlusUser ?? false;
    isInterected = widget.isInterected ?? false;
    likeDislike = widget.likeDislike ?? "";
    final user = ref.read(userProfileFutureProvider);

    user.when(
      data: (data) {
        if (data != null) {
          setState(() {
            isPrimeUser = data.isPremiumUser ?? false;
            isPrimePlusUser = data.isPremiumPlusUser ?? false;
          });
        }
      },
      error: (_, __) => const SizedBox(),
      loading: () => const SizedBox(),
    );
    for (int i = 0; i < (widget.user.pronounceList?.length ?? 0); i++) {
      pronounsList.add(
        CustomCipsModel(
          id: i + 1,
          name: widget.user.pronounceList![i],
          isSelected: true,
        ),
      );
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget _buildDots() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List<Widget>.generate(
        widget.user.imageList?.length ?? 0, // Replace with the actual number of pages
        (int index) {
          return Container(
            width: 48,
            height: 3,
            margin: const EdgeInsets.symmetric(horizontal: 3),
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(10)),
              shape: BoxShape.rectangle,
              color: _currentIndex == index
                  ? Colors.white
                  : const Color.fromARGB(91, 0, 0, 0),
            ),
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentUserProfile = ref.watch(userProfileFutureProvider);
    swipeCount = ref.watch(aNumber);
    final String myUserId = ref.watch(currentUserStateProvider)!.uid;
    final String id = myUserId + widget.user.id;

    currentUserProfile.when(
      data: (data) {
        if (data != null) {
          myData = data;
          setState(() {});
        }
      },
      error: (_, __) => const SizedBox(),
      loading: () => const SizedBox(),
    );
    getExistingInteraction(myUserId, widget.user.id).then((otherUserInteraction) {
      if (otherUserInteraction != null) {
        if (otherUserInteraction.isLike || otherUserInteraction.isMatch) {
          setState(() {
            isBottomShow = true;
          });
        }
      }
    });
    final UserInteractionModel interaction = UserInteractionModel(
      id: id,
      userId: myUserId,
      intractToUserId: widget.user.id,
      isSuperLike: false,
      isLike: false,
      isDislike: false,
      createdAt: DateTime.now(),
    );

    // UserProfileModel? currentUserProfileModel;
    // currentUserProfile.whenData((userProfile) {
    //   currentUserProfileModel = userProfile;
    // });

    final String genderIcon = switch (widget.user.gender.toLowerCase()) {
      'male' => AppConstants.maleSharpIcon,
      'female' => AppConstants.femaleIcon,
      'other' => AppConstants.transgenderIcon,
      'non-binary' => AppConstants.nonbinaryIcon,
      _ => AppConstants.maleIcon,
    };

    return Scaffold(
      backgroundColor: Colors.white,
      extendBody: true,
      body: SingleChildScrollView(
        // padding: const EdgeInsets.only(top: 38),
        child: Column(
          children: [
            Stack(
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(14),
                    topRight: Radius.circular(14),
                  ),
                  child: SizedBox(
                    height: MediaQuery.of(context).size.height / 1.5,
                    width: MediaQuery.of(context).size.width,
                    child: Stack(
                      children: [
                        CustomCacheImage(
                          key: Key(widget.user.imageList?[_currentIndex] ?? ""),
                          imageUrl: widget.user.imageList?[_currentIndex] ?? "",
                          height: MediaQuery.of(context).size.height / 1.5,
                          width: MediaQuery.of(context).size.width,
                          fit: BoxFit.cover,
                          // loadingBuilder: (context, child, loadingProgress) {
                          //   if (loadingProgress == null) return child;
                          //   return const Center(
                          //       child: CircularProgressIndicator.adaptive());
                          // },
                          errorWidget: const Center(child: Icon(CupertinoIcons.photo)),
                          // fadeInDuration: const Duration(milliseconds: 200),
                        ),
                        SizedBox(
                          height: MediaQuery.of(context).size.height / 2,
                          width: MediaQuery.of(context).size.width,
                          child: Stack(
                            children: [
                              Positioned(
                                bottom: 16,
                                left: 0,
                                right: 0,
                                child: _buildDots(),
                              )
                            ],
                          ),
                        ),
                        Positioned.fill(
                          child: Row(
                            children: [
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    if (_currentIndex > 0) {
                                      setState(() {
                                        _currentIndex--;
                                      });
                                    }
                                  },
                                  child: Container(
                                    color: Colors.transparent,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    if (_currentIndex < (widget.user.imageList?.length ?? 0) - 1) {
                                      setState(() {
                                        _currentIndex++;
                                      });
                                    }
                                  },
                                  child: Container(
                                    color: Colors.transparent,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                Positioned(
                  left: AppConstants.defaultNumericValue,
                  top: MediaQuery.of(context).padding.top + 10,
                  right: AppConstants.defaultNumericValue,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                        iconSize: 24,
                        icon: Icon(
                          Icons.arrow_back_ios_new_rounded,
                          color: Colors.white,
                        ),
                        onPressed: () {
                          Navigator.pop(context);
                        },
                      ),
                      PopupMenuButton<UserOptions>(
                        icon: Icon(
                          Icons.more_vert_rounded,
                          color: Colors.white,
                        ),
                        color: Colors.white,
                        position: PopupMenuPosition.under,
                        padding: EdgeInsets.zero,
                        menuPadding: EdgeInsets.zero,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        onSelected: (value) {
                          if (value == UserOptions.report) {
                            Navigator.push(
                              context,
                              CupertinoPageRoute(
                                builder: (context) => ReportPage(userProfileModel: widget.user),
                              ),
                            );
                          } else if (value == UserOptions.block) {
                            showDialog(
                              context: context,
                              builder: (context) => BlockUserDialog(
                                onBlock: () async {
                                  await blockUser(widget.user.id, myUserId);
                                  Navigator.pop(context);
                                },
                              ),
                            );
                          }
                        },
                        itemBuilder: (context) => [
                          PopupMenuItem(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            value: UserOptions.report,
                            child: _iconTile(
                              text: "Report User",
                              iconPath: AppConstants.reportIcon,
                              gap: 10,
                            ),
                          ),
                          PopupMenuItem(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            value: UserOptions.block,
                            child: _iconTile(
                              text: "Block User",
                              iconPath: AppConstants.blockIcon,
                              gap: 10,
                              textColor: Color(0xFFDD0000),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(
                    top: MediaQuery.of(context).size.height / 2,
                  ),
                  width: MediaQuery.of(context).size.width,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    // color: Color(0xFFFFFCF5),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultNumericValue),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 20),
                      Text(
                        "${widget.user.firstName?.toUpperCase()}, ${DateTime.now().difference(widget.user.birthDay!).inDays ~/ 365}",
                        style: GoogleFonts.manrope(
                          color: AppConstants.primaryTextColor,
                          fontWeight: FontWeight.w800,
                          fontSize: 24,
                        ),
                      ),
                      SizedBox(height: 10),
                      _iconTile(
                        iconPath: AppConstants.occupationIcon,
                        text: widget.user.work ?? "Not Set!",
                        gap: 10,
                      ),
                      SizedBox(height: 10),
                      _iconTile(
                        iconPath: AppConstants.locationOutline,
                        text: widget.user.address ?? "Not Set!",
                        gap: 10,
                      ),
                      SizedBox(height: 20),
                      Divider(
                        color: Color(0xB0D9D9D9),
                        thickness: 1,
                      ),
                      SizedBox(height: 20),
                      Row(
                        children: [
                          _iconTile(
                            iconPath: AppConstants.pronounIcon,
                            text: widget.user.pronounceList != null && widget.user.pronounceList!.isNotEmpty
                                ? widget.user.pronounceList!.first
                                : "Not Set!",
                            gap: 10,
                          ),
                          SizedBox(
                            height: 20,
                            width: 40,
                            child: VerticalDivider(
                              color: Color(0xFFC7C7C7),
                              thickness: 1,
                            ),
                          ),
                          _iconTile(
                            iconPath: genderIcon,
                            text: widget.user.gender.isEmpty
                                ? "Not Set!"
                                : widget.user.gender.capitalizeFirst(),
                            gap: 10,
                            iconSize: 24,
                            iconColor: AppConstants.primaryTextColor,
                          ),
                          SizedBox(
                            height: 20,
                            width: 40,
                            child: VerticalDivider(
                              color: Color(0xFFC7C7C7),
                              thickness: 1,
                            ),
                          ),
                          _iconTile(
                            iconPath: AppConstants.drinksIcon,
                            text: widget.user.drinking ?? "Not Set!",
                            gap: 10,
                          ),
                        ],
                      ),
                      SizedBox(height: 20),
                      Row(
                        children: [
                          _iconTile(
                            // iconPath: AppConstants.drinksIcon,
                            text: widget.user.cannabis ?? "Not Set!",
                            iconWidget: Image.asset(
                              AppConstants.cannabisIcon,
                              height: 20,
                              width: 20,
                            ),
                            gap: 10,
                          ),
                          SizedBox(
                            height: 20,
                            width: 40,
                            child: VerticalDivider(
                              color: Color(0xFFC7C7C7),
                              thickness: 1,
                            ),
                          ),
                          _iconTile(
                            iconPath: AppConstants.religionIcon,
                            text: widget.user.religion ?? "Not Set!",
                            gap: 10,
                          ),
                        ],
                      ),
                      SizedBox(height: 20),
                      Divider(
                        color: Color(0xB0D9D9D9),
                        thickness: 1,
                      ),
                      SizedBox(height: 16),
                      Text(
                        "About",
                        style: GoogleFonts.manrope(
                          color: Color(0xFF717171),
                          fontSize: 12,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        widget.user.bio == null ? "Not Set!" : widget.user.bio!,
                        style: GoogleFonts.manrope(
                          color: AppConstants.primaryTextColor,
                          fontSize: 14,
                        ),
                      ),
                      SizedBox(height: 16),
                      Divider(
                        color: Color(0xB0D9D9D9),
                        thickness: 1,
                      ),
                      SizedBox(height: 16),
                      Text(
                        "Interests",
                        style: GoogleFonts.manrope(
                          color: Color(0xFF717171),
                          fontSize: 12,
                        ),
                      ),
                      SizedBox(height: 8),
                      Wrap(
                        spacing: 16,
                        runSpacing: 16,
                        crossAxisAlignment: WrapCrossAlignment.center,
                        children: [
                          if (widget.user.interests.isNotEmpty) ...[
                            ...List.generate(
                                widget.user.interests.length * 2 - 1,
                                (index) {
                              if (index.isEven) {
                                // Interest text
                                final interest = widget.user.interests[index ~/ 2];
                                return Text(
                                  interest,
                                  style: GoogleFonts.manrope(
                                    color: AppConstants.primaryTextColor,
                                    fontSize: 14,
                                  ),
                                );
                              } else {
                                // Dot separator
                                return Container(
                                  height: 10,
                                  width: 10,
                                  decoration: BoxDecoration(
                                    color: AppConstants.primaryTextColor,
                                    shape: BoxShape.circle,
                                  ),
                                );
                              }
                            })
                          ] else ...[
                            Text(
                              "Not Set!",
                              style: GoogleFonts.manrope(
                                color: AppConstants.primaryTextColor,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ],
                      )
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 100)
          ],
        ),
      ),
      bottomNavigationBar: isBottomShow
          ? null
          : SafeArea(
              child: Container(
                color: Colors.transparent,
                padding: const EdgeInsets.symmetric(horizontal: 40),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    _buildIconButton(
                      iconPath: AppConstants.dislike,
                      iconSize: 26,
                      ringColor: Color(0xFFFF4C61),
                      onTap: () {
                        if (!isInterected) {
                          if (!(isPrimeUser || isPrimePlusUser)) {
                            if (widget.totalInteraction! < swipeCount) {
                              onTapCross(interaction);
                            } else {
                              EasyLoading.showToast("You have used up your 50 swipes for today, you need to wait until tomorrow.");
                            }
                          } else {
                            onTapCross(interaction);
                          }
                        } else {
                          EasyLoading.showToast("you already $likeDislike this profile.");
                        }
                      },
                    ),
                    _buildIconButton(
                      iconPath: AppConstants.favoriteBlue,
                      iconSize: 26,
                      ringColor: Color(0xFF1990D7),
                      onTap: () {
                        if (!isInterected) {
                          if (!(isPrimeUser || isPrimePlusUser)) {
                            if (widget.totalInteraction! < swipeCount) {
                              if (likeDislike != "favourited") {
                                onTapBolt(widget.user, myData!, interaction);
                              } else {
                                EasyLoading.showToast("you already $likeDislike this profile.");
                              }
                            } else {
                              EasyLoading.showToast("You have used up your 50 swipes for today, you need to wait until tomorrow.");
                            }
                          } else {
                            if (likeDislike != "favourited") {
                              onTapBolt(widget.user, myData!, interaction);
                            } else {
                              EasyLoading.showToast("you already $likeDislike this profile.");
                            }
                          }
                        }
                      },
                    ),
                    _buildIconButton(
                      iconPath: AppConstants.likeGreen,
                      iconSize: 26,
                      ringColor: Color(0xFF00C48C),
                      onTap: () {
                        if (!isInterected) {
                          if (!(isPrimeUser || isPrimePlusUser)) {
                            if (widget.totalInteraction! < swipeCount) {
                              if (likeDislike != "liked" ||
                                  likeDislike != "favourited") {
                                onTapHeart(interaction, widget.user);
                              } else {
                                EasyLoading.showToast("you already $likeDislike this profile.");
                              }
                            } else {
                              EasyLoading.showToast("You have used up your 50 swipes for today, you need to wait until tomorrow.");
                            }
                          } else {
                            if (likeDislike != "liked" ||
                                likeDislike != "favourited") {
                              onTapHeart(interaction, widget.user);
                            } else {
                              EasyLoading.showToast("you already $likeDislike this profile.");
                            }
                          }
                        } else {
                          EasyLoading.showToast("you already $likeDislike this profile.");
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildIconButton({
    required String iconPath,
    required double iconSize,
    required Color ringColor,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.25),
              blurRadius: 10,
              offset: Offset(0, 6), // slight lift from bottom
            ),
          ],
        ),
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: ringColor),
          ),
          child: SvgPicture.asset(
            iconPath,
            height: iconSize,
            width: iconSize,
          ),
        ),
      ),
    );
  }

  Widget _iconTile({
    required String text,
    String? iconPath,
    double iconSize = 20,
    Widget? iconWidget,
    double? gap,
    Color? iconColor,
    Color? textColor,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (iconWidget != null) ...[
          iconWidget,
        ] else if (iconPath != null) ...[
          SvgPicture.asset(
            iconPath,
            height: iconSize,
            width: iconSize,
            color: iconColor,
          ),
        ],
        if (gap != null && (iconWidget != null || iconPath != null)) ...[
          SizedBox(width: gap),
        ],
        Text(
          text,
          style: GoogleFonts.manrope(
            color: textColor ?? AppConstants.primaryTextColor,
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  onTapCross(UserInteractionModel interaction) {
    final newInteraction =
        interaction.copyWith(isDislike: true, createdAt: DateTime.now());
    createInteraction(newInteraction);
    setState(() {
      isInterected = true;
      likeDislike = "disliked";
      //widget.matchEngine!.currentItem!.nope();
    });
    ref.invalidate(userProfileFutureProvider);
    ref.invalidate(interactionFutureProvider);
    ref.invalidate(otherUsersProvider);
    Navigator.pop(context);

    EasyLoading.showToast("Dislike Successfully");
  }

  onTapBolt(UserProfileModel otherUser, UserProfileModel data,
      UserInteractionModel interaction) async {
    if (data.favouriteIdList != null) {
      if (data.favouriteIdList!.contains(otherUser.userId)) {
        EasyLoading.showToast(
            "${otherUser.firstName} ${otherUser.lastName} is already added in your Favorites");
        return;
      } else {
        data.favouriteIdList?.add(otherUser.userId);
        ref.read(userProfileNotifier).updateUserProfile(
            data.copyWith(favouriteIdList: data.favouriteIdList));

        final newInteraction =
            interaction.copyWith(isLike: true, createdAt: DateTime.now());
        createInteraction(newInteraction);
        //widget.matchEngine!.currentItem!.like();

        setState(() {
          isInterected = true;
          likeDislike = "favourited";
        });
        await createInteraction(newInteraction).then((result) async {
          if (result) {
            await getExistingInteraction(otherUser.id, data.userId)
                .then((otherUserInteraction) {
              if (otherUserInteraction != null) {
                createInteraction(newInteraction.copyWith(isMatch: true));
                createInteraction(otherUserInteraction.copyWith(isMatch: true));
                var otherUserFavList = otherUser.favouriteIdList;
                otherUserFavList?.remove(data.userId);
                ref.read(userProfileNotifier).updateUserProfile(
                    otherUser.copyWith(favouriteIdList: otherUserFavList));

                var currentUserFavList = data.favouriteIdList;
                currentUserFavList?.remove(otherUser.userId);
                ref.read(userProfileNotifier).updateUserProfile(
                    data.copyWith(favouriteIdList: currentUserFavList));
                showMatchingDialog(
                    context: context, currentUser: data, otherUser: otherUser);
              } else {
                createInteractionNotification(
                    title: "You have a new Interaction!",
                    body:
                        "${data.firstName} ${data.lastName} has added you to their favorite list",
                    userId: otherUser.id,
                    notificationType: AppConstants.favoritedNotifacation,
                    currentUser: data);
                Navigator.pop(context);
              }
            });
          }
        });
        EasyLoading.showToast(
            "${otherUser.firstName} ${otherUser.lastName} is added in your Favorites");
      }
    } else {
      List<String> favList = [];
      favList.add(otherUser.userId);
      data.favouriteIdList = favList;
      ref.read(userProfileNotifier).updateUserProfile(
          data.copyWith(favouriteIdList: data.favouriteIdList));

      final newInteraction =
          interaction.copyWith(isLike: true, createdAt: DateTime.now());
      await createInteraction(newInteraction).then((result) async {
        if (result) {
          await getExistingInteraction(otherUser.id, data.userId)
              .then((otherUserInteraction) {
            if (otherUserInteraction != null) {
              createInteraction(newInteraction.copyWith(isMatch: true));
              createInteraction(otherUserInteraction.copyWith(isMatch: true));
              var otherUserFavList = otherUser.favouriteIdList;
              otherUserFavList?.remove(data.userId);
              ref.read(userProfileNotifier).updateUserProfile(
                  otherUser.copyWith(favouriteIdList: otherUserFavList));

              var currentUserFavList = data.favouriteIdList;
              currentUserFavList?.remove(otherUser.userId);
              ref.read(userProfileNotifier).updateUserProfile(
                  data.copyWith(favouriteIdList: currentUserFavList));
              showMatchingDialog(
                  context: context, currentUser: data, otherUser: otherUser);
            } else {
              createInteractionNotification(
                  title: "You have a new Interaction!",
                  body: "${data.firstName} ${data.lastName} has added you to their favorite list",
                  userId: otherUser.id,
                  notificationType: AppConstants.favoritedNotifacation,
                  currentUser: data);
              Navigator.pop(context);
            }
          });
        }
      });
      //widget.matchEngine!.currentItem!.like();
    }
    setState(() {
      isInterected = true;
      likeDislike = "favourited";
    });
    ref.invalidate(userProfileFutureProvider);
    ref.invalidate(otherUsersProvider);
    ref.invalidate(currentUserInteractionProvider);
    ref.invalidate(interactionFutureProvider);
    EasyLoading.showToast("${otherUser.firstName} ${otherUser.lastName} is added in your Favorites");
  }

  onTapHeart(UserInteractionModel interaction, UserProfileModel otherUser) {
    final newInteraction =
        interaction.copyWith(isLike: true, createdAt: DateTime.now());
    final user = ref.read(userProfileFutureProvider);
    user.when(
      data: (data) async {
        if (data != null) {
          await createInteraction(newInteraction).then((result) async {
            if (result) {
              await getExistingInteraction(otherUser.id, data.userId)
                  .then((otherUserInteraction) {
                if (otherUserInteraction != null) {
                  createInteraction(newInteraction.copyWith(isMatch: true));
                  createInteraction(
                      otherUserInteraction.copyWith(isMatch: true));
                  var otherUserFavList = otherUser.favouriteIdList;
                  otherUserFavList?.remove(data.userId);
                  ref.read(userProfileNotifier).updateUserProfile(
                      otherUser.copyWith(favouriteIdList: otherUserFavList));

                  var currentUserFavList = data.favouriteIdList;
                  currentUserFavList?.remove(otherUser.userId);
                  ref.read(userProfileNotifier).updateUserProfile(
                      data.copyWith(favouriteIdList: currentUserFavList));
                  showMatchingDialog(
                      context: context,
                      currentUser: data,
                      otherUser: otherUser);
                } else {
                  createInteractionNotification(
                      title: "You have a new Interaction!",
                      body: "${data.firstName} ${data.lastName} has liked you!",
                      userId: otherUser.id,
                      notificationType: AppConstants.likeNotifacation,
                      currentUser: data);
                  Navigator.pop(context);
                }
              });
            }
          });
        }
        setState(() {
          isInterected = true;
          likeDislike = "liked";
          //widget.matchEngine!.currentItem!.like();
        });
        ref.invalidate(userProfileFutureProvider);
        ref.invalidate(otherUsersProvider);
        ref.invalidate(currentUserInteractionProvider);
        ref.invalidate(interactionFutureProvider);

        EasyLoading.showToast("Like Successfully");
      },
      error: (error, stackTrace) {},
      loading: () {},
    );
  }

  void showMatchingDialog({
    required BuildContext context,
    required UserProfileModel currentUser,
    required UserProfileModel otherUser,
  }) async {
    final MatchModel matchModel = MatchModel(
      id: currentUser.userId + otherUser.userId,
      userIds: [currentUser.userId, otherUser.userId],
      isMatched: true,
    );

    await createConversation(matchModel).then((matchResult) async {
      if (matchResult) {
        final currentTime = DateTime.now();
        final id =
            matchModel.id + currentTime.millisecondsSinceEpoch.toString();
        final NotificationModel notificationModel = NotificationModel(
          id: currentTime.millisecondsSinceEpoch.toString(),
          userId: currentUser.userId,
          receiverId: otherUser.userId,
          matchId: matchModel.id,
          title: "New Notification",
          body: "You have a new match",
          userName: "${currentUser.firstName} ${currentUser.lastName}",
          notificationType: AppConstants.matchNotifacation,
          image: currentUser.profilePicture,
          createdAt: currentTime,
          isRead: false,
          isMatchingNotification: true,
          isInteractionNotification: false,
        );

        await addNotification(notificationModel);

        // ignore: use_build_context_synchronously
        showDialog(
          context: context,
          builder: (context) {
            return SimpleDialog(
              shape: RoundedRectangleBorder(
                borderRadius:
                    BorderRadius.circular(AppConstants.defaultNumericValue),
              ),
              insetPadding:
                  const EdgeInsets.all(AppConstants.defaultNumericValue),
              contentPadding:
                  const EdgeInsets.all(AppConstants.defaultNumericValue),
              title: Stack(
                children: [
                  const Center(
                      child: Text(
                    "Matched",
                    textAlign: TextAlign.center,
                  )),
                  Positioned(
                    right: 5,
                    child: InkWell(
                      child: const Icon(Icons.close_rounded),
                      onTap: () {
                        Navigator.of(context).pop();
                        Navigator.of(context).pop();
                      },
                    ),
                  )
                ],
              ),
              children: [
                const SizedBox(height: AppConstants.defaultNumericValue),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    UserCirlePicture(
                        imageUrl: otherUser.profilePicture, size: 40),
                    const SizedBox(width: AppConstants.defaultNumericValue / 4),
                    UserCirlePicture(
                        imageUrl: currentUser.profilePicture, size: 40),
                  ],
                ),
                const SizedBox(height: AppConstants.defaultNumericValue),
                Center(
                  child: Text("You are now matched with ${otherUser.firstName} ${otherUser.lastName}"),
                ),
                const SizedBox(height: AppConstants.defaultNumericValue),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                        child: OutlinedButton(
                            onPressed: () {
                              var today = DateFormat("MM/dd/yyyy")
                                  .format(DateTime.now());
                              var introDate = "";
                              var introCount = 0;
                              final userRef =
                                  ref.read(userProfileFutureProvider);
                              userRef.when(
                                  data: (value) => {
                                        if (value != null)
                                          {
                                            introDate =
                                                value.introVideoDate ?? "",
                                            introCount =
                                                value.introVideoCount ?? 0
                                          }
                                      },
                                  error: (_, __) => const SizedBox(),
                                  loading: () => const SizedBox());
                              introCount++;
                              if ((otherUser.isPremiumUser ?? false) == false) {
                                showDialog(
                                    context: context,
                                    builder: (BuildContext mContext) {
                                      return CustomTwoButtonDialog(
                                        title: "Premium User Alert",
                                        desciption:
                                            "Non- premium recipients cannot play intro videos",
                                        buttonText: "Send it anyway",
                                        cancelButtonText: "Ok",
                                        onButtonClick: () {
                                          if (introDate == today &&
                                              introCount > 10) {
                                            EasyLoading.showError(
                                                "Sorry, you've exceeded the daily limit of 10 introductory videos. Please try again tomorrow.");
                                          } else {
                                            pickMedia(
                                                    isVideo: true,
                                                    isCamera: true)
                                                .then((value) {
                                              Navigator.of(context).pop();
                                              Navigator.of(context).pop();
                                              Navigator.of(context).push(
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      ChatPage(
                                                    matchId: matchModel.id,
                                                    otherUserId:
                                                        otherUser.userId,
                                                    introVideo: value,
                                                  ),
                                                ),
                                              );
                                            });
                                          }
                                        },
                                      );
                                    });
                              } else {
                                if (introDate == today && introCount > 10) {
                                  EasyLoading.showError(
                                      "Sorry, you've exceeded the daily limit of 10 introductory videos. Please try again tomorrow.");
                                } else {
                                  pickMedia(isVideo: true, isCamera: true)
                                      .then((value) {
                                    Navigator.of(context).pop();
                                    Navigator.of(context).pop();
                                    Navigator.of(context).push(
                                      MaterialPageRoute(
                                        builder: (context) => ChatPage(
                                          matchId: matchModel.id,
                                          otherUserId: otherUser.userId,
                                          introVideo: value,
                                        ),
                                      ),
                                    );
                                  });
                                }
                              }
                            },
                            child: const Text("Send Intro Video"))),
                    const SizedBox(width: AppConstants.defaultNumericValue),
                    Expanded(
                      child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            Navigator.of(context).pop();
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => ChatPage(
                                  matchId: matchModel.id,
                                  otherUserId: otherUser.userId,
                                ),
                              ),
                            );
                          },
                          child: const Text("Start Chat")),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 10,
                ),
                // if (otherUser.isPremiumUser == null ||
                //     otherUser.isPremiumUser != true)
                //   Text("${otherUser.fullName} is not a premium user")
              ],
            );
          },
        );
      }
    });
  }

  void createInteractionNotification(
      {required String title,
      required String body,
      required String userId,
      required int notificationType,
      required UserProfileModel currentUser}) async {
    final currentTime = DateTime.now();
    final id = currentTime.millisecondsSinceEpoch.toString();
    final NotificationModel notificationModel = NotificationModel(
      id: id,
      userId: currentUser.userId,
      receiverId: userId,
      title: title,
      body: body,
      userName: "${currentUser.firstName} ${currentUser.lastName}",
      notificationType: notificationType,
      image: currentUser.profilePicture,
      createdAt: currentTime,
      isRead: false,
      isMatchingNotification: false,
      isInteractionNotification: true,
    );

    await addNotification(notificationModel);
  }
}
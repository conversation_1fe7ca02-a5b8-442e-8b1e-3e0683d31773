import 'package:fringle_app/views/tabs/home/<USER>';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
import '../../../helpers/constants.dart';
import '../../custom/custom_button.dart';
import '../../custom/seeking_filter_dropdown.dart';
import '../../custom/custom_new_app_bar.dart';
import '../../settings/couple_prefrences.dart';

class FilterScreen extends StatefulWidget {
  final Function(FilterData data) filterData;
  final bool isCoupleAccount;
  final bool isPremiumUser;
  final bool isPremiumPlusUser;
  const FilterScreen(
      {super.key,
      required this.filterData,
      required this.isCoupleAccount,
      required this.isPremiumUser,
      required this.isPremiumPlusUser});

  @override
  State<FilterScreen> createState() => _FilterScreenState();
}

class _FilterScreenState extends State<FilterScreen> {
  String? selectedGender;
  String? selectedSeekingFilter;
  double ageStart = 18.0;
  double ageEnd = 99.0;
  double distance = 100;
  late SharedPreferences _prefs;

  @override
  void initState() {
    super.initState();
    setData();
  }

  Future<void> setData() async {
    _prefs = await SharedPreferences.getInstance();

    if (!widget.isPremiumPlusUser &&
        _prefs.getString('seekingFilter') == "Collaborators") {
      _prefs.remove("seekingFilter");
    }
    if (!(widget.isPremiumUser || widget.isPremiumPlusUser) &&
        ((_prefs.getString('seekingFilter') == "Kinksters") ||
            (_prefs.getString('seekingFilter') == "Couples"))) {
      _prefs.remove("seekingFilter");
    }

    if (_prefs.getString('gender') != null) {
      selectedGender = _prefs.getString('gender')!.isEmpty
          ? null
          : _prefs.getString('gender');
    }
    if (_prefs.getString('seekingFilter') != null) {
      selectedSeekingFilter = _prefs.getString('seekingFilter')!.isEmpty
          ? null
          : _prefs.getString('seekingFilter');
    }
    ageStart = _prefs.getDouble('ageStart') ?? 18.0;
    ageEnd = _prefs.getDouble('ageEnd') ?? 99.0;
    distance = _prefs.getDouble('distance') ?? 100;

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color(0xFFFFFCF5),
        appBar: GradientAppBar(
          title: "filter".toUpperCase(),
        ),
        body: Column(children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      children: [
                        const SizedBox(
                          height: 20,
                        ),
                        const Divider(
                          color: Color(0xFFD1D1D1),
                          height: 1,
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        InkWell(
                          onTap: () {
                            if (widget.isCoupleAccount ?? false) {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const CouplePrefrences(),
                                ),
                              );
                            } else {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const PreferencePage(
                                    isFromFilter: true,
                                  ),
                                ),
                              );
                            }
                          },
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Padding(
                                padding: EdgeInsets.only(left: 16),
                                child: Text(
                                  "Preferences",
                                  style: TextStyle(
                                      fontSize: 18,
                                      color: Colors.black,
                                      fontWeight: FontWeight.w600),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(right: 16),
                                child: SvgPicture.asset(
                                  AppConstants.rightArrowIcon,
                                  height: 24,
                                  width: 24,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        const Divider(
                          color: Color(0xFFD1D1D1),
                          height: 1,
                        ),
                      ],
                    ),
                  ),
                  // Padding(
                  //   padding: const EdgeInsets.fromLTRB(20, 17, 20, 20),
                  //   child: CustomDropdownNew(
                  //     items: AppConstants.genderList
                  //         .map((gender) => gender.name)
                  //         .toList(),
                  //     selectedValue: selectedGender,
                  //     onChanged: (newValue) {
                  //       //Update the selected value
                  //       setState(() {
                  //         selectedGender = newValue;
                  //       });
                  //     },
                  //     labelText: 'Gender',
                  //     hintText: 'Select',
                  //   ),
                  // ),
                  const SizedBox(
                    height: 40,
                  ),
                  Padding(
                    padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          "Age range",
                          style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: Colors.black),
                        ),
                        const SizedBox(
                          height: 7,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              "Only show people in this range",
                              style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black),
                            ),
                            GradientText(
                              gradientDirection: GradientDirection.ttb,
                              colors: const [
                                Color(0xFFC70973),
                                Color(0xFF46239F)
                              ],
                              "${ageStart.toInt()}-${ageEnd.toInt()}",
                              style: TextStyle(
                                color: AppConstants.primaryColor,
                                fontWeight: FontWeight.w700,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                  RangeSlider(
                    values: RangeValues(ageStart, ageEnd),
                    min: 18.0,
                    max: 99.0,
                    inactiveColor: const Color(0xFFE7E7E7),
                    onChanged: (RangeValues values) {
                      if ((values.end.toInt() - values.start.toInt()) < 3) {
                        return;
                      }
                      setState(() {
                        ageStart = values.start;
                        ageEnd = values.end;
                      });
                    },
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  Padding(
                    padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          "Distance",
                          style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: Colors.black),
                        ),
                        const SizedBox(
                          height: 7,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              "Only show people in this\nrange",
                              style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black),
                            ),
                            GradientText(
                              gradientDirection: GradientDirection.ttb,
                              colors: const [
                                Color(0xFFC70973),
                                Color(0xFF46239F)
                              ],
                              "${distance.toInt()} miles",
                              style: TextStyle(
                                color: AppConstants.primaryColor,
                                fontWeight: FontWeight.w700,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                  Slider(
                    value: distance,
                    max: 1000,
                    onChanged: (value) {
                      // if ((values.end.toInt() - values.start.toInt()) < 3) {
                      //   return;
                      // }
                      setState(() {
                        distance = value;
                      });
                    },
                  ),

                  Padding(
                    padding: const EdgeInsets.fromLTRB(20, 17, 20, 20),
                    child: SeekingFilterDropdown(
                      items: widget.isPremiumUser
                          ? seekingFilterListWithPremium
                          : widget.isPremiumPlusUser
                              ? seekingFilterListWithPremiumPlus
                              : seekingFilterList,
                      selectedValue: selectedSeekingFilter,
                      onChanged: (newValue) {
                        //Update the selected value
                        setState(() {
                          selectedSeekingFilter = newValue;
                        });
                      },
                      labelText: 'Seeking',
                      hintText: 'Select',
                    ),
                  ),
                ],
              ),
            ),
          ),
          Padding(
              padding: const EdgeInsets.all(24),
              child: SizedBox(
                  height: 52,
                  child: CustomButton(
                      onPressed: () {
                        final filter = FilterData(
                            gender: selectedGender ?? "",
                            startAge: ageStart.toInt(),
                            endAge: ageEnd.toInt(),
                            distance: distance.toInt(),
                            seekingFilter: selectedSeekingFilter ?? "");
                        widget.filterData(filter);
                        _prefs.setString("gender", selectedGender ?? "");
                        _prefs.setDouble("ageStart", ageStart);
                        _prefs.setDouble("ageEnd", ageEnd);
                        _prefs.setDouble("distance", distance);
                        _prefs.setString(
                            "seekingFilter", selectedSeekingFilter ?? "");

                        Navigator.pop(context);
                      },
                      text: "Apply")))
        ]));
  }
}

class FilterData {
  final String gender;
  final String seekingFilter;
  final int startAge;
  final int endAge;
  final int distance;

  FilterData({
    required this.gender,
    required this.seekingFilter,
    required this.startAge,
    required this.endAge,
    required this.distance,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FilterData &&
          runtimeType == other.runtimeType &&
          gender == other.gender &&
          seekingFilter == other.seekingFilter &&
          startAge == other.startAge &&
          endAge == other.endAge &&
          distance == other.distance;

  @override
  int get hashCode =>
      gender.hashCode ^
      seekingFilter.hashCode ^
      startAge.hashCode ^
      endAge.hashCode ^
      distance.hashCode;
}

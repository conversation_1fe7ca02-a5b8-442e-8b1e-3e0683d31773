import 'package:flutter/material.dart';

import '../../helpers/constants.dart';
import 'custom_feature_row_widget.dart';

class FreeMembership extends StatefulWidget {
  final bool isPremiumUser;
  final bool isPremiumPlusUser;
  const FreeMembership(
      {Key? key, required this.isPremiumUser, required this.isPremiumPlusUser});

  @override
  State<FreeMembership> createState() => _FreeMembershipState();
}

class _FreeMembershipState extends State<FreeMembership> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(
            height: 24,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Card(
              elevation: 1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(14.0),
              ),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(14),
                  color: Colors.white,
                ),
                child: Column(
                  children: [
                    if (!(widget.isPremiumUser || widget.isPremiumPlusUser))
                      Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 20, vertical: 5),
                        width: double.infinity,
                        decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Color(0xFFD9056C).withOpacity(
                                    .5), // Color(red: 0.85, green: 0.02, blue: 0.42)
                                Color(0xFF3A24A2).withOpacity(
                                    .5), // Color(red: 0.23, green: 0.14, blue: 0.64)
                              ],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              // begin: Alignment(0, -0.42),
                              // end: Alignment(0.98, 1.6),
                            ),
                            borderRadius: BorderRadius.only(
                                topRight: Radius.circular(14),
                                topLeft: Radius.circular(14))),
                        child: const Text(
                          "CURRENT PLAN",
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.w700),
                        ),
                      ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            "Free",
                            style: TextStyle(
                              color: Color(0xff000000),
                              fontSize: 24,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          SizedBox(
                            height: 14,
                          ),
                          Text(
                            "For Traditional Relationships, Basic Swipe & Match Features.",
                            style: TextStyle(
                                height: 1.4,
                                color: Color(0xff737373),
                                fontSize: 14,
                                fontWeight: FontWeight.w400),
                            textAlign: TextAlign.start,
                          ),
                          SizedBox(
                            height: 30,
                          ),
                          CustomFeatureRow(
                              name: "Swipe",
                              iconPath: AppConstants.premiumCheckIcon),
                          SizedBox(
                            height: 6,
                          ),
                          CustomFeatureRow(
                              name: "Match",
                              iconPath: AppConstants.premiumCheckIcon),
                          SizedBox(
                            height: 6,
                          ),
                          CustomFeatureRow(
                              name: "Message",
                              iconPath: AppConstants.premiumCheckIcon),
                          SizedBox(
                            height: 6,
                          ),
                          CustomFeatureRow(
                              name: "Like",
                              iconPath: AppConstants.premiumCloseIcon),
                          SizedBox(
                            height: 6,
                          ),
                          CustomFeatureRow(
                              name: "Unlimited Scroll to New Profiles",
                              iconPath: AppConstants.premiumCloseIcon),
                          SizedBox(
                            height: 6,
                          ),
                          CustomFeatureRow(
                              name: "Send 8 sec Introductory Video",
                              iconPath: AppConstants.premiumCloseIcon),
                          SizedBox(
                            height: 6,
                          ),
                          CustomFeatureRow(
                              name: "Create a Couple Account",
                              iconPath: AppConstants.premiumCloseIcon),
                          SizedBox(
                            height: 6,
                          ),
                          CustomFeatureRow(
                              name: "Get Featured",
                              iconPath: AppConstants.premiumCloseIcon),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    ));
  }
}

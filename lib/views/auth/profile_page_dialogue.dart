import 'package:flutter/material.dart';

class PremiumFeaturesDialog extends StatelessWidget {
  const PremiumFeaturesDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: SingleChildScrollView(
        child: <PERSON>umn(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Premium Features',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const ListTile(
              title: Text('Premium Feature 1'),
              subtitle: Text('Description of Premium Feature 1'),
            ),
            const ListTile(
              title: Text('Premium Feature 2'),
              subtitle: Text('Description of Premium Feature 2'),
            ),
            const ListTile(
              title: Text('Premium Feature 3'),
              subtitle: Text('Description of Premium Feature 3'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // Perform the action to redirect the user to the app store
              },
              child: const Text('Buy Now'),
            ),
            const SizedBox(height: 16),
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
      ),
    );
  }
}

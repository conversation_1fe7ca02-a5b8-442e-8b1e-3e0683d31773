import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fringle_app/views/custom/custom_app_loader.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../helpers/constants.dart';
import '../../helpers/validators.dart';
import '../../providers/auth_providers.dart';
import '../../providers/user_profile_provider.dart';
import '../custom/custom_button.dart';
import '../custom/custom_new_app_bar.dart';
import '../custom/custom_textfield.dart';

class CoupleAccountDetials extends ConsumerStatefulWidget {
  const CoupleAccountDetials({super.key});

  @override
  ConsumerState<CoupleAccountDetials> createState() =>
      _CoupleAccountDetialsState();
}

class _CoupleAccountDetialsState extends ConsumerState<CoupleAccountDetials> {
  TextEditingController nameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  QuerySnapshot<Map<String, dynamic>>? data;

  @override
  void initState() {
    super.initState();
    CustomAppLoader.showCustomLoader("Please wait...");
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final userCollection = FirebaseFirestore.instance
          .collection(FirebaseConstants.coupleAccount);
      userCollection
          .where("coupleId",
              isEqualTo: ref.watch(currentUserStateProvider)?.uid)
          .get()
          .then((data) {
        nameController.text = data.docs.first.data()['couplename'];
        emailController.text = data.docs.first.data()['email'];
        this.data = data;

        setState(() {});
        CustomAppLoader.dismissCustomLoader();
      });
    });
  }

  void deleteCoupleAccount() {
    CustomAppLoader.showCustomLoader("Please wait...");
    final user = ref.read(userProfileFutureProvider);
    user.when(
      data: (data) async {
        if (data != null) {
          ref.read(userProfileNotifier).updateUserProfile(data.copyWith(
              isCoupleAccount: false,
              couplePrefrenceSecond: {},
              couplePrefrencePrimary: {},
              preferences: {}));
          final coupleUserCollection = FirebaseFirestore.instance
              .collection(FirebaseConstants.coupleAccount);
          coupleUserCollection
              .where("coupleId", isEqualTo: data.userId)
              .get()
              .then((coupleData) async {
            var coupleUserId = coupleData.docs.first.data()['userId'];
            await ref.read(authProvider).deleteUser(coupleUserId).then((value) {
              if (value) {
                coupleUserCollection.doc(data.userId).delete();
                ref.invalidate(userProfileFutureProvider);
                EasyLoading.dismiss();
                Navigator.pop(context);
              }
            });
          });
        }
      },
      error: (error, stackTrace) {
        EasyLoading.dismiss();
        EasyLoading.showToast("Someting want wrong.");
      },
      loading: () {},
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFFFCF5),
      appBar: GradientAppBar(
        title: "Remove Secondary Account".toUpperCase(),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(20, 34, 20, 0),
        child: Column(
          children: [
            CustomTextField2(
              controller: emailController,
              labelText: "Email",
              hintText: "Email",
              isenabled: false,
              validator: Validators.validateCoupleEmail,
            ),
            const SizedBox(
              height: 20,
            ),
            CustomTextField2(
              controller: nameController,
              labelText: "Username",
              hintText: "Name",
              isenabled: false,
              validator: Validators.validateCoupleName,
            ),
            const Spacer(),
            Padding(
              padding: const EdgeInsets.only(bottom: 24),
              child: SizedBox(
                height: 52,
                child: CustomButton(
                  onPressed: () {
                    deleteCoupleAccount();
                  },
                  text: "Remove",
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

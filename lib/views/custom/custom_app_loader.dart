import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import '../../helpers/constants.dart';

class CustomAppLoader {
  static void showCustomLoader(String text) {
    EasyLoading.show(
        status: text,
        indicator: Stack(
          children: [
            BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 2.0, sigmaY: 2.0),
              child: Container(
                color: Colors.black.withOpacity(0.3),
              ),
            ),
            Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  LoadingAnimationWidget.staggeredDotsWave(
                    color: AppConstants.primaryColor,
                    size: 60,
                  ),
                ],
              ),
            ),
          ],
        ));
  }

  static void dismissCustomLoader() {
    EasyLoading.dismiss();
  }
}

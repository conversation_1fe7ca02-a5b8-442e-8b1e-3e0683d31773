import 'dart:io';

import 'package:animations/animations.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/models/user_profile_model.dart';
import 'package:fringle_app/providers/banned_users_provider.dart';
import 'package:fringle_app/providers/match_provider.dart';
import 'package:fringle_app/providers/user_profile_provider.dart';
import 'package:fringle_app/views/others/custom_gradient_background.dart';
import 'package:fringle_app/views/others/error_page.dart';
import 'package:fringle_app/views/others/loading_page.dart';
import 'package:fringle_app/views/others/user_is_banned_page.dart';
import 'package:fringle_app/views/tabs/home/<USER>';
import 'package:fringle_app/views/tabs/messages/messages_page.dart';
import 'package:fringle_app/views/tabs/profile/first_time_update_profile_page.dart';
import 'package:fringle_app/views/tabs/profile/profile_page.dart';

import '../../main.dart';
import '../custom/custom_status_bar_theme.dart';
import 'home/home_tab_page.dart';
import 'messages/components/chat_page.dart';

class BottomNavBarPage extends ConsumerStatefulWidget {
  final bool isPremiumUser;
  final bool isPremiumPlusUser;
  final String userId;
  const BottomNavBarPage(
      {super.key,
      required this.userId,
      required this.isPremiumPlusUser,
      required this.isPremiumUser});

  @override
  ConsumerState<BottomNavBarPage> createState() => _BottomNavBarPageState();
}

class _BottomNavBarPageState extends ConsumerState<BottomNavBarPage>
    with WidgetsBindingObserver {
  int _currentIndex = 0;
  late SharedPreferences _prefs;

  @override
  void initState() {
    FirebaseMessaging.instance.getInitialMessage().then(
      (message) {
        handleFirebaseMessaging(message);
      },
    );
    FirebaseMessaging.onMessageOpenedApp.listen((message) {
      handleFirebaseMessaging(message);
    });

    FirebaseMessaging.onMessage.listen((message) {
      if (Platform.isAndroid) {
        _showCustomNotifcation(message);
      }
    });
    removePreference();
    WidgetsBinding.instance.addObserver(this);
    // initPlatformStateForPurchases(widget.userId);
    // setSubscriptionUpdate();
    super.initState();
  }

  Future<void> removePreference() async {
    _prefs = await SharedPreferences.getInstance();
    var isSecondaryAccount = _prefs.getBool(AppConstants.coupleAccount);
    _prefs.clear();
    _prefs.setBool(AppConstants.coupleAccount, isSecondaryAccount ?? false);
    setState(() {});
  }

  handleFirebaseMessaging(RemoteMessage? message) {
    if (message == null) {
      return;
    }
    if (message.data["type"] == "like" || message.data["type"] == "fav") {
      _currentIndex = 1;
    } else if (message.data['type'] == 'message' ||
        message.data["type"] == "match") {
      final otherUserId = message.data["userId"];
      final matchId = message.data["matchId"];
      _currentIndex = 2;
      if (otherUserId != null &&
          otherUserId.toString().isNotEmpty &&
          matchId.toString().isNotEmpty) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) =>
                ChatPage(matchId: matchId, otherUserId: otherUserId),
          ),
        );
      }
    }
  }

  _showCustomNotifcation(RemoteMessage message) async {
    var title = message.notification?.title ?? "";
    var desc = message.notification?.body ?? "";
    if (_currentIndex == 2) {
      return;
    }
    OverlayState overlayState = Overlay.of(navigationKey.currentContext!);
    OverlayEntry overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: 30,
        left: 10,
        right: 10,
        child: InkWell(
          onTap: () {
            if (_currentIndex == 2) {
              return;
            }
            handleFirebaseMessaging(message);
          },
          child: CustomGradientWidget(
            borderRadius: 8,
            child: Padding(
              padding: const EdgeInsets.all(10.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 15,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    desc,
                    style: const TextStyle(
                      fontSize: 13,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
    overlayState.insert(overlayEntry);
    await Future.delayed(const Duration(seconds: 3));
    overlayEntry.remove();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // setSubscriptionUpdate();
      setUserOnlineStatus(true);
    } else if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.detached) {
      setUserOnlineStatus(false);
    }

    super.didChangeAppLifecycleState(state);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  // here we are checking that subscription is for current logged in user or not and set subscription variable true or false accordingly

  // void setSubscriptionUpdate() {
  //   final userRef = ref.read(userProfileFutureProvider);
  //   userRef.when(
  //       data: (value) {
  //         if (value != null) {
  //           ref.read(userProfileNotifier).isUserPremium().then((data) async {
  //             if (data == value.userId) {
  //               if (widget.isPremiumUser && !widget.isPremiumPlusUser) {
  //                 var preferences = value.preferences;
  //                 if (preferences != null) {
  //                   preferences.remove("contentCreator");
  //                 }
  //                 ref.read(userProfileNotifier).updateUserProfile(value
  //                     .copyWith(isPremiumUser: true, isPremiumPlusUser: false));
  //                 // ref.read(userProfileNotifier).updateUserProfile(
  //                 //     value.copyWith(
  //                 //         isPremiumPlusUser: false,
  //                 //         preferences: preferences));
  //               } else if (!widget.isPremiumUser && widget.isPremiumPlusUser) {
  //                 ref.read(userProfileNotifier).updateUserProfile(value
  //                     .copyWith(isPremiumPlusUser: true, isPremiumUser: false));
  //               } else if (widget.isPremiumPlusUser && widget.isPremiumUser) {
  //                 ref.read(userProfileNotifier).updateUserProfile(value
  //                     .copyWith(isPremiumPlusUser: true, isPremiumUser: false));
  //               }
  //               ref.invalidate(userProfileFutureProvider);
  //             } else {
  //               var preferences = value.preferences;
  //               if (preferences != null) {
  //                 preferences.remove("kink");
  //                 preferences.remove("contentCreator");
  //                 if (preferences["seeking"] == "Couple") {
  //                   preferences.remove("seeking");
  //                 }
  //               }
  //               ref.read(userProfileNotifier).updateUserProfile(value.copyWith(
  //                   isPremiumUser: false,
  //                   isPremiumPlusUser: false,
  //                   isFeaturedOn: false,
  //                   planType: -1,
  //                   isCoupleAccount: false,
  //                   couplePrefrencePrimary: {},
  //                   couplePrefrenceSecond: {},
  //                   isDMOn: false,
  //                   preferences: preferences));
  //               final coupleUserCollection = FirebaseFirestore.instance
  //                   .collection(FirebaseConstants.coupleAccount);
  //               coupleUserCollection
  //                   .where("coupleId", isEqualTo: value.userId)
  //                   .get()
  //                   .then((coupleData) async {
  //                 if (coupleData.docs.isNotEmpty) {
  //                   var coupleUserId = coupleData.docs.first.data()['userId'];
  //                   coupleUserCollection.doc(value.userId).delete();
  //                   await ref.read(authProvider).deleteUser(coupleUserId);
  //                 }
  //               });
  //               //await userCollection.doc(value.userId).delete();
  //               ref.invalidate(userProfileFutureProvider);
  //               if (_prefs.getBool(AppConstants.coupleAccount) ?? false) {
  //                 ref.read(userProfileNotifier).updateOnlineStatus(
  //                     isOnline: false, userId: value.userId);
  //                 ref.read(authProvider).signOut();
  //                 _prefs.setBool(AppConstants.coupleAccount, false);
  //                 EasyLoading.showToast(
  //                     "Unfortunately, you are not an authorized person to access this app.");
  //                 // ignore: use_build_context_synchronously
  //                 Navigator.pushAndRemoveUntil(
  //                     context,
  //                     MaterialPageRoute(builder: (_) => const LendingPage()),
  //                     (route) => false);
  //               }
  //             }
  //           });
  //         }
  //       },
  //       error: (_, __) => const SizedBox(),
  //       loading: () => const SizedBox());
  // }

  void setUserOnlineStatus(bool status) async {
    final userRef = ref.watch(userProfileFutureProvider);

    UserProfileModel? newModel;

    userRef.whenData((value) {
      if (value != null) {
        if (value.userAccountSettingsModel.showOnlineStatus != false) {
          newModel = value.copyWith(isOnline: status);
        }
      }
    });

    if (newModel != null) {
      await ref.read(userProfileNotifier).updateUserProfile(newModel!);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isUserAdded = ref.watch(isUserAddedProvider);
    final isMeBanned = ref.watch(isMeBannedProvider);

    return isMeBanned.when(
      data: (data) {
        bool isBanned = false;

        if (data == null) {
          isBanned = false;
        } else {
          if (data.bannedUntil.isAfter(DateTime.now())) {
            isBanned = true;
          } else if (data.isLifetimeBan) {
            isBanned = true;
          } else {
            isBanned = false;
          }
        }

        return isBanned
            ? UserIsBannedPage(bannedUserModel: data!)
            : isUserAdded.when(
                loading: () => const LoadingPage(),
                error: (e, _) => const LoadingPage(),
                data: (data) {
                  return data
                      ? CustomStatusBarTheme(
                          isLightTheme: false,
                          child: Scaffold(
                            body: PageTransitionSwitcher(
                              duration: const Duration(milliseconds: 300),
                              transitionBuilder: (child, primaryAnimation,
                                      secondaryAnimation) =>
                                  FadeThroughTransition(
                                fillColor:
                                    Theme.of(context).scaffoldBackgroundColor,
                                animation: primaryAnimation,
                                secondaryAnimation: secondaryAnimation,
                                child: child,
                              ),
                              child: _currentIndex == 0
                                  ? HomeTabPage(onTapChange: (notificationData) {
                                      if (notificationData.notificationType ==
                                              AppConstants
                                                  .favoritedNotifacation ||
                                          notificationData.notificationType ==
                                              AppConstants.likeNotifacation) {
                                        setState(() {
                                          _currentIndex = 1;
                                        });
                                      } else {
                                        setState(() {
                                          _currentIndex = 2;
                                        });

                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => ChatPage(
                                                matchId:
                                                    notificationData.matchId ??
                                                        "",
                                                otherUserId:
                                                    notificationData.userId ??
                                                        ""),
                                          ),
                                        );
                                      }
                                    })
                                  : _currentIndex == 1
                                      ? const FavoriteList()
                                      : _currentIndex == 2
                                          ? MessageConsumerPage(
                                              onTapChange: (notificationData) {
                                              if (notificationData
                                                          .notificationType ==
                                                      AppConstants
                                                          .favoritedNotifacation ||
                                                  notificationData
                                                          .notificationType ==
                                                      AppConstants
                                                          .likeNotifacation) {
                                                setState(() {
                                                  _currentIndex = 1;
                                                });
                                              } else {
                                                setState(() {
                                                  _currentIndex = 2;
                                                });
                                                Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder: (context) => ChatPage(
                                                        matchId:
                                                            notificationData
                                                                    .matchId ??
                                                                "",
                                                        otherUserId:
                                                            notificationData
                                                                    .userId ??
                                                                ""),
                                                  ),
                                                );
                                              }
                                            })
                                          : const ProfilePage(),
                              // child: (_navItems[_currentIndex].icon ==
                              //         AppConstants.favorite)
                              //     ? (userProfileModel?.isPremiumUser == null)
                              //         ? _navItems[_currentIndex].page
                              //         : const MembershipScreen(
                              //             fromBottomBar: true)
                              //     : _navItems[_currentIndex].page,
                            ),
                            bottomNavigationBar: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(30),
                                  topRight: Radius.circular(30),
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 12,
                                    spreadRadius: 2,
                                    offset: Offset(0, 0),
                                  ),
                                ],
                                color: Color(0xFFF8FAFF),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.only(
                                    top: AppConstants.defaultNumericValue),
                                child: WillPopScope(
                                  onWillPop: () async => !Navigator.of(context)
                                      .userGestureInProgress,
                                  child: BottomNavigationBar(
                                    unselectedLabelStyle: const TextStyle(
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold),
                                    selectedLabelStyle: const TextStyle(
                                        fontSize: 11,
                                        fontWeight: FontWeight.bold),
                                    backgroundColor: Color(0xFFF8FAFF),
                                    elevation: 0,
                                    type: BottomNavigationBarType.fixed,
                                    currentIndex: _currentIndex,
                                    onTap: (index) {
                                      setState(() {
                                        _currentIndex = index;
                                      });
                                    },
                                    items: _navItems.map((e) {
                                      return BottomNavigationBarItem(
                                        icon: MessageConsumerBottomNavIcon(
                                            icon: e.icon),
                                        label: e.title,
                                        activeIcon:
                                            MessageConsumerBottomNavIcon(
                                                icon: e.activeIcon),
                                      );
                                    }).toList(),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        )
                      : FirstTimeUserProfilePage(
                          // onTap: (v) {
                          //   Navigator.of(context)
                          //       .push(MaterialPageRoute(builder: (context) {
                          //     return SubscriptionBuilder(
                          //         // children: [
                          //         //   FilterInteraction(users: data),
                          //         // ],
                          //         builder: (context, isPremiumUser,
                          //             isPremiumPlusUser) {
                          //       return MembershipScreen(
                          //         isFirstTime: v,
                          //         isPremiumUser: isPremiumUser,
                          //         isPremiumPlusUser: isPremiumPlusUser,
                          //       );
                          //     });
                          //   }));
                          // },
                        );
                },
              );
      },
      error: (error, stackTrace) => const ErrorPage(),
      loading: () => const LoadingPage(),
    );
  }
}

class _BottomNavBarItem {
  final String title;
  final String icon;
  final String activeIcon;

  _BottomNavBarItem({
    required this.title,
    required this.icon,
    required this.activeIcon,
  });
}

final List<_BottomNavBarItem> _navItems = [
  _BottomNavBarItem(
    title: '',
    icon: AppConstants.homeIcon,
    activeIcon: AppConstants.homeIconGreen,
  ),
  //Explore
  _BottomNavBarItem(
    title: '',
    icon: AppConstants.chatIcon,
    activeIcon: AppConstants.chatIconGreen,
  ),
  //Favourites
  _BottomNavBarItem(
    title: '',
    icon: AppConstants.requestIcon,
    activeIcon: AppConstants.requestIconGreen,
  ),
  //Messages
  _BottomNavBarItem(
    title: '',
    icon: AppConstants.accountIcon,
    activeIcon: AppConstants.accountIconGreen,
  ),
  //Proile
];

class MessageConsumerBottomNavIcon extends ConsumerWidget {
  final String icon;
  const MessageConsumerBottomNavIcon({
    super.key,
    required this.icon,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final matchStream = ref.watch(matchStreamProvider);

    return matchStream.when(
      data: (data) {
        final List<MessageViewModel> messages = [];

        messages.addAll(getAllMessages(ref, data));
        int unreadCount = 0;
        for (var e in messages) {
          unreadCount += e.unreadCount;
        }

        return MessageIcon(unreadCount: unreadCount, icon: icon);
      },
      error: (_, __) => MessageIcon(unreadCount: 0, icon: icon),
      loading: () => MessageIcon(unreadCount: 0, icon: icon),
    );
  }
}

class MessageIcon extends StatelessWidget {
  final int unreadCount;
  final String icon;
  const MessageIcon({
    super.key,
    required this.unreadCount,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SvgPicture.asset(
          icon,
        ),
        if (unreadCount > 0)
          if (icon == AppConstants.chat)
            Positioned(
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(1),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(6),
                ),
                constraints: const BoxConstraints(minWidth: 12, minHeight: 12),
                child: Center(
                  child: Text(
                    '$unreadCount',
                    style: const TextStyle(
                        color: Colors.white,
                        fontSize: 7,
                        fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
      ],
    );
  }
}

import 'dart:io';

import 'package:firebase_storage/firebase_storage.dart';
import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/helpers/media_picker_helper.dart';
import 'package:fringle_app/models/user_profile_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsfw_detector_flutter/nsfw_detector_flutter.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';

import '../../../helpers/validators.dart';
import '../../../models/custom_check_model.dart';
import '../../../providers/auth_providers.dart';
import '../../../providers/user_profile_provider.dart';
import '../../custom/custom_app_loader.dart';
import '../../custom/custom_check_button.dart';
import '../../custom/custom_clip_item.dart';
import '../../custom/custom_dropdown_new.dart';
import '../../custom/custom_radio_button.dart';
import '../../custom/custom_status_bar_theme.dart';
import '../../custom/custom_textfield.dart';

class EditProfilePage extends ConsumerStatefulWidget {
  final UserProfileModel userProfileModel;
  const EditProfilePage({super.key, required this.userProfileModel});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _EditProfilePageState();
}

class _EditProfilePageState extends ConsumerState<EditProfilePage> {
  List<CustomCipsModel> pronounsList =
      AppConstants.createChipsList(AppConstants.pronounsList);
  List<CustomCipsModel> interestsList =
      AppConstants.createChipsList(AppConstants.interestsList);
  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  TextEditingController interestsController = TextEditingController();
  TextEditingController bioController = TextEditingController();
  TextEditingController heightController = TextEditingController();
  String? selectedGender;
  DateTime? selectedBirthday;
  int pronounsCount = 0;
  int interestsCount = 0;

  TextEditingController workController = TextEditingController();
  String? selectedEducation;
  String? selectedEthnicity;
  String? selectedExercise;
  String? selectedDrinking;
  String? selectedSmoking;
  String? selectedCannabis;
  String? selectedKids;
  String? selectedZodiacSign;

  List<CustomCheckModel> smokingList =
      AppConstants.createRadioList(AppConstants.radioBtnList);

  List<CustomCheckModel> drinkingList =
      AppConstants.createRadioList(AppConstants.radioBtnList);

  final _formKey = GlobalKey<FormState>();
  List<CustomCheckModel> items =
      AppConstants.createRadioList(AppConstants.genderList);
  List<String> imageList = [];

  @override
  void initState() {
    var data = widget.userProfileModel;
    firstNameController.text = data.firstName ?? "";
    lastNameController.text = data.lastName ?? "";
    selectedGender = data.gender;
    for (var item in items) {
      if (item.name == selectedGender) {
        item.isSelected = true;
        break; // If you only expect one match, you can break the loop after updating.
      }
    }
    for (var item in data.pronounceList ?? []) {
      for (var pro in pronounsList) {
        if (pro.name == item.toString()) {
          pronounsCount++;
          pro.isSelected = true;
          break;
        }
      }
    }
    bioController.text = data.bio ?? "";
    for (var item in data.interests ?? []) {
      if (interestsList.any((interest) =>
          interest.name.toLowerCase().trim() !=
          item.toString().toLowerCase().trim())) {
        interestsList.add(CustomCipsModel(
            id: (interestsList.length + 1), name: item.toString()));
      }
      for (var pro in interestsList) {
        if (pro.name == item.toString()) {
          interestsCount++;
          pro.isSelected = true;
          break;
        }
      }
    }

    if (widget.userProfileModel.imageList != null) {
      imageList.addAll(widget.userProfileModel.imageList!.toList());
    }
    if (imageList.length < 6) {
      imageList.add("");
    }
    workController.text = data.work ?? "";
    selectedEducation = data.education;
    selectedCannabis = data.cannabis;
    for (var smokingItem in smokingList) {
      if (smokingItem.name == selectedSmoking) {
        smokingItem.isSelected = true;
      } else {
        smokingItem.isSelected = false;
      }
    }
    for (var drinkingItem in drinkingList) {
      if (drinkingItem.name == selectedDrinking) {
        drinkingItem.isSelected = true;
      } else {
        drinkingItem.isSelected = false;
      }
    }

    super.initState();
  }

  void _onSave() async {
    if (imageList.length < 3) {
      EasyLoading.showToast("at least 2 images are required",
          toastPosition: EasyLoadingToastPosition.bottom);
      return;
    }
    if (_formKey.currentState!.validate()) {
      CustomAppLoader.showCustomLoader("Saving...");

      for (int i = 0; i < imageList.length; i++) {
        var media = imageList[i];
        if (!Uri.parse(imageList[i]).isAbsolute) {
          if (media == "") {
            imageList.removeAt(i);
          } else {
            final mediaURL = await _uploadUserMediaFiles(
              media,
              ref.watch(currentUserStateProvider)!.uid,
            );
            if (mediaURL != null) {
              imageList[i] = mediaURL;
            }
          }
        }
      }
      final newUserProfileModel = widget.userProfileModel.copyWith(
        firstName: firstNameController.text.trim(),
        lastName: lastNameController.text.trim(),
        gender: selectedGender,
        pronounceList: pronounsList
            .where((pronoun) => pronoun.isSelected) // Filter selected pronouns
            .map((pronoun) => pronoun.name) // Extract names
            .toList(),
        bio: bioController.text.trim(),
        interests: interestsList
            .where((interests) =>
                interests.isSelected) // Filter selected interests
            .map((interests) => interests.name) // Extract names
            .toList(),
        education: selectedEducation,
        work: workController.text.trim(),
        cannabis: selectedCannabis,
        imageList: imageList,
        profilePicture: imageList[0],
      );

      await ref
          .read(userProfileNotifier)
          .updateUserProfile(newUserProfileModel)
          .then((value) {
        EasyLoading.dismiss();
        ref.invalidate(userProfileFutureProvider);
        Navigator.pop(context);
      });
    }
  }

  void selecImage(int index) async {
    final imagePath = await pickMedia();
    if (imagePath != null) {
      NsfwDetector detector = await NsfwDetector.load();
      NsfwResult? result = await detector.detectNSFWFromFile(File(imagePath));
      final hasNudity = result?.isNsfw ?? false;
      if (hasNudity) {
        EasyLoading.showError(
            "Our platform does not allow the upload of explicit content. Let's keep it classy and respectful for everyone.");
      } else {
        setState(() {
          if (index == -1) {
            imageList.removeLast();
            imageList.add(imagePath);
            if (imageList.length < 6) {
              imageList.add("");
            }
          } else {
            imageList[index] = imagePath;
          }
        });
      }
    }
  }

  Future<String?> _uploadUserMediaFiles(String path, String userId) async {
    final storageRef = FirebaseStorage.instance.ref();

    final imageRef =
        storageRef.child("user_media_files/$userId/${path.split("/").last}");
    final uploadTask = imageRef.putFile(File(path));

    String? imageUrl;
    await uploadTask.whenComplete(() async {
      imageUrl = await imageRef.getDownloadURL();
    });
    return imageUrl;
  }

  void _handleGenderSelectionChanged(int itemId, bool isSelected) {
    setState(() {
      items = items.map<CustomCheckModel>((item) {
        if (item.id == itemId) {
          selectedGender = item.name;
          return item.copyWith(isSelected: isSelected);
        } else {
          return item.copyWith(isSelected: false);
        }
      }).toList();
    });
  }

  void _handleSmokingSelectionChanged(int itemId, bool isSelected) {
    setState(() {
      smokingList = smokingList.map<CustomCheckModel>((item) {
        if (item.id == itemId) {
          selectedSmoking = item.name;
          return item.copyWith(isSelected: isSelected);
        } else {
          return item.copyWith(isSelected: false);
        }
      }).toList();
    });
  }

  void _handleDrinkSelectionChanged(int itemId, bool isSelected) {
    setState(() {
      drinkingList = drinkingList.map<CustomCheckModel>((item) {
        if (item.id == itemId) {
          selectedDrinking = item.name;
          return item.copyWith(isSelected: isSelected);
        } else {
          return item.copyWith(isSelected: false);
        }
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return CustomStatusBarTheme(
      isLightTheme: false,
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Scaffold(
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 45, 24, 0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InkWell(
                      onTap: () => Navigator.of(context).pop(),
                      child: SvgPicture.asset(
                        AppConstants.backIcon,
                        color: const Color(0xFF303030),
                        height: 26,
                        width: 26,
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        _onSave();
                      },
                      child: GradientText("Done",
                          colors: const [Color(0xFFC70973), Color(0xFF46239F)],
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontFamily: AppConstants.fontStyleName,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          )),
                    )
                  ],
                ),
              ),
              const Center(
                child: Text(
                  "Edit Profile",
                  style: TextStyle(
                    color: Colors.black,
                    fontFamily: AppConstants.fontStyleName,
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 16),
                        Padding(
                          padding: const EdgeInsets.only(left: 24),
                          child: SizedBox(
                            height: 150,
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: imageList.length,
                              itemBuilder: (BuildContext context, int index) {
                                return imageList[index].isNotEmpty
                                    ? Padding(
                                        padding:
                                            const EdgeInsets.only(right: 8),
                                        child: InkWell(
                                          onTap: () {
                                            selecImage(index);
                                          },
                                          child: Stack(
                                            alignment: Alignment.topRight,
                                            children: [
                                              Container(
                                                width: 120,
                                                decoration: const BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.all(
                                                            Radius.circular(8)),
                                                    color: Colors.black12),
                                                child: ClipRRect(
                                                  borderRadius:
                                                      const BorderRadius.all(
                                                          Radius.circular(8)),
                                                  child: Center(
                                                    child: Uri.parse(imageList[
                                                                index])
                                                            .isAbsolute
                                                        ? Image.network(
                                                            imageList[index],
                                                            height: MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .height /
                                                                1.5,
                                                            width:
                                                                MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .width,
                                                            fit: BoxFit.cover,
                                                            loadingBuilder:
                                                                (context, child,
                                                                    loadingProgress) {
                                                              if (loadingProgress ==
                                                                  null)
                                                                return child;
                                                              return const Center(
                                                                  child: CircularProgressIndicator
                                                                      .adaptive());
                                                            },
                                                            errorBuilder:
                                                                (context, error,
                                                                    stackTrace) {
                                                              return const Center(
                                                                  child: Icon(
                                                                      CupertinoIcons
                                                                          .photo));
                                                            },
                                                          )
                                                        : Image.file(
                                                            height: MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .height /
                                                                1.5,
                                                            width:
                                                                MediaQuery.of(
                                                                        context)
                                                                    .size
                                                                    .width,
                                                            File(imageList[
                                                                index]),
                                                            fit: BoxFit.cover),
                                                  ),
                                                ),
                                              ),
                                              Padding(
                                                padding:
                                                    const EdgeInsets.all(8.0),
                                                child: InkWell(
                                                    onTap: () {
                                                      setState(() {
                                                        if (imageList.isEmpty) {
                                                          return;
                                                        }
                                                        imageList.remove(
                                                            imageList[index]);

                                                        if (!imageList.any(
                                                            (str) => str
                                                                .trim()
                                                                .isEmpty)) {
                                                          imageList.add("");
                                                        }
                                                      });
                                                    },
                                                    child: SvgPicture.asset(
                                                        AppConstants
                                                            .closeSmall)),
                                              )
                                            ],
                                          ),
                                        ),
                                      )
                                    : Padding(
                                        padding:
                                            const EdgeInsets.only(right: 8),
                                        child: SizedBox(
                                          height: 150,
                                          width: 120,
                                          child: InkWell(
                                            onTap: () {
                                              selecImage(-1);
                                            },
                                            child: SvgPicture.asset(
                                              AppConstants.addImage,
                                              fit: BoxFit.fill,
                                            ),
                                          ),
                                        ),
                                      );
                              },
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(
                                height: 8,
                              ),
                              const Text(
                                "You can upload image upto min 2 and max 6.",
                                textAlign: TextAlign.left,
                                style: TextStyle(
                                    fontFamily: AppConstants.fontStyleName,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    color: Color(0xFF717171)),
                              ),
                              const SizedBox(
                                height: 24,
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    CustomTextField2(
                                      controller: firstNameController,
                                      labelText: "First Name *",
                                      hintText: "First Name",
                                      validator: Validators.validateName,
                                    ),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    CustomTextField2(
                                      controller: lastNameController,
                                      labelText: "Last Name *",
                                      hintText: "Last Name",
                                      validator: Validators.validateName,
                                    ),
                                const SizedBox(
                                  height: 20,
                                ),
                                  const Text(
                                    "Gender",
                                    style: TextStyle(
                                      fontFamily: AppConstants.fontStyleName,
                                      color: Color(0xff6A6A6A),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                      // Additional InputDecoration properties...
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 7,
                                  ),
                                  GridView.builder(
                                    padding: EdgeInsets.zero,
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    gridDelegate:
                                        const SliverGridDelegateWithFixedCrossAxisCount(
                                            crossAxisCount: 2,
                                            mainAxisSpacing: 19,
                                            childAspectRatio: 3.0,
                                            crossAxisSpacing: 7),
                                    itemCount: items.length,
                                    itemBuilder: (context, index) {
                                      return GenderItemWidget(
                                        item: items[index],
                                        onSelectionChanged: (isSelected) {
                                          _handleGenderSelectionChanged(
                                              items[index].id, isSelected);
                                        },
                                      );
                                    },
                                  ),
                                  const SizedBox(
                                    height: 20,
                                  ),
                                  CustomTextField2(
                                    controller: heightController,
                                    labelText: "Height",
                                    hintText: "Height",
                                    keyboardType: TextInputType.number,
                                    suffixIcon: const SizedBox(
                                      width: 30,
                                      child: Center(
                                        child: Text("cm",
                                            style: TextStyle(
                                              fontFamily:
                                                  AppConstants.fontStyleName,
                                              color: Color(0xff303030),
                                              fontSize: 16,
                                              fontWeight: FontWeight.w400,
                                              // Additional InputDecoration properties...
                                            )),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 20,
                                  ),
                                  const Text(
                                    "Pronouns",
                                    style: TextStyle(
                                      fontFamily: AppConstants.fontStyleName,
                                      color: Color(0xff6A6A6A),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                      // Additional InputDecoration properties...
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 7,
                                  ),
                                  Wrap(
                                    runSpacing: 8,
                                    spacing: 8,
                                    children: List.generate(pronounsList.length,
                                        (index) {
                                      return ClipItemWidget(
                                        item: pronounsList[index],
                                        onSelectionChanged: (isSelected) {
                                          setState(() {
                                            if (pronounsCount < 3 &&
                                                isSelected) {
                                              pronounsList[index].isSelected =
                                                  isSelected;
                                              pronounsCount++;
                                            } else if (!isSelected) {
                                              pronounsCount--;
                                              pronounsList[index].isSelected =
                                                  isSelected;
                                            } else {
                                              EasyLoading.showToast(
                                                  "You can only select 3 pronouns.");
                                            }
                                          });
                                        },
                                      );
                                    }),
                                  ),
                                  const SizedBox(
                                    height: 20,
                                  ),
                                  CustomTextField2(
                                    controller: bioController,
                                    labelText: 'Bio *',
                                    hintText: 'Bio',
                                    validator: Validators.validateBio,
                                    minLines: 3,
                                    maxLength: 250,
                                  ),
                                  const SizedBox(
                                    height: 20,
                                  ),
                                  const Text(
                                    "Interests",
                                    style: TextStyle(
                                      fontFamily: AppConstants.fontStyleName,
                                      color: Color(0xff6A6A6A),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                      // Additional InputDecoration properties...
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 7,
                                  ),
                                  Wrap(
                                    runSpacing: 8,
                                    spacing: 8,
                                    children: List.generate(
                                        interestsList.length, (index) {
                                      return ClipItemWidget(
                                        item: interestsList[index],
                                        onSelectionChanged: (isSelected) {
                                          setState(() {
                                            if (interestsCount < 3 &&
                                                isSelected) {
                                              interestsList[index].isSelected =
                                                  isSelected;
                                              interestsCount++;
                                            } else if (!isSelected) {
                                              interestsCount--;
                                              interestsList[index].isSelected =
                                                  isSelected;
                                            } else {
                                              EasyLoading.showToast(
                                                  "You can only select 3 interests.");
                                            }
                                          });
                                        },
                                      );
                                    }),
                                  ),
                                  const SizedBox(
                                    height: 38,
                                  ),
                                  CustomTextField2(
                                    controller: interestsController,
                                    labelText: 'Others',
                                    hintText: 'Others',
                                    suffixIcon: IconButton(
                                      onPressed: () {
                                        if (interestsController.text
                                            .trim()
                                            .isEmpty) {
                                          EasyLoading.showToast(
                                              "Please enter an interest",
                                              toastPosition:
                                                  EasyLoadingToastPosition.top);
                                          return;
                                        }

                                        if (interestsList.any((interest) =>
                                            interest.name
                                                .toLowerCase()
                                                .trim() ==
                                            interestsController.text
                                                .toLowerCase()
                                                .trim())) {
                                          EasyLoading.showToast(
                                              "Interest already added",
                                              toastPosition:
                                                  EasyLoadingToastPosition.top);
                                          return;
                                        }

                                        setState(() {
                                          interestsList.add(CustomCipsModel(
                                              id: (interestsList.length + 1),
                                              name: interestsController.text));
                                          interestsController.clear();
                                        });
                                      },
                                      icon: const Icon(
                                        Icons.add_circle_outline,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(
                                height: 24,
                              ),
                            ],
                          ),
                        ),
                        const Divider(
                          color: Color(0xFFD1D1D1),
                          height: 1,
                        ),
                        const SizedBox(
                          height: 24,
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomDropdownNew(
                                items: listEducation,
                                selectedValue: selectedEducation,
                                onChanged: (newValue) {
                                  // Update the selected value
                                  setState(() {
                                    selectedEducation = newValue;
                                  });
                                },
                                labelText: 'Education',
                                hintText: 'Select',
                              ),
                              const SizedBox(
                                height: 24,
                              ),
                              CustomTextField2(
                                controller: workController,
                                labelText: 'Work',
                                hintText: 'Type here...',
                              ),
                              const SizedBox(
                                height: 24,
                              ),
                              CustomDropdownNew(
                                items: listKids,
                                selectedValue: selectedKids,
                                onChanged: (newValue) {
                                  // Update the selected value
                                  setState(() {
                                    selectedKids = newValue;
                                  });
                                },
                                labelText: 'Kids',
                                hintText: 'Select',
                              ),
                              const SizedBox(
                                height: 24,
                              ),
                              CustomDropdownNew(
                                items: listEthnicity,
                                selectedValue: selectedEthnicity,
                                onChanged: (newValue) {
                                  // Update the selected value
                                  setState(() {
                                    selectedEthnicity = newValue;
                                  });
                                },
                                labelText: 'Ethnicity',
                                hintText: 'Select',
                              ),
                              const SizedBox(
                                height: 24,
                              ),
                              CustomDropdownNew(
                                items: listZodiacSign,
                                selectedValue: selectedZodiacSign,
                                onChanged: (newValue) {
                                  // Update the selected value
                                  setState(() {
                                    selectedZodiacSign = newValue;
                                  });
                                },
                                labelText: 'Zodiac Sign',
                                hintText: 'Select',
                              ),
                              const SizedBox(
                                height: 24,
                              ),
                              CustomDropdownNew(
                                items: listCannabis,
                                selectedValue: selectedCannabis,
                                onChanged: (newValue) {
                                  // Update the selected value
                                  setState(() {
                                    selectedCannabis = newValue;
                                  });
                                },
                                labelText: 'Cannabis',
                                hintText: 'Select',
                              ),
                              const SizedBox(
                                height: 24,
                              ),
                              CustomDropdownNew(
                                items: listExercise,
                                selectedValue: selectedExercise,
                                onChanged: (newValue) {
                                  // Update the selected value
                                  setState(() {
                                    selectedExercise = newValue;
                                  });
                                },
                                labelText: 'Exercise',
                                hintText: 'Select',
                              ),
                              const SizedBox(
                                height: 24,
                              ),
                              const Text(
                                "Smoking",
                                style: TextStyle(
                                  fontFamily: AppConstants.fontStyleName,
                                  color: Color(0xff6A6A6A),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  // Additional InputDecoration properties...
                                ),
                              ),
                              const SizedBox(
                                height: 7,
                              ),
                              GridView.builder(
                                padding: EdgeInsets.zero,
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                gridDelegate:
                                    const SliverGridDelegateWithFixedCrossAxisCount(
                                        crossAxisCount: 2,
                                        mainAxisSpacing: 19,
                                        childAspectRatio: 3.0,
                                        crossAxisSpacing: 7),
                                itemCount: smokingList.length,
                                itemBuilder: (context, index) {
                                  return RadioButtonItemWidget(
                                    item: smokingList[index],
                                    onSelectionChanged: (isSelected) {
                                      _handleSmokingSelectionChanged(
                                          smokingList[index].id, isSelected);
                                    },
                                  );
                                },
                              ),
                              const SizedBox(
                                height: 24,
                              ),
                              const Text(
                                "Drinking",
                                style: TextStyle(
                                  fontFamily: AppConstants.fontStyleName,
                                  color: Color(0xff6A6A6A),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  // Additional InputDecoration properties...
                                ),
                              ),
                              const SizedBox(
                                height: 7,
                              ),
                              GridView.builder(
                                padding: EdgeInsets.zero,
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                gridDelegate:
                                    const SliverGridDelegateWithFixedCrossAxisCount(
                                        crossAxisCount: 2,
                                        mainAxisSpacing: 19,
                                        childAspectRatio: 3.0,
                                        crossAxisSpacing: 7),
                                itemCount: drinkingList.length,
                                itemBuilder: (context, index) {
                                  return RadioButtonItemWidget(
                                    item: drinkingList[index],
                                    onSelectionChanged: (isSelected) {
                                      _handleDrinkSelectionChanged(
                                          drinkingList[index].id, isSelected);
                                    },
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          height: 24,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

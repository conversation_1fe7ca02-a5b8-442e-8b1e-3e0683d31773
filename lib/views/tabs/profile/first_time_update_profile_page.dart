import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:nsfw_detector_flutter/nsfw_detector_flutter.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';

import 'package:fringle_app/config/config.dart';
import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/helpers/media_picker_helper.dart';
import 'package:fringle_app/models/user_account_settings_model.dart';
import 'package:fringle_app/models/user_profile_model_new.dart';
import 'package:fringle_app/providers/auth_providers.dart';
import 'package:fringle_app/providers/user_profile_provider.dart';
import 'package:fringle_app/views/custom/app_toast.dart';
import 'package:fringle_app/views/custom/custom_button.dart';
import 'package:fringle_app/views/others/set_user_location_page.dart';
import 'package:fringle_app/views/tabs/profile/interests_detail_page.dart';
import 'package:fringle_app/views/tabs/profile/other_details_page.dart';

import '../../custom/custom_app_loader.dart';
import 'basic_profile_details_page.dart';
import 'upload_photos_page.dart';

class FirstTimeUserProfilePage extends ConsumerStatefulWidget {
  final Function(bool isFirstTime)? onTap;
  const FirstTimeUserProfilePage({super.key, this.onTap});

  @override
  ConsumerState<FirstTimeUserProfilePage> createState() =>
      _FirstTimeUserProfilePageState();
}

class _FirstTimeUserProfilePageState
    extends ConsumerState<FirstTimeUserProfilePage> {
  final _pageController = PageController();
  int _currentPage = 0;
  List<String> imageList = [];
  final _formKey = GlobalKey<FormState>();
  var userDetailsModel =
      UserProfileModelNew(id: "", userId: "", isVerified: false);

  getUserData() {
    final user = ref.read(userProfileFutureProvider);
    return user.when(
        data: (data) {
          if (data != null) {
            setState(() {
              userDetailsModel.firstName = data.firstName;
              userDetailsModel.lastName = data.lastName;
              userDetailsModel.email = data.email;
              userDetailsModel.profilePicture = data.profilePicture;
              userDetailsModel.phoneNumber = data.phoneNumber;
              userDetailsModel.gender = data.gender;
              userDetailsModel.about = data.about;
              userDetailsModel.birthDay = data.birthDay;
              userDetailsModel.mediaFiles = data.mediaFiles;
              userDetailsModel.interests = data.interests;
              userDetailsModel.userAccountSettingsModel =
                  data.userAccountSettingsModel;
              userDetailsModel.isVerified = data.isVerified;
              userDetailsModel.isOnline = data.isOnline;
              userDetailsModel.isNewUser = data.isNewUser;
              userDetailsModel.isPremiumUser = data.isPremiumUser;
              userDetailsModel.education = data.education;
              userDetailsModel.religion = data.religion;
              userDetailsModel.cannabis = data.cannabis;
              userDetailsModel.pronounceList = data.pronounceList;
              userDetailsModel.imageList = data.imageList;
              userDetailsModel.bio = data.bio;
              userDetailsModel.work = data.work;
              userDetailsModel.address = data.address;
              userDetailsModel.latitude = data.latitude;
              userDetailsModel.longitude = data.longitude;
              userDetailsModel.interests = data.interests;
              userDetailsModel.mediaFiles = data.mediaFiles;
              userDetailsModel.profilePicture = data.profilePicture;
              imageList = data.imageList ?? [];
            });
          }
        },
        error: (_, __) => const SizedBox(),
        loading: () => const SizedBox());
  }

  @override
  void initState() {
    getUserData();

    _pageController.addListener(() {
      setState(() {
        _currentPage = _pageController.page!.round();
      });
    });
    super.initState();
  }

  List<String> profileUrl = [];
  void _onSubmit() async {
    // for (var media in imageList) {
    //   if (Uri.parse(media).isAbsolute) {
    //     profileUrl.add(media);
    //   } else if (media == "") {
    //     setState(() {});
    //   } else {
    //     final mediaURL = await _uploadUserMediaFiles(
    //       media,
    //       ref.watch(currentUserStateProvider)!.uid,
    //     );
    //     if (mediaURL != null) {
    //       profileUrl.add(mediaURL);
    //       setState(() {});
    //     }
    //   }
    // }
    final userId = ref.watch(currentUserStateProvider)!.uid;
    await ref.read(userProfileNotifier).createProfile(userId, {
      "isProfileCompleted": true,
    });
    userDetailsModel.userId = userId;
    userDetailsModel.id = userId;
    userDetailsModel.email = ref.watch(currentUserStateProvider)?.user?.email;
    // userDetailsModel.phoneNumber = ref.watch(currentUserStateProvider)?.user?.phoneNumber;
    userDetailsModel.imageList = profileUrl;
    userDetailsModel.profilePicture = profileUrl.isNotEmpty ? profileUrl[0] : null;
    userDetailsModel.mediaFiles = [];
    final result = await ref.read(userProfileNotifier).createUserProfile(userDetailsModel);
    if (result) {
      ref.invalidate(isUserAddedProvider);
      ref.invalidate(userProfileFutureProvider);
      if (widget.onTap != null) {
        widget.onTap!(true);
      }
      // FBEvents.logEvent("Profile creation");
      EasyLoading.dismiss();
      final userCollection = FirebaseFirestore.instance
          .collection(FirebaseConstants.coupleAccount);
      userCollection
          .where("email", isEqualTo: userDetailsModel.email)
          .get()
          .then((data) async {
        if (data.docs.isNotEmpty) {
          await userCollection.doc(data.docs.first.data()['coupleId']).delete();
        }
      });
    } else {
      EasyLoading.dismiss();
    }
  }

  onContinue() {
    if (imageList.length < 2) {
      CustomToast.showToast(
        message: "You need to add at-least 2 images to create an account",
      );
    } else {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  void selecImage() async {
    final imagePath = await pickMedia();
    if (imagePath != null) {
      final file = File(imagePath);
      final bytes = await file.length();
      final mb = bytes / (1024 * 1024);
      if (mb > 10) {
        CustomToast.showToast(message: "Images larger than 10MB are not allowed");
        return;
      }
      NsfwDetector detector = await NsfwDetector.load();
      NsfwResult? result = await detector.detectNSFWFromFile(File(imagePath));
      final hasNudity = result?.isNsfw ?? false;
      if (hasNudity) {
        EasyLoading.showError(
            "Sorry this photo not uploaded because its contains nudity!");
      } else {
        setState(() {
          imageList.add(imagePath);
        });
      }
    }
  }

  Future<String?> _uploadUserMediaFiles(String path, String userId) async {
    final storageRef = FirebaseStorage.instance
        .ref()
        .child(FirebaseConstants.userMediaFiles)
        .child(userId)
        .child(path.split('/').last);

    final uploadTask = storageRef.putFile(File(path));

    String? imageUrl;
    await uploadTask.whenComplete(() async {
      imageUrl = await storageRef.getDownloadURL();
    });
    return imageUrl;
  }

  Widget _buildDots() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List<Widget>.generate(
        4, // Replace with the actual number of pages
        (int index) {
          return Container(
            width: 10,
            height: 10,
            margin: const EdgeInsets.symmetric(horizontal: 5),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: _currentPage == index ? null : const Color(0xFFD9D9D9),
              gradient: _currentPage == index
                  ? LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: [
                        AppConstants.primaryLightColor,
                        AppConstants.primaryColor,
                      ],
                    )
                  : null,
            ),
          );
        },
      ),
    );
  }

  String getTitleFromCondition(int page) {
    if (page == 0) {
      return 'Upload photos';
    } else if (page == 1) {
      return 'Let’s get started';
    } else if (page == 2) {
      return 'What are you into?';
    } else {
      return 'Other Details';
    }
  }

  String getTextFromCondition(int page) {
    if (page == 0) {
      return 'Please upload your photos.\nMinimum 2 and maximum 6 (Portrait Only)';
    } else if (page == 1) {
      return 'Share some basic info about yourself';
    } else if (page == 2) {
      return 'Select at least 3 to help us find your vibe';
    } else {
      return 'Please fill in your other details to continue.';
    }
  }

  Future<bool> _onWillPop() async {
    if (_pageController.page!.toInt() > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeOut,
      );
      return false;
    } else {
      showCloseDialog();
    }
    return true;
  }

  void showCloseDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("Are you sure you want to cancel?"),
        content: const Text("You will be logged out."),
        actions: [
          TextButton(
            child: const Text(
              "Cancel",
              style: TextStyle(color: Colors.black),
            ),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
          TextButton(
            child: const Text(
              "Sure",
              style: TextStyle(color: Colors.red),
            ),
            onPressed: () async {
              Navigator.of(context).pop();

              await ref.read(authProvider).signOut();
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Form(
        key: _formKey,
        child: WillPopScope(
          onWillPop: _onWillPop,
          child: Scaffold(
            backgroundColor: Colors.white,
            appBar: AppBar(
              title: SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Text("Step ${_currentPage + 1}/4",
                    textAlign: TextAlign.end,
                    style: const TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 12,
                        color: Color(0xFF717171))),
              ),
              backgroundColor: Colors.transparent,
              systemOverlayStyle: SystemUiOverlayStyle.dark,
              foregroundColor: Colors.black,
              leading: IconButton(
                icon: SvgPicture.asset(
                  AppConstants.backIcon,
                  color: Colors.black,
                  height: 25,
                  width: 25,
                ),
                onPressed: () {
                  if (_pageController.page == 0) {
                    showCloseDialog();
                  } else {
                    _pageController.previousPage(
                      duration: const Duration(milliseconds: 500),
                      curve: Curves.easeInOut,
                    );
                  }
                },
              ),
            ),
            body: Column(
              //crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDots(),
                const SizedBox(
                  height: 32,
                ),
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: GradientText(getTitleFromCondition(_currentPage),
                      textAlign: TextAlign.center,
                      colors: [
                        AppConstants.primaryLightColor,
                        AppConstants.primaryColor,
                      ],
                      style: const TextStyle(
                        fontFamily: AppConstants.fontStyleName,
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                      )),
                ),
                const SizedBox(
                  height: 8,
                ),
                Container(
                  padding: const EdgeInsets.only(left: 32, right: 32),
                  child: Text(
                    getTextFromCondition(_currentPage),
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF717171),
                        fontSize: 12),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Expanded(
                  child: PageView(
                    controller: _pageController,
                    physics: const NeverScrollableScrollPhysics(),
                    children: [
                      UploadPhotosPage(
                          imageList: imageList,
                          onAddImage: () {
                            selecImage();
                          },
                          onRemoveImage: (index) {
                            if (imageList.isNotEmpty) {
                              setState(() {
                                imageList.removeAt(index);
                              });
                            }
                          },
                          onContinue: onContinue),
                      BasicProfileDetailsPage(
                          userDetailsModel: userDetailsModel,
                          onSelected: (v) {
                            if (_formKey.currentState!.validate()) {
                              setState(() {
                                userDetailsModel = v;
                              });
                              _pageController.nextPage(
                                duration: const Duration(milliseconds: 500),
                                curve: Curves.easeInOut,
                              );
                            }
                          }),
                      InterestsDetailPage(
                          userProfileModelNew: userDetailsModel,
                          onInterestsSelected: (v) {
                            setState(() {
                              userDetailsModel = v;
                            });
                            _pageController.nextPage(
                              duration: const Duration(milliseconds: 500),
                              curve: Curves.easeInOut,
                            );
                          }),
                      SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 24),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  OtherDetailsScreen(
                                    userProfileModelNew: userDetailsModel,
                                    onSelected: (v) {
                                      if (_formKey.currentState!.validate()) {
                                        CustomAppLoader.showCustomLoader("Uploading...");
                                        setState(() {
                                          userDetailsModel = v;
                                        });
                                        _onSubmit();
                                      }
                                    },
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: AppConstants.defaultNumericValue * 2),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class UserDetailsTakingScreen extends StatelessWidget {
  final TextEditingController nameController;
  final Function(String gender) onGenderSelected;
  final String? gender;
  final Function(DateTime birthday) onBirthdaySelected;
  final TextEditingController birthdayController;
  final DateTime? birthday;
  final List<String> selectedInterests;
  final Function(bool, String) onSelectInterest;
  final VoidCallback onNext;
  const UserDetailsTakingScreen({
    super.key,
    required this.nameController,
    required this.onGenderSelected,
    this.gender,
    required this.onBirthdaySelected,
    required this.birthdayController,
    this.birthday,
    required this.selectedInterests,
    required this.onSelectInterest,
    required this.onNext,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        TextFormField(
          controller: nameController,
          autofocus: true,
          validator: (value) {
            if (value!.isEmpty) {
              return 'Please enter your name';
            }
            return null;
          },
          decoration: const InputDecoration(
            label: Text('Name'),
          ),
        ),
        const SizedBox(height: AppConstants.defaultNumericValue),
        Text(
          "Please enter your full name. You ${AppConfig.canChangeName ? "can" : "cannot"} change it later.",
          style: Theme.of(context)
              .textTheme
              .bodySmall!
              .copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppConstants.defaultNumericValue * 2),
        Text(
          "I am",
          style: Theme.of(context)
              .textTheme
              .headlineSmall!
              .copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppConstants.defaultNumericValue),
        Wrap(
          alignment: WrapAlignment.center,
          children: [
            GestureDetector(
              onTap: () {
                onGenderSelected(AppConfig.maleText);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: AppConstants.defaultNumericValue / 1.5,
                  horizontal: AppConstants.defaultNumericValue,
                ),
                decoration: BoxDecoration(
                  color: gender == AppConfig.maleText
                      ? AppConstants.primaryColor.withOpacity(0.4)
                      : null,
                  border:
                      Border.all(color: AppConstants.primaryColor, width: 2),
                  borderRadius: BorderRadius.circular(
                      AppConstants.defaultNumericValue * 2),
                ),
                child: Text(
                  AppConfig.maleText.toUpperCase(),
                  textAlign: TextAlign.center,
                  style: Theme.of(context)
                      .textTheme
                      .bodyLarge!
                      .copyWith(fontWeight: FontWeight.bold),
                ),
              ),
            ),
            const SizedBox(width: AppConstants.defaultNumericValue),
            GestureDetector(
              onTap: () {
                onGenderSelected(AppConfig.femaleText);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: AppConstants.defaultNumericValue / 1.5,
                  horizontal: AppConstants.defaultNumericValue,
                ),
                decoration: BoxDecoration(
                  color: gender == AppConfig.femaleText
                      ? AppConstants.primaryColor.withOpacity(0.4)
                      : null,
                  border:
                      Border.all(color: AppConstants.primaryColor, width: 2),
                  borderRadius: BorderRadius.circular(
                      AppConstants.defaultNumericValue * 2),
                ),
                child: Text(
                  AppConfig.femaleText.toUpperCase(),
                  textAlign: TextAlign.center,
                  style: Theme.of(context)
                      .textTheme
                      .bodyLarge!
                      .copyWith(fontWeight: FontWeight.bold),
                ),
              ),
            ),
            if (AppConfig.allowTransGender)
              const SizedBox(width: AppConstants.defaultNumericValue),
            if (AppConfig.allowTransGender)
              GestureDetector(
                onTap: () {
                  onGenderSelected(AppConfig.transText);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: AppConstants.defaultNumericValue / 1.5,
                    horizontal: AppConstants.defaultNumericValue,
                  ),
                  decoration: BoxDecoration(
                    color: gender == AppConfig.transText
                        ? AppConstants.primaryColor.withOpacity(0.4)
                        : null,
                    border:
                        Border.all(color: AppConstants.primaryColor, width: 2),
                    borderRadius: BorderRadius.circular(
                        AppConstants.defaultNumericValue * 2),
                  ),
                  child: Text(
                    AppConfig.transText.toUpperCase(),
                    textAlign: TextAlign.center,
                    style: Theme.of(context)
                        .textTheme
                        .bodyLarge!
                        .copyWith(fontWeight: FontWeight.bold),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: AppConstants.defaultNumericValue),
        Text(
          "Select your gender to get noticed!",
          style: Theme.of(context)
              .textTheme
              .bodySmall!
              .copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppConstants.defaultNumericValue * 2),
        Text(
          "My birthday is",
          style: Theme.of(context)
              .textTheme
              .headlineSmall!
              .copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppConstants.defaultNumericValue),
        TextFormField(
          controller: birthdayController,
          autofocus: true,
          readOnly: true,
          textAlign: TextAlign.center,
          style: Theme.of(context)
              .textTheme
              .bodyLarge!
              .copyWith(fontWeight: FontWeight.bold),
          decoration: const InputDecoration(
            hintText: "MM/DD/YYYY",
            // border: InputBorder.none,
          ),
          validator: (value) {
            if (value!.isEmpty) {
              return 'Please Select Your Birthday';
            }
            return null;
          },
          onTap: () {
            const duration = Duration(days: 365 * AppConfig.minimumAgeRequired);
            showDatePicker(
                    context: context,
                    firstDate: DateTime(1900),
                    lastDate: DateTime.now().subtract(duration),
                    initialDate: birthday ?? DateTime.now().subtract(duration))
                .then((value) {
              if (value != null) {
                onBirthdaySelected(value);
                birthdayController.text =
                    DateFormat("MM/dd/yyyy").format(value);
              }
            });
          },
        ),
        const SizedBox(height: AppConstants.defaultNumericValue),
        Text(
          "You must be ${AppConfig.minimumAgeRequired} years old to use this app!\nYour age will be shown to other users.",
          style: Theme.of(context)
              .textTheme
              .bodySmall!
              .copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppConstants.defaultNumericValue * 2),
        Text(
          "My Interests",
          style: Theme.of(context)
              .textTheme
              .headlineSmall!
              .copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppConstants.defaultNumericValue),
        Wrap(
          spacing: AppConstants.defaultNumericValue / 2,
          children: AppConfig.interests
              .map(
                (interest) => ChoiceChip(
                  label:
                      Text(interest[0].toUpperCase() + interest.substring(1)),
                  selected: selectedInterests.contains(interest),
                  shape: selectedInterests.contains(interest)
                      ? RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                              AppConstants.defaultNumericValue * 2),
                          side: BorderSide(
                              color: AppConstants.primaryColor, width: 1),
                        )
                      : null,
                  selectedColor: AppConstants.primaryColor.withOpacity(0.3),
                  onSelected: (notSelected) {
                    onSelectInterest(notSelected, interest);
                  },
                ),
              )
              .toList(),
        ),
        const SizedBox(height: AppConstants.defaultNumericValue),
        Text(
          "Please select your interests to get noticed!\nYou can select ${AppConfig.maxNumOfInterests} interests at most.",
          style: Theme.of(context)
              .textTheme
              .bodySmall!
              .copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: AppConstants.defaultNumericValue * 2),
        CustomButton(onPressed: onNext, text: "Continue"),
        const SizedBox(height: AppConstants.defaultNumericValue * 2),
      ],
    );
  }
}

class UserLocationScreen extends StatelessWidget {
  final Function(UserLocation location) onLocationChanged;
  final UserLocation? location;
  final VoidCallback onNext;
  final VoidCallback onBack;
  const UserLocationScreen({
    super.key,
    required this.onLocationChanged,
    this.location,
    required this.onNext,
    required this.onBack,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultNumericValue * 2),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            "My location is",
            style: Theme.of(context)
                .textTheme
                .headlineSmall!
                .copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.defaultNumericValue),
          // const SizedBox(
          //   height: 300,
          //   child: Center(
          //       child: Text(
          //     "Not Yet Implemented!\nYou can move on!",
          //     textAlign: TextAlign.center,
          //   )),
          // ),
          GestureDetector(
            onTap: () async {
              final location = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SetUserLocation(),
                  fullscreenDialog: true,
                ),
              );

              if (location != null) {
                onLocationChanged(location);
              }
            },
            child: Container(
              padding: const EdgeInsets.all(AppConstants.defaultNumericValue),
              decoration: BoxDecoration(
                border: Border.all(
                  color: Theme.of(context).primaryColor,
                ),
                borderRadius:
                    BorderRadius.circular(AppConstants.defaultNumericValue),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.location_on,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: AppConstants.defaultNumericValue),
                  Expanded(
                    child: Text(
                      location?.addressText ?? "Tap to set location",
                      style: Theme.of(context)
                          .textTheme
                          .bodyLarge!
                          .copyWith(fontWeight: FontWeight.bold),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: AppConstants.defaultNumericValue),
          Text(
            "You must set your location to use this app!\nOther users need to know the distance between you and them to use the app.",
            style: Theme.of(context)
                .textTheme
                .bodySmall!
                .copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppConstants.defaultNumericValue * 2),
          Row(
            children: [
              Expanded(
                  child: CustomButton(
                      onPressed: onBack, text: "Back".toUpperCase())),
              const SizedBox(width: AppConstants.defaultNumericValue),
              Expanded(
                  child: CustomButton(onPressed: onNext, text: "Continue")),
            ],
          ),
          const SizedBox(height: AppConstants.defaultNumericValue * 2),
        ],
      ),
    );
  }
}

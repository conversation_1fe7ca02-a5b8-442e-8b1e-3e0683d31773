// import 'package:flutter/cupertino.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:fringle_app/config/config.dart';
// import 'package:purchases_flutter/purchases_flutter.dart';

// final isPremiumUserProvider = FutureProvider<bool>((ref) async {
//   final CustomerInfo customerInfo = await Purchases.getCustomerInfo();

//   if (customerInfo.entitlements.all[SubscriptionConstants.entitlementId] !=
//           null &&
//       customerInfo.entitlements.all[SubscriptionConstants.entitlementId]!
//               .isActive ==
//           true) {
//     return true;
//   } else {
//     return false;
//   }
// });
// final isPremiumPlusUserProvider = FutureProvider<bool>((ref) async {
//   final CustomerInfo customerInfo = await Purchases.getCustomerInfo();

//   if (customerInfo.entitlements
//               .all[SubscriptionConstants.entitlementIdPremiumPlus] !=
//           null &&
//       customerInfo.entitlements
//               .all[SubscriptionConstants.entitlementIdPremiumPlus]!.isActive ==
//           true) {
//     return true;
//   } else {
//     return false;
//   }
// });


// final premiumCustomerInfoProvider = FutureProvider<CustomerInfo>((ref) async {
//   final CustomerInfo customerInfo = await Purchases.getCustomerInfo();

//   return customerInfo;
// });

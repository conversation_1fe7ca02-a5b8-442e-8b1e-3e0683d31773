<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_2362_588)">
<rect width="24" height="24" rx="12" fill="#31D04C"/>
<g clip-path="url(#clip0_2362_588)">
<path d="M9.33333 9.99999H14.6667M9.33333 12.6667H13.3333M16 6.66666C16.5304 6.66666 17.0391 6.87737 17.4142 7.25244C17.7893 7.62752 18 8.13622 18 8.66666V14C18 14.5304 17.7893 15.0391 17.4142 15.4142C17.0391 15.7893 16.5304 16 16 16H12.6667L9.33333 18V16H8C7.46957 16 6.96086 15.7893 6.58579 15.4142C6.21071 15.0391 6 14.5304 6 14V8.66666C6 8.13622 6.21071 7.62752 6.58579 7.25244C6.96086 6.87737 7.46957 6.66666 8 6.66666H16Z" stroke="white" stroke-width="1.15" stroke-linecap="round" stroke-linejoin="round"/>
<circle cx="17" cy="16" r="2.75" fill="white" stroke="#31D04C" stroke-width="1.5"/>
</g>
</g>
<defs>
<filter id="filter0_b_2362_588" x="-24" y="-24" width="72" height="72" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_2362_588"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_2362_588" result="shape"/>
</filter>
<clipPath id="clip0_2362_588">
<rect width="16" height="16" fill="white" transform="translate(4 4)"/>
</clipPath>
</defs>
</svg>

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';

import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/providers/auth_providers.dart';
import 'package:fringle_app/views/auth/signup_screen.dart';
import 'package:fringle_app/views/custom/custom_button.dart';
import 'package:fringle_app/views/tabs/bottom_nav_bar_page.dart';

import '../../helpers/validators.dart';
import '../custom/app_toast.dart';
import '../custom/custom_app_loader.dart';
import '../custom/custom_textfield.dart';

class LoginPage extends ConsumerStatefulWidget {
  const LoginPage({super.key});

  @override
  ConsumerState<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends ConsumerState<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool isCoupleAccount = false;
  bool isObscureText = true;

  void login() async {
    if (_formKey.currentState!.validate()) {
      final email = _emailController.text;
      final password = _passwordController.text;
      CustomAppLoader.showCustomLoader("Logging in...");
      await ref
          .read(authProvider)
          .signInWithEmailAndPassword(email, password)
          .then((value) {
          CustomAppLoader.dismissCustomLoader();
        if (value != null) {
          CustomToast.showToast(message: "Login Successful");
          Navigator.pushAndRemoveUntil(
              context,
              MaterialPageRoute(builder: (_) => BottomNavBarPage(
                isPremiumPlusUser: true,
                isPremiumUser: true,
                userId: value.uid,
              )),
              (route) => false);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        clipBehavior: Clip.none,
        alignment: Alignment.bottomCenter,
        fit: StackFit.loose,
        children: [
          SvgPicture.asset(
            AppConstants.logoBgImage,
          ),
          Scaffold(
            backgroundColor: Colors.transparent,
            body: SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 120,
                      ),
                      SvgPicture.asset(
                        AppConstants.logo,
                      ),
                      const SizedBox(
                        height: 67,
                      ),
                      CustomTextField(
                        controller: _emailController,
                        labelText: 'Email',
                        hintText: 'Enter email',
                        validator: Validators.validateEmail,
                        keyboardType: TextInputType.emailAddress,
                        inputFormatters: [
                          FilteringTextInputFormatter.deny(RegExp(r'\s')),
                        ],
                      ),
                      const SizedBox(height: 24),
                      CustomTextField(
                        suffixIcon: InkWell(
                          onTap: () {
                            isObscureText = !isObscureText;
                            setState(() {});
                          },
                          child: isObscureText
                              ? const Icon(
                                  Icons.visibility_off,
                                  color: Color(0xFFC0C0C0),
                                )
                              : const Icon(Icons.visibility,
                                  color: Color(0xFFC0C0C0)),
                        ),
                        controller: _passwordController,
                        labelText: 'Password',
                        hintText: 'Enter password',
                        obscureText: isObscureText,
                        validator: Validators.validatePassword,
                        onTap: () {},
                      ),
                      const SizedBox(
                        height: 12,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          InkWell(
                              onTap: () {
                                // Navigator.of(context)
                                //     .push(MaterialPageRoute(
                                //                     builder: (context) {
                                //   return PasswordResetScreen();
                                // }));
                              },
                              child: Text(
                                "Forgot password?",
                                style: TextStyle(
                                    color: AppConstants.primaryTextColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500),
                              ))
                        ],
                      ),
                      Spacer(),
                      CustomButton(
                        onPressed: () {
                          login();
                        },
                        text: "Login",
                      ),
                      const SizedBox(
                        height: 24,
                      ),
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                                text: "Don't have an account? ",
                                style: TextStyle(
                                  color: AppConstants.primaryTextColor,
                                  fontSize: 14,
                                )),
                            TextSpan(
                                text: "Sign up",
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            const SignupScreen(),
                                      ),
                                    );
                                  },
                                style: TextStyle(
                                  color: AppConstants.primaryTextColor,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                )),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 48,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

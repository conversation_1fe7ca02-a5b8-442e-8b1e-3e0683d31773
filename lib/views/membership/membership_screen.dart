// import 'dart:io';

// import 'package:fringle_app/helpers/constants.dart';
// import 'package:fringle_app/helpers/fb_events.dart';
// import 'package:fringle_app/providers/interaction_provider.dart';
// import 'package:fringle_app/providers/subscriptions/is_subscribed_provider.dart';
// import 'package:fringle_app/providers/subscriptions/offerings_provider.dart';
// import 'package:fringle_app/views/membership/free_membership_screen.dart';
// import 'package:fringle_app/views/membership/premium_plus_membership.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_easyloading/flutter_easyloading.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:purchases_flutter/purchases_flutter.dart';
// import 'package:simple_gradient_text/simple_gradient_text.dart';

// import '../../providers/user_profile_provider.dart';
// import '../custom/custom_app_loader.dart';
// import '../custom/new_custom_app_button.dart';
// import 'premium_membership_screen.dart';

// class MembershipScreen extends ConsumerStatefulWidget {
//   final bool isFirstTime;
//   final bool fromBottomBar;
//   final bool isPremiumUser;
//   final bool isPremiumPlusUser;

//   const MembershipScreen(
//       {super.key,
//       Key? key,
//       this.isFirstTime = false,
//       this.fromBottomBar = false,
//       required this.isPremiumUser,
//       required this.isPremiumPlusUser});

//   @override
//   ConsumerState<MembershipScreen> createState() => _MembershipScreenState();
// }

// class _MembershipScreenState extends ConsumerState<MembershipScreen>
//     with TickerProviderStateMixin {
//   late TabController _tabController;
//   int selectedIndex = 0;
//   var planType;

//   @override
//   void initState() {
//     super.initState();
//     _tabController = TabController(length: 3, vsync: this);
//     _tabController.addListener(_handleTabSelection);
//   }

//   void _handleTabSelection() {
//     setState(() {
//       selectedIndex = _tabController.index;
//     });
//   }

//   void updatePuchase(bool isPurchase, int selectedOption) {
//     if (isPurchase) {
//       final user = ref.read(userProfileFutureProvider);
//       return user.when(
//           data: (data) async {
//             if (data != null) {
//               if (!(widget.isPremiumPlusUser && Platform.isIOS)) {
//                 ref.read(userProfileNotifier).updateUserProfile(
//                     (selectedOption < 3)
//                         ? data.copyWith(
//                             isPremiumUser: true,
//                             isPremiumPlusUser: false,
//                             planType: selectedOption)
//                         : data.copyWith(
//                             isPremiumPlusUser: true,
//                             isPremiumUser: false,
//                             planType: selectedOption));
//               }
//               ref.invalidate(userProfileFutureProvider);
//               EasyLoading.dismiss();
//               FBEvents.logEvent("Subscription");
//               EasyLoading.showInfo("Purchase Successfully");
//               Navigator.of(context).pop();
//               ref.invalidate(isPremiumUserProvider);
//               ref.invalidate(isPremiumPlusUserProvider);
//               ref.invalidate(premiumCustomerInfoProvider);
//               ref.invalidate(userProfileFutureProvider);
//               ref.read(checkboxStateProvider.notifier).state;
//               // Navigator.of(context).pop();
//             }
//           },
//           error: (_, __) => const SizedBox(),
//           loading: () => const SizedBox());
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     final user = ref.read(userProfileFutureProvider);
//     final offeringsRef = ref.watch(subscriptionOfferingsProvider);
//     return DefaultTabController(
//       length: 3,
//       child: Scaffold(
//         appBar: PreferredSize(
//           preferredSize: Size.fromHeight(
//               kToolbarHeight + 70), // Add 20 pixels for the top distance
//           child: AppBar(
//             systemOverlayStyle: const SystemUiOverlayStyle(
//               // Status bar color
//               statusBarColor: Color(0xFFFFFCF5),
//               // Status bar brightness (optional)
//               statusBarIconBrightness:
//                   Brightness.dark, // For Android (dark icons)
//               statusBarBrightness: Brightness.light, // For iOS (dark icons)
//             ),
//             leading: widget.fromBottomBar
//                 ? SizedBox.shrink()
//                 : Padding(
//                     padding: const EdgeInsets.all(8.0),
//                     child: InkWell(
//                       onTap: () {
//                         Navigator.of(context).pop();
//                       },
//                       child: Container(
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.circular(14),
//                             color: const Color(0xffF2F2F2),
//                             gradient: const LinearGradient(
//                               colors: [
//                                 Color(0xFFC70973),
//                                 Color(0xFF46239F),
//                               ],
//                               begin: Alignment.topCenter,
//                               end: Alignment.bottomCenter,
//                             ),
//                           ),
//                           padding: EdgeInsets.all(5),
//                           child: Icon(Icons.close)),
//                     ),
//                   ),
//             backgroundColor: Colors.transparent,
//             elevation: 0,
//             title: const Text(
//               'Membership',
//               style: TextStyle(
//                   fontSize: 18,
//                   fontWeight: FontWeight.w600,
//                   color: Colors.black),
//             ),
//             bottom: TabBar(
//               controller: _tabController,
//               // indicator: null,
//               indicator: const BoxDecoration(
//                 border: Border(
//                     bottom: BorderSide(color: Colors.transparent, width: 0.0)),
//               ),

//               // unselectedLabelColor: Colors.black,
//               // indicatorSize: TabBarIndicatorSize.tab,
//               // indicator: UnderlineTabIndicator(
//               //   borderSide:
//               //       const BorderSide(color: Color(0xFFC70973), width: 4.0),
//               //   borderRadius: BorderRadius.circular(10),
//               // ),
//               // indicatorPadding:
//               //     const EdgeInsets.symmetric(horizontal: 17, vertical: -16.0),
//               labelPadding: EdgeInsets.symmetric(horizontal: 3.5),
//               tabs: [
//                 Tab(
//                   height: 46,
//                   child: Container(
//                     decoration: BoxDecoration(
//                       gradient: LinearGradient(
//                         colors: [
//                           (selectedIndex == 0)
//                               ? Color(0xFFC70973)
//                               : Colors.white,
//                           (selectedIndex == 0)
//                               ? Color(0xFF46239F)
//                               : Colors.white,
//                         ],
//                         begin: Alignment.centerLeft,
//                         end: Alignment.centerRight,
//                       ),
//                       borderRadius: BorderRadius.circular(38),
//                       color: (selectedIndex == 0)
//                           ? const Color(0xFFC70973)
//                           : Colors.white,
//                       border: (selectedIndex == 1 || selectedIndex == 2)
//                           ? Border.all(
//                               color: (const Color(0xFFD9D9D9)), width: 1)
//                           : null,
//                     ),
//                     child: Align(
//                       alignment: Alignment.center,
//                       child: Center(
//                           child: Text(
//                         'Free',
//                         textAlign: TextAlign.center,
//                         style: TextStyle(
//                             fontFamily: AppConstants.fontStyleName,
//                             fontSize: 14,
//                             fontWeight: FontWeight.w600,
//                             color: (selectedIndex == 0)
//                                 ? Colors.white
//                                 : Colors.black),
//                       )),
//                     ),
//                   ),
//                 ),
//                 Tab(
//                   height: 46,
//                   child: Container(
//                     decoration: BoxDecoration(
//                       gradient: LinearGradient(
//                         colors: [
//                           (selectedIndex == 1)
//                               ? const Color(0xFFC70973)
//                               : Colors.white,
//                           (selectedIndex == 1)
//                               ? const Color(0xFF46239F)
//                               : Colors.white,
//                         ],
//                         begin: Alignment.centerLeft,
//                         end: Alignment.centerRight,
//                       ),
//                       color: (selectedIndex == 1)
//                           ? const Color(0xFFC70973)
//                           : Colors.white,
//                       borderRadius: BorderRadius.circular(38),
//                       border: (selectedIndex == 0 || selectedIndex == 2)
//                           ? Border.all(
//                               color: (const Color(0xFFD9D9D9)), width: 1)
//                           : null,
//                     ),
//                     child: Align(
//                       child: Text(
//                         'Premium',
//                         textAlign: TextAlign.center,
//                         style: TextStyle(
//                             fontSize: 14,
//                             fontWeight: FontWeight.w600,
//                             color: (selectedIndex == 1)
//                                 ? Colors.white
//                                 : Colors.black),
//                       ),
//                     ),
//                   ),
//                 ),
//                 Tab(
//                   height: 46,
//                   child: Container(
//                     decoration: BoxDecoration(
//                       gradient: LinearGradient(
//                         colors: [
//                           (selectedIndex == 2)
//                               ? const Color(0xFF421B92)
//                               : Colors.white,
//                           (selectedIndex == 2)
//                               ? const Color(0xFF6332C6)
//                               : Colors.white,
//                         ],
//                         begin: Alignment.topCenter,
//                         end: Alignment.bottomCenter,
//                       ),
//                       color: (selectedIndex == 2)
//                           ? const Color(0xFFC70973)
//                           : Colors.white,
//                       borderRadius: BorderRadius.circular(38),
//                       border: (selectedIndex == 0 || selectedIndex == 1)
//                           ? Border.all(
//                               color: (const Color(0xFFD9D9D9)), width: 1)
//                           : null,
//                     ),
//                     child: Align(
//                       child: Text(
//                         'Premium +',
//                         textAlign: TextAlign.center,
//                         style: TextStyle(
//                             fontSize: 14,
//                             fontWeight: FontWeight.w600,
//                             color: (selectedIndex == 2)
//                                 ? Colors.white
//                                 : Colors.black),
//                       ),
//                     ),
//                   ),
//                 )
//               ],
//             ),
//           ),
//         ),
//         body: GestureDetector(
//           onHorizontalDragEnd: (details) {
//             if (details.primaryVelocity! > 0) {
//               // Swiped from left to right
//               if (selectedIndex == 1) {
//                 // Only switch to the previous tab if the current tab is not the first tab
//                 _tabController.animateTo(0);
//                 return;
//               }
//               if (selectedIndex == 2) {
//                 _tabController.animateTo(1);
//               }
//             } else if (details.primaryVelocity! < 0) {
//               // Swiped from right to left
//               if (selectedIndex == 0) {
//                 // Only switch to the next tab if the current tab is not the last tab
//                 _tabController.animateTo(1);
//                 return;
//               }
//               if (selectedIndex == 1) {
//                 _tabController.animateTo(2);
//               }
//             }
//           },
//           child: TabBarView(
//             controller: _tabController,
//             physics:
//                 const NeverScrollableScrollPhysics(), // Disable swiping between tabs
//             children: [
//               FreeMembership(
//                 isPremiumUser: widget.isPremiumUser,
//                 isPremiumPlusUser: widget.isPremiumPlusUser,
//               ),
//               PremiumMembership(
//                 isPremiumUser: widget.isPremiumUser,
//                 isPremiumPlusUser: widget.isPremiumPlusUser,
//                 isFirstTime: widget.isFirstTime,
//                 isPurchase: (isPurchase, selectedOption) {
//                   updatePuchase(isPurchase, selectedOption);
//                 },
//               ),
//               PremiumPlusMembership(
//                 isPremiumUser: widget.isPremiumUser,
//                 isPremiumPlusUser: widget.isPremiumPlusUser,
//                 isFirstTime: widget.isFirstTime,
//                 isPurchase: (isPurchase, selectedOption) {
//                   updatePuchase(isPurchase, selectedOption);
//                 },
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }

// class MyBottomSheet extends ConsumerStatefulWidget {
//   final bool isFirstTime;

//   final Function(bool isPurchase, int selectedOption) isPurchase;
//   final bool isPremiumUser;
//   final bool isPremiumPlusUser;

//   const MyBottomSheet({
//     super.key,
//     Key? key,
//     this.isFirstTime = false,
//     required this.isPurchase,
//     required this.isPremiumUser,
//     required this.isPremiumPlusUser,
//   });

//   @override
//   ConsumerState<MyBottomSheet> createState() => _MyBottomSheetState();
// }

// class _MyBottomSheetState extends ConsumerState<MyBottomSheet> {
//   int selectedOption = 0;
//   var selectedData;
//   var planData;

//   @override
//   Widget build(BuildContext context) {
//     final offeringsRef = ref.watch(subscriptionOfferingsProvider);

//     return Container(
//       height: 600,
//       padding: const EdgeInsets.all(16.0),
//       decoration: const BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.vertical(
//           top: Radius.circular(16.0),
//         ),
//       ),
//       child: SingleChildScrollView(
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 const Text(
//                   'Choose your plan',
//                   style: TextStyle(
//                     fontSize: 18.0,
//                     fontWeight: FontWeight.w600,
//                   ),
//                 ),
//                 GestureDetector(
//                   onTap: () {
//                     Navigator.pop(context);
//                   },
//                   child: const Icon(
//                     Icons.close,
//                     size: 25,
//                   ),
//                 )
//               ],
//             ),
//             const SizedBox(height: 16.0),
//             offeringsRef.when(
//               data: (data) {
//                 selectedData = data[0];
//                 setState(() {});

//                 return SizedBox(
//                   child: ListView.builder(
//                     shrinkWrap: true,
//                     physics: const NeverScrollableScrollPhysics(),
//                     itemCount: 3,
//                     itemBuilder: (context, index) {
//                       final element = data[index];
//                       // oldPurchaseDetail = data[widget.planType];
//                       return Column(
//                         children: [
//                           Container(
//                             decoration: BoxDecoration(
//                               gradient: selectedOption == index
//                                   ? const LinearGradient(
//                                       begin: Alignment.topCenter,
//                                       end: Alignment.bottomCenter,
//                                       colors: [
//                                         Color(0xFFC70973),
//                                         Color(0xFF46239F)
//                                       ],
//                                     )
//                                   : null,
//                               border: selectedOption == index
//                                   ? null
//                                   : Border.all(
//                                       color: const Color(0xFFA1A1A1),
//                                       width: selectedOption == index ? 2.0 : 1,
//                                     ),
//                               borderRadius: BorderRadius.circular(14),
//                             ),
//                             child: Padding(
//                               padding: const EdgeInsets.all(2.0),
//                               child: InkWell(
//                                 onTap: () {
//                                   setState(() {
//                                     selectedOption = index;
//                                     selectedData = data[selectedOption];
//                                     planData = data[selectedOption];
//                                   });
//                                 },
//                                 child: Container(
//                                   decoration: BoxDecoration(
//                                     color: Colors.white,
//                                     border: Border.all(color: Colors.white),
//                                     borderRadius: BorderRadius.circular(12),
//                                   ),
//                                   child: Column(
//                                     crossAxisAlignment:
//                                         CrossAxisAlignment.start,
//                                     children: [
//                                       Padding(
//                                         padding: const EdgeInsets.only(
//                                             left: 16,
//                                             right: 16,
//                                             top: 10,
//                                             bottom: 6),
//                                         child: Row(
//                                           mainAxisAlignment:
//                                               MainAxisAlignment.spaceBetween,
//                                           children: [
//                                             Text(
//                                               getPlanName(
//                                                   element.packageType.name),
//                                               style: const TextStyle(
//                                                 color: Color(0xff626262),
//                                                 fontWeight: FontWeight.w600,
//                                                 fontSize: 18,
//                                               ),
//                                             ),
//                                             SvgPicture.asset(
//                                               selectedOption == index
//                                                   ? AppConstants.selectedRadio
//                                                   : AppConstants
//                                                       .unselectedRadio,
//                                               height: 24,
//                                               width: 24,
//                                             )
//                                           ],
//                                         ),
//                                       ),
//                                       SizedBox(height: 5),
//                                       Padding(
//                                         padding: EdgeInsets.symmetric(
//                                             horizontal: 16),
//                                         child: GradientText(
//                                           gradientDirection:
//                                               GradientDirection.ttb,
//                                           colors: const [
//                                             Color(0xFFC70973),
//                                             Color(0xFF46239F)
//                                           ],
//                                           element.storeProduct.priceString,
//                                           //  +
//                                           //     "/" +
//                                           //     getPlanType(
//                                           //         element.packageType.name),
//                                           style: TextStyle(
//                                             color: AppConstants.primaryColor,
//                                             fontWeight: FontWeight.w700,
//                                             fontSize: 18,
//                                           ),
//                                         ),
//                                       ),
//                                       SizedBox(height: 16),
//                                     ],
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ),
//                           if (index + 1 != data.length)
//                             SizedBox(
//                               height: 18,
//                             )
//                         ],
//                       );
//                     },
//                   ),
//                 );
//               },
//               error: (error, stackTrace) => CupertinoActionSheetAction(
//                 onPressed: () {},
//                 child: const Text("Error loading offers!"),
//               ),
//               loading: () => CircularProgressIndicator(),
//             ),
//             const SizedBox(
//               height: 4,
//             ),
//             NewCustomAppButton(
//                 onTap: () async {
//                   if (planData != null || selectedData != null) {
//                     Navigator.pop(context);
//                     CustomAppLoader.showCustomLoader("Processing...");
//                     // Offerings offerings = await Purchases.getOfferings();
//                     // debugPrint(offerings.toString());
//                     // CustomerInfo customerInfo = await Purchases.getCustomerInfo();
//                     // debugPrint(
//                     //     customerInfo.toString() + selectedOption.toString());
//                     final CustomerInfo customerInfo =
//                         await Purchases.getCustomerInfo();

//                     if (Platform.isAndroid &&
//                         customerInfo.activeSubscriptions.isNotEmpty &&
//                         (widget.isPremiumPlusUser)) {
//                       await Purchases.purchasePackage(planData ?? selectedData,
//                           upgradeInfo: UpgradeInfo(
//                             customerInfo.activeSubscriptions.reversed.first,
//                             prorationMode:
//                                 ProrationMode.immediateAndChargeFullPrice,
//                           )).then((value) {
//                         widget.isPurchase(true, selectedOption);
//                       }).onError((error, stackTrace) {
//                         EasyLoading.dismiss();
//                         EasyLoading.showInfo("Purchase Failed!");
//                         // widget.isPurchase(false);
//                       });
//                     } else {
//                       await Purchases.purchasePackage(
//                         planData ?? selectedData,
//                       ).then((value) {
//                         widget.isPurchase(true, selectedOption);
//                       }).onError((error, stackTrace) {
//                         EasyLoading.dismiss();
//                         EasyLoading.showInfo("Purchase Failed!");
//                         // widget.isPurchase(false);
//                       });
//                     }
//                   } else {
//                     Navigator.of(context).pop();
//                   }
//                 },
//                 isGradient: true,
//                 textColor: Colors.white,
//                 borderRadius: 12,
//                 buttonTxt: "Continue")
//           ],
//         ),
//       ),
//     );
//   }

//   String getPlanName(String name) {
//     if (name.contains("month")) {
//       return "Monthly";
//     } else if (name.contains("annual")) {
//       return "Annually";
//     } else if (name.contains("three")) {
//       return "3 Months";
//     } else {
//       return name;
//     }
//   }

//   String getPlanType(String name) {
//     if (name.contains("month")) {
//       return "month";
//     } else if (name.contains("annual")) {
//       return "year";
//     } else {
//       return name;
//     }
//   }
// }

import 'package:flutter/material.dart';
import 'package:fringle_app/config/config.dart';

import '../models/custom_check_model.dart';
import 'get_color_from.hex.dart';

class AppConstants {
  AppConstants._();

  static Color primaryColor = AppConfig.primaryColor;
  static Color primaryLightColor = AppConfig.primaryLightColor;
  static Color primaryTextColor = const Color(0xFF333333);
  static Color greenB5E1B1 = HexColor("#B5E1B1");
  static Color redEFD0C7 = HexColor("#EFD0C7");
  static Color greenF0FFF0 = HexColor("#F0FFF0");
  static Color grayF1F1F1 = HexColor("#F1F1F1");
  static Color gray737373 = HexColor("#737373");
  static Color redFBEFEB = const Color(0xFFFBEFEB);
  static const double defaultNumericValue = 16.0;

  static const String logo = 'assets/images/logo.svg';
  static const String noDataImage = 'assets/images/no_data_image.png';
  static const String splashLogo = 'assets/images/splash_logo.svg';
  static const String flusterLogo = 'assets/images/fluster_logo.svg';
  static const String flusterLogoPng = 'assets/images/fluster_logo.png';
  static const String fringleTextLogo = 'assets/images/fringle_text_logo.svg';
  static const String loginBackground = 'assets/images/login_background.svg';
  static const String loginPage = 'assets/images/login_page.png';
  static const String emailIcon = 'assets/images/email_ic.svg';
  static const String dmOnIcon = 'assets/images/dm_on_icon.svg';
  static const String dmOffIcon = 'assets/images/dm_off_icon.svg';
  static const String profilePic = 'assets/images/profile_pic.svg';
  static const String filter = 'assets/images/filters.svg';
  static const String notification = 'assets/images/notification.svg';
  static const String notificationPng = 'assets/images/notification.png';
  static const String notificationBellPng =
      'assets/images/notificationbell.svg';
  static const String search = 'assets/images/searchIcon.svg';
  static const String bgNotification = 'assets/images/bg_notification.svg';
  static const String chat = 'assets/images/chat.png';
  static const String account = 'assets/images/account.png';
  static const String accountGreen = 'assets/images/account_green.png';
  static const String favorite = 'assets/images/favorite.png';
  static const String favoriteGreen = 'assets/images/favorite_green.png';
  static const String flowerLogo = 'assets/images/flower_logo.png';
  static const String flowerLogoDeactive =
      'assets/images/flower_logo_deactive.png';
  static const String dislike = 'assets/images/close_red.svg';
  static const String likeGreen = 'assets/images/like_green.svg';
  static const String favoriteBlue = 'assets/images/favorite_blue.svg';
  static const String dummy = 'assets/images/dummy_profilepic.png';
  static const String location = 'assets/images/location_white.svg';
  static const String splashBack = 'assets/images/splash_background.png';
  static const String premiumVector = 'assets/images/premium_vector.svg';
  static const String polygon = 'assets/images/polygon.svg';
  static const String locationIcon = 'assets/images/location_icon.png';
  static const String greenBack = 'assets/images/back_arrow.svg';
  static const String circleClose = 'assets/images/circle_close.png';
  static const String locationIconGreen =
      'assets/images/location_icon_green.png';
  // static const String backIcon = 'assets/images/back_icon.svg';
  // static const String uncheckedIcon = 'assets/images/unchecked_icon.svg';
  // static const String checkedIcon = 'assets/images/checked_icon.svg';
  static const String premiumIcon = 'assets/images/premium_icon.svg';
  static const String premiumPlusIcon = 'assets/images/premium_plus_icon.svg';
  static const String premiumCheckIcon = 'assets/images/premium_check_icon.svg';
  static const String premiumWhiteCheckIcon =
      'assets/images/premium_white_check_icon.svg';
  static const String premiumPlusWhiteCheckIcon =
      'assets/images/premium_plus_white_check_icon.svg';
  static const String premiumCloseIcon = 'assets/images/premium_close_icon.svg';
  static const String premiumWhiteCloseIcon =
      'assets/images/premium_white_close_icon.svg';
  static const String dropdownIcon = 'assets/images/dropdown_arrow.svg';
  static const String maleIcon = 'assets/images/male.svg';
  static const String femaleIcon = 'assets/images/female_icon.svg';
  static const String transgenderIcon = 'assets/images/transgender_icon.svg';
  static const String nonbinaryIcon = 'assets/images/nonbinary_icon.svg';
  static const String uncheckRadioIcon = 'assets/images/uncheck_radio.svg';
  static const String checkRadioIcon = 'assets/images/check_radio.svg';
  static const String selectedRadio = 'assets/images/selected_radio.svg';
  static const String unselectedRadio = 'assets/images/unselected_radio.svg';
  static const String starIcon = 'assets/images/star_icon.svg';
  static const String likeIcon = 'assets/images/like_icon.svg';
  static const String matchIcon = 'assets/images/match_icon.svg';
  static const String searchIcon = 'assets/images/search.svg';
  static const String rightArrowIcon = 'assets/images/right_arrow_icon.svg';
  static const String settingsIcon = 'assets/images/settings.svg';
  static const String editProfile = 'assets/images/edit.svg';
  static const String subscriptionDesc = 'Upgrade to our subscription plan';
  static const String camera = 'assets/images/camera.svg';
  static const String whiteStar = 'assets/images/white_star.svg';
  static const String gridViewIcon = 'assets/images/grid_view_icon.svg';
  static const String swipviewIcon = 'assets/images/swip_view.svg';
  static const String introVideo = 'assets/images/intro_video.svg';
  static const String newLikeIcon = 'assets/images/new_like_icon.png';
  static const String smiley = 'assets/images/smiley.png';
  static const String sendMessage = 'assets/images/send.png';
  static const String blackBackIcon = 'assets/images/black_back_icon.svg';
  static const String videoPlayIcon = 'assets/images/video_play.svg';
  static const int favoritedNotifacation = 1;
  static const int likeNotifacation = 2;
  static const int matchNotifacation = 3;
  static const int messageNotifacation = 4;
  static const String coupleAccount = 'coupleAccount';
  static const String logoBgImage = 'assets/images/logo_bg_img.svg';
  static const String fontStyleName = 'Manrope';
  static const String checkGreenIcon = 'assets/images/check_green_icon.svg';
  static const String infoRedIcon = 'assets/images/info_red_icon.svg';
  static const String backIcon = 'assets/images/back_ic.svg';
  static const String uncheckedIcon = 'assets/images/unchecked_ic.svg';
  static const String checkedIcon = 'assets/images/checked_ic.svg';
  static const String homeIcon = 'assets/images/home.svg';
  static const String homeIconGreen = 'assets/images/home_green.svg';
  static const String accountIcon = 'assets/images/account.svg';
  static const String accountIconGreen = 'assets/images/account_green.svg';
  static const String chatIcon = 'assets/images/chat.svg';
  static const String chatIconGreen = 'assets/images/chat_green.svg';
  static const String requestIcon = 'assets/images/requests.svg';
  static const String requestIconGreen = 'assets/images/requests_green.svg';
  static const String addImage = 'assets/images/gallery.svg';
  static const String closeSmall = 'assets/images/close_small.svg';
  static const String calendarIcon = 'assets/images/calendar.svg';
  static const String currentLocation = 'assets/images/current_location.svg';
  static const String occupationIcon = 'assets/images/occupation.svg';
  static const String locationOutline = 'assets/images/location_outline.svg';
  static const String pronounIcon = 'assets/images/pronounce.svg';
  static const String maleSharpIcon = 'assets/images/male_sharp.svg';
  static const String drinksIcon = 'assets/images/drinks.svg';
  static const String religionIcon = 'assets/images/religion.svg';
  static const String cannabisIcon = 'assets/images/weed.png';
  static const String reportIcon = 'assets/images/report.svg';
  static const String blockIcon = 'assets/images/block.svg';
  static const String emailOutline = 'assets/images/email_outline.svg';

  static LinearGradient defaultGradient = LinearGradient(
    colors: [
      AppConstants.primaryLightColor,
      AppConstants.primaryColor,
    ],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  static List<CustomCipsModel> pronounsList = [
    CustomCipsModel(id: 1, name: "she/her"),
    CustomCipsModel(id: 2, name: "he/him"),
    CustomCipsModel(id: 3, name: "they/them"),
    CustomCipsModel(id: 4, name: "ze/zir"),
    CustomCipsModel(id: 5, name: "xe/xim"),
    CustomCipsModel(id: 6, name: "co/co"),
    CustomCipsModel(id: 7, name: "ey/em"),
    CustomCipsModel(id: 8, name: "ve/ver"),
    CustomCipsModel(id: 9, name: "per/per"),
  ];
  static List<CustomCipsModel> interestsList = [
    CustomCipsModel(id: 1, name: "Travel"),
    CustomCipsModel(id: 2, name: "Reading"),
    CustomCipsModel(id: 3, name: "Pets"),
    CustomCipsModel(id: 4, name: "Books"),
    CustomCipsModel(id: 5, name: "Games"),
    CustomCipsModel(id: 6, name: "Design"),
    CustomCipsModel(id: 7, name: "Art & Craft"),
    CustomCipsModel(id: 8, name: "Dance"),
    CustomCipsModel(id: 9, name: "Music"),
    CustomCipsModel(id: 9, name: "Movie"),
  ];

  static List<CustomCheckModel> genderList = [
    CustomCheckModel(
        id: 1,
        name: "Male",
        selectedIcon: AppConstants.maleIcon,
        unselectedIcon: "",
        isSelected: false),
    CustomCheckModel(
        id: 2,
        name: "Female",
        selectedIcon: AppConstants.femaleIcon,
        unselectedIcon: "",
        isSelected: false),
    CustomCheckModel(
        id: 3,
        name: "Other",
        selectedIcon: AppConstants.transgenderIcon,
        unselectedIcon: "",
        isSelected: false),
  ];

  static List<CustomCheckModel> radioBtnList = [
    CustomCheckModel(
        id: 1,
        name: "Yes",
        selectedIcon: AppConstants.checkRadioIcon,
        unselectedIcon: AppConstants.uncheckRadioIcon,
        isSelected: false),
    CustomCheckModel(
        id: 2,
        name: "No",
        selectedIcon: AppConstants.checkRadioIcon,
        unselectedIcon: AppConstants.uncheckRadioIcon,
        isSelected: false),
  ];

  static List<CustomCheckModel> drinkingAndCannabisList = [
    CustomCheckModel(
      id: 1,
      name: "Yes",
      selectedIcon: AppConstants.checkRadioIcon,
      unselectedIcon: AppConstants.uncheckRadioIcon,
      isSelected: false,
    ),
    CustomCheckModel(
      id: 2,
      name: "No",
      selectedIcon: AppConstants.checkRadioIcon,
      unselectedIcon: AppConstants.uncheckRadioIcon,
      isSelected: false,
    ),
    CustomCheckModel(
      id: 3,
      name: "Socially",
      selectedIcon: AppConstants.checkRadioIcon,
      unselectedIcon: AppConstants.uncheckRadioIcon,
      isSelected: false,
    ),
  ];

  static List<CustomCipsModel> createChipsList(List<CustomCipsModel> items) {
    List<CustomCipsModel> updatedList = items.map((item) {
      return CustomCipsModel(
        id: item.id,
        name: item.name,
        isSelected: item.isSelected,
      );
    }).toList();
    return updatedList;
  }

  static List<CustomCheckModel> createRadioList(List<CustomCheckModel> items) {
    List<CustomCheckModel> updatedList = items.map((item) {
      return CustomCheckModel(
        id: item.id,
        name: item.name,
        selectedIcon: item.selectedIcon,
        unselectedIcon: item.unselectedIcon,
        isSelected: item.isSelected,
      );
    }).toList();
    return updatedList;
  }
}

class FirebaseConstants {
  FirebaseConstants._();

  static const String userProfileCollection = "userProfile";
  static const String userInteractionCollection = "userInteraction";
  static const String matchCollection = "matches";
  static const String chatCollection = "chat";
  static const String verificationFormsCollection = "verificationForms";
  static const String feedsCollection = "feeds";
  static const String deviceTokensCollection = "deviceTokens";
  static const String notificationsCollection = "notifications";
  static const String blockedUsersCollection = "blockedUsers";
  static const String reportsCollection = "reports";
  static const String bannedUsersCollection = "bannedUsers";
  static const String accountDeleteRequestCollection = "accountDeleteRequest";
  static const String appSettingsCollection = "appSettings";
  static const String coupleAccount = "couple_account";
  static const String firebaseServerKey =
      "AAAAsoVcjKs:APA91bEsM__8LkNNIg6Wf_Gh8wa4URFOnNuuGjkXAmXM5__KJWnTMRY_GYoooRrhgLOWAjTNLoSvk2BEL6dHlO2u8MT_IUsM_RQVPYC_rO4Dg5QRUIGkarpv9BzewjjStJfWhGo8Tt_u";

  static const String irlActivitiesCollection = "irlActivities";

  // Storage collections
  static const String userMediaFiles = "userMediaFiles";
}

class HiveConstants {
  HiveConstants._();

  static const String hiveBox = "hiveBox";

  static const String chatWallpaper = "chatWallpaper";
  static const String showCompleteDialog = "showCompleteDialog";
  static const String guidedTour = "guidedTour";

  static const String onboardingCompleted = "onboardingCompleted";
}

///Json
const String countryCodeJson = "assets/json/country_code.json";

/// Lottie Json
const String lottieNoItemFound = "assets/json/lottie/no_item_found.json";

//tutorial text
const String tutorialTextOne =
    "Swiping left will reject the profile and you will not see the profile again in your feed";
const String tutorialTextTwo =
    "Swiping right will notify user about your liking ";

///Images
const String appleLogo = "assets/logos/apple.png";
const String facebookLogo = "assets/logos/facebook.png";
const String googleLogo = "assets/logos/google.png";
const String twitterLogo = "assets/logos/twitter.png";

final emailVerificationRedExp = RegExp(
    r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+");

const List<String> listGender = [
  "Man",
  "Women",
  "Non Binary",
  "Trans",
];

const List<String> listGenderCouple = [
  "Man",
  "Women",
  "Couple",
  "Non Binary",
  "Trans",
];

const List<String> listEthnicity = [
  "African American",
  "Black",
  "Black African",
  "Black American",
  "Black Caribbean",
  "Central Asian",
  "Chinese Caribbean",
  "East Asian",
  "Hispanic",
  "Jewish",
  "Indo - Caribbean",
  "Latina",
  "Latino",
  "Latinx",
  "Middle Eastern",
  "Native American",
  "North African",
  "Pacific Islander",
  "South Asian",
  "Southeast Asian",
  "West Asian ",
  "White",
  "Mediterranean"
];

const List<String> listEducation = [
  "High school",
  "Trade/tech school",
  "In college",
  "Undergraduate degree",
  "In grad school",
  "Graduate degree"
];

const List<String> listExercise = ["Active", "Sometimes", "Almost never"];

const List<String> listDrinking = [
  "Frequently",
  "Socially",
  "Rarely",
  "Never",
  "Sober"
];

const List<String> listSmoking = ["Socially", "Never", "Regularly"];

const List<String> listCannabis = ["Yes", "No", "Socially"];

const List<String> listKids = [
  "Want someday",
  "Don't want",
  "Have and want more",
  "Have and don't want more",
  "Not sure yet",
  "Have kids",
  "Open to kids"
];

const List<String> listZodiacSign = [
  "Aries",
  "Taurus",
  "Gemini",
  "Cancer",
  "Leo",
  "Virgo",
  "Libra",
  "Scorpius",
  "Sagittarius",
  "Capricorn",
  "Aquarius",
  "Pisces"
];
const List<String> listPronounce = [
  "she/her",
  "he/him",
  "they/them",
  "ze/zir",
  "xe/xim",
  "co/co",
  "ey/em",
  "ve/ver",
  "per/per",
];

const List<String> listWhoIs = [
  "Straight",
  "Bi - Curious",
  "Bi - sexual",
  "Gay",
  "Lesbian",
  "Fluid",
  "Pansexual",
  "LGBTQ+"
];

const List<String> listRelationship = [
  "Traditional (Monogamous)",
  "Keep it casual",
  "Non - Monogamy (ENM)",
  "Swinging - only to premium"
];

const List<String> listKink = ["Dom", "Sub", "Switch"];
const List<String> listContentCreator = [
  "Influencers",
  "Spicy Creators",
  "Content Creators"
];
const List<String> seekingFilterList = [
  "Traditional (Monogamous)",
  "Keep it casual",
  "Non - Monogamy (ENM)",
];
const List<String> seekingFilterListWithPremium = [
  "Traditional (Monogamous)",
  "Keep it casual",
  "Non - Monogamy (ENM)",
  "Couples",
  "Kinksters",
];
const List<String> seekingFilterListWithPremiumPlus = [
  "Traditional (Monogamous)",
  "Keep it casual",
  "Non - Monogamy (ENM)",
  "Couples",
  "Kinksters",
  "Collaborators"
];

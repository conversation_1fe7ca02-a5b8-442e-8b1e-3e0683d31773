import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../helpers/constants.dart';
import '../../../models/notification_model.dart';
import '../../../providers/user_profile_provider.dart';
import '../../custom/custom_app_bar.dart';
import 'filter_screen.dart';
import 'home_page.dart';
import 'irl_activities_tab.dart';

class HomeTabPage extends ConsumerStatefulWidget {
  final Function(NotificationModel notificationData)? onTapChange;

  const HomeTabPage({super.key, this.onTapChange});

  @override
  ConsumerState<HomeTabPage> createState() => _HomeTabPageState();
}

class _HomeTabPageState extends ConsumerState<HomeTabPage> with TickerProviderStateMixin {
  late TabController _tabController;

  int selectedIndex = 0;

  FilterData? filterData;
  bool isRefresh = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_handleTabSelection);
  }

  void _handleTabSelection() {
    setState(() {
      selectedIndex = _tabController.index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          systemOverlayStyle: SystemUiOverlayStyle.dark,
          leading: const SizedBox(),
          toolbarHeight: 0,
        ),
        body: Column(
          children: [
            const SizedBox(height: AppConstants.defaultNumericValue),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultNumericValue),
              child: CustomAppBar(
                leading: SvgPicture.asset(AppConstants.fringleTextLogo),
                title: Consumer(
                  builder: (context, ref, _) {
                    final user = ref.watch(userProfileFutureProvider);
                    return user.when(
                      data: (data) {
                        if (data?.userAccountSettingsModel.showOnlineStatus != false) {
                          if (data?.isOnline == false) {
                            ref.read(userProfileNotifier).updateUserProfile(data!.copyWith(isOnline: true));
                          }
                        }
                        return const SizedBox();
                      },
                      error: (_, __) => const SizedBox(),
                      loading: () => const SizedBox(),
                    );
                  },
                ),
                trailing: InkWell(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => FilterScreen(
                          isPremiumUser: true,
                          isPremiumPlusUser: true,
                          isCoupleAccount: false,
                          filterData: (data) {
                            setState(() {
                              filterData = data;
                              isRefresh = false;
                            });
                            Future.delayed(const Duration(milliseconds: 100), () {
                              setState(() {
                                isRefresh = true;
                              });
                            });
                          },
                        ),
                      ),
                    );
                  },
                  child: Container(
                    margin: const EdgeInsets.only(right: 4),
                    child: SvgPicture.asset(AppConstants.filter),
                  ),
                ),
              ),
            ),
            TabBar(
              controller: _tabController,
              unselectedLabelColor: Colors.black,
              indicatorSize: TabBarIndicatorSize.tab,
              indicator: const UnderlineTabIndicator(
                borderSide: BorderSide(color: Color(0xFFC70973), width: 4.0),
              ),
              dividerHeight: 4,
              dividerColor: const Color(0xFFD9D9D9),
              tabs: [
                Tab(
                  height: 50,
                  child: Center(
                    child: Text(
                      'Online',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: (selectedIndex == 0)
                            ? Colors.black
                            : const Color(0xFF737373),
                      ),
                    ),
                  ),
                ),
                Tab(
                  height: 50,
                  child: Text(
                    'IRL',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontFamily: AppConstants.fontStyleName,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: (selectedIndex == 1)
                          ? Colors.black
                          : const Color(0xFF737373),
                    ),
                  ),
                )
              ],
            ),
            const SizedBox(height: 12),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                physics: const NeverScrollableScrollPhysics(),
                children: const [
                  HomePage(),
                  IrlActivitiesTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabSelection);
    _tabController.dispose();
    super.dispose();
  }
}

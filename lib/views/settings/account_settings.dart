import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/models/user_profile_model.dart';
import 'package:fringle_app/providers/auth_providers.dart';
import 'package:fringle_app/providers/user_profile_provider.dart';
import 'package:fringle_app/views/custom/custom_confirmation_dialog.dart';
import 'package:fringle_app/views/custom/custom_textfield_old.dart';
import 'package:fringle_app/views/others/error_page.dart';
import 'package:fringle_app/views/others/loading_page.dart';
import 'package:fringle_app/views/settings/couple_account_details.dart';
import 'package:fringle_app/views/settings/couple_account_page.dart';
import 'package:fringle_app/views/tabs/home/<USER>';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
// import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
import 'package:url_launcher/url_launcher.dart';
import '../auth/login_page.dart';
import '../custom/custom_app_loader.dart';
import '../custom/custom_button.dart';
import '../custom/custom_new_app_bar.dart';
import '../custom/custom_status_bar_theme.dart';
import '../custom/custom_two_button_dialog.dart';
import 'couple_prefrences.dart';

class AccountSettingsLandingWidget extends ConsumerWidget {
  final Widget Function(UserProfileModel data)? builder;
  const AccountSettingsLandingWidget({
    super.key,
    this.builder,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(userProfileFutureProvider);

    return user.when(
      data: (data) {
        return data == null
            ? const ErrorPage()
            : builder == null
                ? AccountSettingsPage(
                    isPremiumPlusUser: true,
                    isPremiumUser: true,
                  )
                : builder!(data);
      },
      error: (_, __) => const ErrorPage(),
      loading: () => const LoadingPage(),
    );
  }
}

class AccountSettingsPage extends ConsumerStatefulWidget {
  final bool isPremiumUser;
  final bool isPremiumPlusUser;
  const AccountSettingsPage(
      {super.key,
      required this.isPremiumUser,
      required this.isPremiumPlusUser});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _AccountSettingsPageState();
}

class _AccountSettingsPageState extends ConsumerState<AccountSettingsPage> {
  final _inTimeController = TextEditingController();
  final _inTimeZoneController = TextEditingController();
  final _outTimeController = TextEditingController();
  final _outTimeZoneController = TextEditingController();
  late SharedPreferences _prefs;

  bool? isDMOn = false;
  bool? isFeatured = false;
  bool? isPrimeUser = false;
  bool? isPrimePlusUser = false;
  bool? isCoupleAccount = false;
  bool? isSecondaryAccount = false;
  int planType = -1;
  DateTime? featuredTime;
  DateTime? selectedDmDate;
  TimeOfDay? selectedTime;
  TimeOfDay? selectedEndTime;

  @override
  void initState() {
    initPrefrence();
    setSettings(ref);
    // setSubscriptionUpdate();
    // ref.invalidate(isPremiumPlusUserProvider);
    // ref.invalidate(premiumCustomerInfoProvider);
    // ref.invalidate(userProfileFutureProvider);
    // WidgetsBinding.instance.addPersistentFrameCallback((timeStamp) {
    // ref.invalidate(isPremiumPlusUserProvider);
    // ref.invalidate(isPremiumUserProvider);
    // });
    super.initState();
  }

  Future<void> initPrefrence() async {
    _prefs = await SharedPreferences.getInstance();
    isSecondaryAccount = _prefs.getBool(AppConstants.coupleAccount);
  }

  // here we are checking that subscription is for current logged in user or not and set subscription variable true or false accordingly

  // void setSubscriptionUpdate() {
  //   final userRef = ref.read(userProfileFutureProvider);

  //   userRef.when(
  //       data: (value) => {
  //             if (value != null)
  //               {
  //                 ref
  //                     .read(userProfileNotifier)
  //                     .isUserPremium()
  //                     .then((data) async {
  //                   if (data == value.userId) {
  //                     if (widget.isPremiumUser && !widget.isPremiumPlusUser) {
  //                       var preferences = value.preferences;
  //                       if (preferences != null) {
  //                         preferences.remove("contentCreator");
  //                       }
  //                       ref.read(userProfileNotifier).updateUserProfile(
  //                           value.copyWith(
  //                               isPremiumUser: true, isPremiumPlusUser: false));
  //                       // ref.read(userProfileNotifier).updateUserProfile(
  //                       //     value.copyWith(
  //                       //         isPremiumPlusUser: false,
  //                       //         preferences: preferences));
  //                     } else if (!widget.isPremiumUser &&
  //                         widget.isPremiumPlusUser) {
  //                       ref.read(userProfileNotifier).updateUserProfile(
  //                           value.copyWith(
  //                               isPremiumPlusUser: true, isPremiumUser: false));
  //                     } else if (widget.isPremiumPlusUser &&
  //                         widget.isPremiumUser) {
  //                       ref.read(userProfileNotifier).updateUserProfile(
  //                           value.copyWith(
  //                               isPremiumPlusUser: true, isPremiumUser: false));
  //                     }
  //                     ref.invalidate(userProfileFutureProvider);
  //                   } else {
  //                     var preferences = value.preferences;
  //                     if (preferences != null) {
  //                       preferences.remove("kink");
  //                       preferences.remove("contentCreator");

  //                       if (preferences["seeking"] == "Couple") {
  //                         preferences.remove("seeking");
  //                       }
  //                     }
  //                     ref
  //                         .read(userProfileNotifier)
  //                         .updateUserProfile(value.copyWith(
  //                             isPremiumUser: false,
  //                             isPremiumPlusUser: false,
  //                             isFeaturedOn: false,
  //                             planType: -1,
  //                             isCoupleAccount: false,
  //                             couplePrefrencePrimary: {},
  //                             couplePrefrenceSecond: {},
  //                             isDMOn: false,
  //                             // ignore: prefer_null_aware_operators
  //                             preferences: preferences));
  //                     final coupleUserCollection = FirebaseFirestore.instance
  //                         .collection(FirebaseConstants.coupleAccount);
  //                     coupleUserCollection
  //                         .where("coupleId", isEqualTo: value.userId)
  //                         .get()
  //                         .then((coupleData) async {
  //                       if (coupleData.docs.isNotEmpty) {
  //                         var coupleUserId =
  //                             coupleData.docs.first.data()['userId'];
  //                         coupleUserCollection.doc(value.userId).delete();
  //                         await ref.read(authProvider).deleteUser(coupleUserId);
  //                       }
  //                     });
  //                     ref.invalidate(userProfileFutureProvider);
  //                     if (isSecondaryAccount ?? false) {
  //                       ref.read(userProfileNotifier).updateOnlineStatus(
  //                           isOnline: false, userId: value.userId);

  //                       ref.read(authProvider).signOut();
  //                       _prefs.setBool(AppConstants.coupleAccount, false);
  //                       EasyLoading.showToast(
  //                           "Unfortunately, you are not an authorized person to access this app.");

  //                       // ignore: use_build_context_synchronously
  //                       Navigator.pushAndRemoveUntil(
  //                           context,
  //                           MaterialPageRoute(
  //                               builder: (_) => const LendingPage()),
  //                           (route) => false);
  //                     }
  //                   }
  //                 })
  //               }
  //           },
  //       error: (_, __) => const SizedBox(),
  //       loading: () => const SizedBox());
  // }

  Future<void> _launchUrlForAndroid() async {
    try {
      if (!await launchUrl(
          Uri.parse('https://play.google.com/store/account/subscriptions'))) {
        throw Exception(
            'Could not launch https://play.google.com/store/account/subscriptions');
      }
    } catch (e) {
      print(e.toString());
    }
  }

  Future<void> _launchUrlForIOS() async {
    try {
      if (!await launchUrl(
          Uri.parse('https://apps.apple.com/account/subscriptions'))) {
        throw Exception(
            'Could not launch https://apps.apple.com/account/subscriptions');
      }
    } catch (e) {
      print(e.toString());
    }
  }

  setSettings(WidgetRef ref) {
    // final user = ref.read(userProfileFutureProvider);

    // return user.when(
    //     data: (data) {
    //       if (data != null) {
    //         debugPrint("tap set");

    //         isDMOn = data.isDMOn ?? false;
    //         isFeatured = data.isFeaturedOn ?? false;
    //         featuredTime = data.featuredTime ?? DateTime.now();
    //         isPrimeUser = data.isPremiumUser ?? false;
    //         planType = data.planType ?? -1;
    //         if (isFeatured == true) {
    //           //featuredTime!.add(const Duration(days: 1));
    //           if (featuredTime!
    //               .add(const Duration(days: 1))
    //               .isAfter(DateTime.now())) {
    //             isFeatured = true;
    //             setState(() {});
    //           } else {
    //             isFeatured = false;
    //             setState(() {});
    //           }
    //         }
    //       }
    //     },
    //     error: (_, __) => const SizedBox(),
    //     loading: () => const SizedBox());
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(userProfileFutureProvider);

    return user.when(
        data: (data) {
          if (data != null) {
            isDMOn = data.isDMOn ?? false;
            isFeatured = data.isFeaturedOn ?? false;
            featuredTime = data.featuredTime ?? DateTime.now();
            isPrimeUser = data.isPremiumUser ?? false;
            isPrimePlusUser = data.isPremiumPlusUser ?? false;
            isCoupleAccount = data.isCoupleAccount ?? false;
            planType = data.planType ?? -1;
            if (isFeatured == true) {
              //featuredTime!.add(const Duration(days: 1));
              if (featuredTime!
                  .add(const Duration(days: 1))
                  .isAfter(DateTime.now())) {
                isFeatured = true;
              } else {
                isFeatured = false;
              }
            }
            setState(() {});
          }
          return data == null
              ? const Center(child: Text("Not Available"))
              : CustomStatusBarTheme(
                  isLightTheme: false,
                  child: Scaffold(
                    backgroundColor: const Color(0xffFFFCF5),
                    appBar: GradientAppBar(
                      title: "Account settings".toUpperCase(),
                    ),
                    body: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // const SizedBox(height: AppConstants.defaultNumericValue),
                          // Padding(
                          //   padding: const EdgeInsets.only(
                          //       left: 29, right: 29, top: 24),
                          //   child: NewCustomAppButton(
                          //       isGradient: true,
                          //       borderRadius: 8,
                          //       textColor: Colors.white,
                          //       begin: Alignment.topCenter,
                          //       end: Alignment.bottomCenter,
                          //       onTap: () {
                          //         Navigator.of(context).push(
                          //             MaterialPageRoute(builder: (context) {
                          //           return MembershipScreen(
                          //             isPremiumUser: isPrimeUser ?? false,
                          //             isPremiumPlusUser:
                          //                 isPrimePlusUser ?? false,
                          //           );
                          //         }));
                          //       },
                          //       buttonTxt: ((isPrimeUser ?? false) ||
                          //               (isPrimePlusUser ?? false))
                          //           ? "Change Subscription"
                          //           : "Subscribe Now"),
                          // ),
                          Padding(
                            padding: const EdgeInsets.only(
                                left: 16, top: 40, right: 16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'User Settings',
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleLarge!
                                      .copyWith(fontWeight: FontWeight.w600),
                                ),
                                Container(
                                  padding: const EdgeInsets.only(top: 30),
                                  width: MediaQuery.of(context).size.width,
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          Text(
                                            'Turn Off DM',
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleMedium!
                                                .copyWith(
                                                    fontWeight:
                                                        FontWeight.normal),
                                          ),
                                          const SizedBox(
                                            width: 8,
                                          ),
                                          SizedBox(
                                            width: 90,
                                            child: CustomButton(
                                              text: "Premium",
                                              onPressed: () {
                                                // Navigator.of(context).push(
                                                //     MaterialPageRoute(
                                                //         builder: (context) {
                                                //   return const MembershipScreen();
                                                // }));
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                      // Switch(
                                      //   // This bool value toggles the switch.
                                      //   value: isDMOn!,
                                      //   activeTrackColor:
                                      //       const Color(0xff891688),
                                      //   activeColor: Colors.white,
                                      //   onChanged: (bool value) {
                                      //     if ((isPrimeUser == true) ||
                                      //         (isPrimePlusUser == true)) {
                                      //       showDialog(
                                      //           context: context,
                                      //           builder:
                                      //               (BuildContext context) {
                                      //             return alertPopup(
                                      //                 ref,
                                      //                 'DM',
                                      //                 isDMOn! ? "Off" : "On",
                                      //                 value);
                                      //           });
                                      //     } else {
                                      //       showDialog(
                                      //           context: context,
                                      //           builder:
                                      //               (BuildContext mContext) {
                                      //             return CustomConfirmationDialog(
                                      //               title:
                                      //                   "Unlock Premium Features",
                                      //               desciption: AppConstants
                                      //                   .subscriptionDesc,
                                      //               onButtonClick: () {
                                      //                 Navigator.of(context).push(
                                      //                     MaterialPageRoute(
                                      //                         builder:
                                      //                             (context) {
                                      //                   return MembershipScreen(
                                      //                     isPremiumUser:
                                      //                         isPrimeUser ??
                                      //                             false,
                                      //                     isPremiumPlusUser:
                                      //                         isPrimePlusUser ??
                                      //                             false,
                                      //                   );
                                      //                 }));
                                      //               },
                                      //             );
                                      //           });
                                      //     }
                                      //   },
                                      // ),
                                    ],
                                  ),
                                ),
                                const SizedBox(
                                  height: 16,
                                ),
                                Text(
                                  "Turn off the DM's from all the users for a particular time period",
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleMedium!
                                      .copyWith(
                                          fontWeight: FontWeight.normal,
                                          fontSize: 12,
                                          color: const Color(0xff737373)),
                                ),
                                Container(
                                  padding: const EdgeInsets.only(top: 30),
                                  width: MediaQuery.of(context).size.width,
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          Text(
                                            'Get Featured',
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleMedium!
                                                .copyWith(
                                                    fontWeight:
                                                        FontWeight.normal),
                                          ),
                                          const SizedBox(
                                            width: 8,
                                          ),
                                          SizedBox(
                                            width: 90,
                                            child: CustomButton(
                                              text: "Premium",
                                              onPressed: () {
                                                // Navigator.of(context).push(
                                                //     MaterialPageRoute(
                                                //         builder: (context) {
                                                //   return const MembershipScreen();
                                                // }));
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                      // Switch(
                                      //   // This bool value toggles the switch.

                                      //   value: isFeatured!,
                                      //   activeTrackColor:
                                      //       const Color(0xff891688),
                                      //   activeColor: Colors.white,
                                      //   onChanged: (bool value) {
                                      //     if ((isPrimeUser == true) ||
                                      //         (isPrimePlusUser == true)) {
                                      //       showDialog(
                                      //           context: context,
                                      //           builder:
                                      //               (BuildContext context) {
                                      //             return alertPopup(
                                      //                 ref,
                                      //                 'featured',
                                      //                 isFeatured!
                                      //                     ? "Off"
                                      //                     : "On",
                                      //                 value);
                                      //           });
                                      //     } else {
                                      //       showDialog(
                                      //           context: context,
                                      //           builder:
                                      //               (BuildContext mContext) {
                                      //             return CustomConfirmationDialog(
                                      //               title:
                                      //                   "Unlock Premium Features",
                                      //               desciption: AppConstants
                                      //                   .subscriptionDesc,
                                      //               onButtonClick: () {
                                      //                 Navigator.of(context).push(
                                      //                     MaterialPageRoute(
                                      //                         builder:
                                      //                             (context) {
                                      //                   return MembershipScreen(
                                      //                     isPremiumUser:
                                      //                         isPrimeUser ??
                                      //                             false,
                                      //                     isPremiumPlusUser:
                                      //                         isPrimePlusUser ??
                                      //                             false,
                                      //                   );
                                      //                 }));
                                      //               },
                                      //             );
                                      //           });
                                      //     }
                                      //   },
                                      // ),
                                    ],
                                  ),
                                ),
                                const SizedBox(
                                  height: 16,
                                ),
                                Text(
                                  "Increases my chance of getting noticed",
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleMedium!
                                      .copyWith(
                                          fontWeight: FontWeight.normal,
                                          fontSize: 12,
                                          color: const Color(0xff737373)),
                                ),
                                Visibility(
                                  visible: (((isPrimeUser ?? false) ||
                                          (isPrimePlusUser ?? false)) &&
                                      !(isCoupleAccount ?? false)),
                                  child: Padding(
                                    padding: const EdgeInsets.fromLTRB(
                                        15, 24, 15, 0),
                                    child: InkWell(
                                      onTap: () {
                                        Navigator.of(context).push(
                                            MaterialPageRoute(
                                                builder: (context) {
                                          return const CoupleAccountPage();
                                        }));
                                      },
                                      child: Container(
                                        width:
                                            MediaQuery.of(context).size.width,
                                        decoration: BoxDecoration(
                                          gradient: const LinearGradient(
                                            begin: Alignment.topCenter,
                                            end: Alignment.bottomCenter,
                                            colors: [
                                              Color(0xFFC70973),
                                              Color(0xFF46239F)
                                            ],
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.all(2.0),
                                          child: Container(
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              border: Border.all(
                                                  color: Colors.white),
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                            ),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 18),
                                              child: Center(
                                                child: GradientText(
                                                  gradientDirection:
                                                      GradientDirection.ttb,
                                                  colors: const [
                                                    Color(0xFFC70973),
                                                    Color(0xFF46239F)
                                                  ],
                                                  "Convert to Couple Account",
                                                  style: TextStyle(
                                                    color: AppConstants
                                                        .primaryColor,
                                                    fontWeight: FontWeight.w700,
                                                    fontSize: 12,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(
                            height: 24,
                          ),
                          const Divider(
                            color: Color(0xFFD1D1D1),
                            height: 1,
                          ),
                          const SizedBox(
                            height: 25,
                          ),
                          InkWell(
                            onTap: () {
                              if (isCoupleAccount ?? false) {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        const CouplePrefrences(),
                                  ),
                                );
                              } else {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        const PreferencePage(),
                                  ),
                                );
                              }
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Padding(
                                  padding: EdgeInsets.only(left: 16),
                                  child: Text(
                                    "Preferences",
                                    style: TextStyle(
                                        fontSize: 18,
                                        color: Colors.black,
                                        fontWeight: FontWeight.w600),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(right: 16),
                                  child: SvgPicture.asset(
                                    AppConstants.rightArrowIcon,
                                    height: 24,
                                    width: 24,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(
                            height: 25,
                          ),
                          const Divider(
                            color: Color(0xFFD1D1D1),
                            height: 1,
                          ),

                          Padding(
                            padding: const EdgeInsets.fromLTRB(16, 0, 16, 60),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  padding: const EdgeInsets.only(top: 24),
                                  child: const Text(
                                    'Other Settings',
                                    style: TextStyle(
                                        fontSize: 18,
                                        color: Colors.black,
                                        fontWeight: FontWeight.w600),
                                  ),
                                ),
                                Visibility(
                                    //visible: isPrimeUser ?? false,
                                    child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const SizedBox(
                                      height: 28,
                                    ),
                                    Visibility(
                                      visible: (isCoupleAccount ?? false),
                                      child: InkWell(
                                        onTap: () async {
                                          if (isSecondaryAccount ?? false) {
                                            EasyLoading.showToast(
                                                "You do not have the required authorization.");
                                            return;
                                          } else {
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    const CoupleAccountDetials(),
                                              ),
                                            );
                                          }
                                        },
                                        child: Text(
                                          'Remove Secondary Account',
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleMedium!
                                              .copyWith(
                                                  fontWeight:
                                                      FontWeight.normal),
                                        ),
                                      ),
                                    ),
                                    (isCoupleAccount ?? false)
                                        ? const SizedBox(
                                            height: 21,
                                          )
                                        : const SizedBox.shrink(),
                                    InkWell(
                                      onTap: () async {
                                        if (isSecondaryAccount ?? false) {
                                          EasyLoading.showToast(
                                              "You do not have the required authorization.");
                                          return;
                                        }
                                        if ((isPrimeUser == true) ||
                                            (isPrimePlusUser == true)) {
                                          if (Platform.isAndroid) {
                                            _launchUrlForAndroid();
                                          } else if (Platform.isIOS) {
                                            _launchUrlForIOS();
                                          }
                                        } else {
                                          // ignore: use_build_context_synchronously
                                          showDialog(
                                              context: context,
                                              builder: (BuildContext mContext) {
                                                return CustomConfirmationDialog(
                                                  title: "",
                                                  desciption:
                                                      "You don't have any subsctiption plan.",
                                                  onButtonClick: () {},
                                                );
                                              });
                                        }
                                      },
                                      child: Text(
                                        'Cancel Subscription',
                                        style: Theme.of(context)
                                            .textTheme
                                            .titleMedium!
                                            .copyWith(
                                                fontWeight: FontWeight.normal),
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 21,
                                    ),
                                    // InkWell(
                                    //   onTap: () async {
                                    //     if (isSecondaryAccount ?? false) {
                                    //       EasyLoading.showToast(
                                    //           "You do not have the required authorization.");
                                    //       return;
                                    //     }
                                    //     if (planType == -1) {
                                    //       showDialog(
                                    //           context: context,
                                    //           builder: (BuildContext mContext) {
                                    //             return CustomConfirmationDialog(
                                    //               title: "",
                                    //               desciption:
                                    //                   "You don't have any subsctiption plan.",
                                    //               onButtonClick: () {},
                                    //             );
                                    //           });
                                    //       return;
                                    //     }
                                    //     CustomAppLoader.showCustomLoader(
                                    //         "Restore Purchase...");

                                    //     var offerings =
                                    //         await Purchases.getOfferings();
                                    //     // CustomerInfo customerInfo =
                                    //     //     await Purchases.restorePurchases();

                                    //     // debugPrint(customerInfo.toString());

                                    //     var desiredOffering =
                                    //         offerings.getOffering(
                                    //             offerings.current!.identifier);
                                    //     await Purchases.purchasePackage(
                                    //             desiredOffering!
                                    //                     .availablePackages[
                                    //                 planType])
                                    //         .then((value) {
                                    //       EasyLoading.dismiss();
                                    //       EasyLoading.showInfo(
                                    //           "Restore Purchase Successfully");
                                    //       final user = ref
                                    //           .read(userProfileFutureProvider);
                                    //       user.when(
                                    //           data: (data) async {
                                    //             if (data != null) {
                                    //               // planType<3
                                    //               planType < 3
                                    //                   ? ref
                                    //                       .read(
                                    //                           userProfileNotifier)
                                    //                       .updateUserProfile(
                                    //                           data.copyWith(
                                    //                               isPremiumUser:
                                    //                                   true))
                                    //                   : ref
                                    //                       .read(
                                    //                           userProfileNotifier)
                                    //                       .updateUserProfile(
                                    //                           data.copyWith(
                                    //                               isPremiumPlusUser:
                                    //                                   true));
                                    //               ref.invalidate(
                                    //                   userProfileFutureProvider);
                                    //             }
                                    //           },
                                    //           error: (_, __) =>
                                    //               const SizedBox(),
                                    //           loading: () => const SizedBox());
                                    //     }).onError((error, stackTrace) {
                                    //       EasyLoading.dismiss();

                                    //       EasyLoading.showInfo(
                                    //           "Restore Purchase Failed!");
                                    //       // widget.isPurchase(false);
                                    //     });
                                    //   },
                                    //   child: Text(
                                    //     'Restore Purchase',
                                    //     style: Theme.of(context)
                                    //         .textTheme
                                    //         .titleMedium!
                                    //         .copyWith(
                                    //             fontWeight: FontWeight.normal),
                                    //   ),
                                    // ),
                                  
                                  ],
                                )),
                                const SizedBox(
                                  height: 21,
                                ),
                                InkWell(
                                  onTap: () {
                                    showDialog(
                                        context: context,
                                        builder: (BuildContext context) {
                                          return alertPopup(
                                              ref, 'Logout', "", false);
                                        });
                                  },
                                  child: Text(
                                    'Logout',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium!
                                        .copyWith(
                                            fontWeight: FontWeight.normal),
                                  ),
                                ),
                                const SizedBox(
                                  height: 21,
                                ),
                                InkWell(
                                  onTap: () {
                                    if (isSecondaryAccount ?? false) {
                                      EasyLoading.showToast(
                                          "You do not have the required authorization.");
                                      return;
                                    }
                                    showDialog(
                                        context: context,
                                        builder: (BuildContext context) {
                                          return CustomTwoButtonWithInputDialog(
                                            title:
                                                "Are you sure you want to delete you account?",
                                            description:
                                                "If you delete your account, you will permanently loose all your data and this action cannot be undone.",
                                            onAuthenticate: (password) {
                                              onDeleteUser(password);
                                            },
                                            isDeleteAccount: true,
                                          );
                                        });
                                  },
                                  child: Text(
                                    'Delete Account',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium!
                                        .copyWith(
                                            fontWeight: FontWeight.normal,
                                            color: const Color(0xFFDD0000)),
                                  ),
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                );
        },
        error: (_, __) => const SizedBox(),
        loading: () => const SizedBox());
  }

  String _formatHour(int hour) {
    if (hour > 12) {
      return (hour - 12).toString().padLeft(2, "0");
    } else if (hour == 0) {
      return "12";
    } else {
      return hour.toString().padLeft(2, "0");
    }
  }

  onDmOnOff(WidgetRef ref, dynamic context, bool value, DateTime dmDate,
      TimeOfDay dmStartTime, TimeOfDay dmEndTime) {
    final user = ref.read(userProfileFutureProvider);

    return user.when(
        data: (data) async {
          if (data != null) {
            if (value == true) {
              // final currentUserId = ref.read(currentUserStateProvider)?.uid;
              isDMOn = value;
              setState(() {});
              ref.read(userProfileNotifier).updateUserProfile(data.copyWith(
                  isDMOn: isDMOn,
                  dmDate: dmDate,
                  dmStartTime: DateTime(
                    dmDate.year,
                    dmDate.month,
                    dmDate.day,
                    dmStartTime.hour,
                    dmStartTime.minute,
                  ),
                  dmEndTime: DateTime(
                    dmDate.year,
                    dmDate.month,
                    dmDate.day,
                    dmEndTime.hour,
                    dmEndTime.minute,
                  )));
            } else {
              isDMOn = value;
              setState(() {});
              ref.read(userProfileNotifier).updateUserProfile(
                  data.copyWith(isDMOn: isDMOn, dmDate: null));
            }

            // ref
            //     .read(userProfileNotifier)
            //     .updateUserProfile(data.copyWith(isDMOn: value, ));
            // setState(() {
            //   isDMOn = value;
            // });
            // EasyLoading.showInfo("Preferences applied successfully");

            ref.invalidate(userProfileFutureProvider);
            Navigator.pop(context);
          }
        },
        error: (e, _) => const SizedBox(),
        loading: () => const SizedBox());
  }

  onFeaturedOnOff(
      WidgetRef ref, dynamic context, bool value, DateTime featuredDate) {
    final currentUserId = ref.read(currentUserStateProvider)?.uid;

    final user = ref.read(userProfileFutureProvider);

    return user.when(
        data: (data) async {
          if (data != null) {
            // final currentUserId = ref.read(currentUserStateProvider)?.uid;

            isFeatured = value;
            setState(() {});

            ref.read(userProfileNotifier).updateUserProfile(data.copyWith(
                userId: currentUserId!,
                isFeaturedOn: isFeatured,
                featuredTime: featuredDate));

            ref.invalidate(userProfileFutureProvider);
            Navigator.pop(context);
          }
        },
        error: (e, _) => const SizedBox(),
        loading: () => const SizedBox());
  }

  logout(WidgetRef ref, dynamic context) {
    {
      CustomAppLoader.showCustomLoader("Logging out...");
      final currentUserId = ref.read(currentUserStateProvider)?.uid;

      if (currentUserId != null) {
        ref
            .read(userProfileNotifier)
            .updateOnlineStatus(isOnline: false, userId: currentUserId);

        Navigator.pop(context);
      }
      _prefs.setBool(AppConstants.coupleAccount, false);
      ref.read(authProvider).signOut();

      Navigator.pushAndRemoveUntil(context,
          MaterialPageRoute(builder: (BuildContext context) {
        return const LoginPage();
      }), (r) {
        return false;
      });
      // Navigator.pushReplacement(
      //     context,
      //     MaterialPageRoute(
      //       builder: (context) => const LendingPage(),
      //     ));

      EasyLoading.dismiss();

      //  Navigator.pushReplacement(
      //   context,
      //   MaterialPageRoute(
      //     builder: (context) => const LendingPage(),
      //   ),
      // );
    }
  }

  alertPopup(WidgetRef ref, String text, String text2, bool value) {
    //   String pro = provider.toLowerCase().contains("google") ? "Google" : "Apple";
    return AlertDialog(
      contentPadding: const EdgeInsets.only(top: 30, left: 25, right: 25),
      content: Text(
        text == "Logout"
            ? "Are you sure you want to Logout?"
            : text == "DM"
                ? "Are you sure you want to Turn $text2 DM"
                : "Are you sure you want to get Featured $text2",
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
      ),
      actionsAlignment: MainAxisAlignment.spaceEvenly,
      actions: <Widget>[
        // Row(
        //   mainAxisAlignment: MainAxisAlignment.spaceAround,
        //   children: [
        TextButton(
          style: const ButtonStyle(alignment: Alignment.bottomLeft),
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text(
            "No",
            style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
          ),
        ),
        TextButton(
          style: const ButtonStyle(alignment: Alignment.bottomRight),
          onPressed: () {
            // const duration = Duration(days: 365 * AppConfig.minimumAgeRequired);

            // Handle Google connect action
            if (text == "Logout") {
              logout(ref, context);
            } else if (text == "DM") {
              if (text2 == "On") {
                showDatePicker(
                    context: context,
                    firstDate: DateTime.now(),
                    lastDate: DateTime(2025),
                    initialDate: DateTime.now(),
                    builder: ((context, child) {
                      return Theme(
                          data: Theme.of(context).copyWith(
                              textButtonTheme: TextButtonThemeData(
                                  style: TextButton.styleFrom(
                                      foregroundColor:
                                          const Color(0xFF626262)))),
                          child: child!);
                    })).then((dmDate) {
                  if (dmDate != null) {
                    selectedDmDate = dmDate;
                    //onBirthdaySelected(value);
                    Navigator.pop(context);
                    showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return timePickerDialog(value, dmDate);
                        });
                  }
                });
              } else {
                onDmOnOff(
                    ref,
                    context,
                    value,
                    DateTime.now(),
                    selectedTime ?? const TimeOfDay(hour: 0, minute: 0),
                    selectedEndTime ?? const TimeOfDay(hour: 0, minute: 0));
              }
            } else if (text == "featured") {
              onFeaturedOnOff(ref, context, value, DateTime.now());
            }

            // Perform the necessary actions for Google account connection
          },
          child: const Text(
            "Yes",
            style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
          ),
        ),
      ],
    );
  }

  timePickerDialog(bool value, DateTime dmDate) {
    return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(100),
        ),
        elevation: 0,
        child: Container(
          height: 400,
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(10)),
          child: Padding(
            padding: const EdgeInsets.only(left: 16, right: 16, top: 24),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "Select Duration",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black),
                  ),
                ],
              ),
              const SizedBox(
                height: 40,
              ),
              const Text(
                "from",
                //textAlign: TextAlign.start,
                style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: Colors.black),
              ),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                SizedBox(
                  width: MediaQuery.of(context).size.width / 2,
                  height: 35,
                  child: CustomTextField(
                      controller: _inTimeController,
                      readOnly: true,
                      labelText: "",
                      hintText: "7:55",
                      onTap: () {
                        TimeOfDay initialTime = TimeOfDay.now();
                        showTimePicker(
                            context: context,
                            initialTime: initialTime,
                            builder: (context, childWidget) {
                              return MediaQuery(
                                  data: MediaQuery.of(context).copyWith(
                                      // Using 24-Hour format
                                      alwaysUse24HourFormat: false),
                                  // If you want 12-Hour format, just change alwaysUse24HourFormat to false or remove all the builder argument
                                  child: childWidget!);
                            }).then((v) {
                          if (v != null) {
                            if (selectedDmDate?.day == DateTime.now().day) {
                              if (v.hour <= TimeOfDay.now().hour &&
                                  v.minute <= TimeOfDay.now().minute) {
                                EasyLoading.showError(
                                    "You cannot select past time");
                              } else {
                                _inTimeController.text =
                                    "${_formatHour(v.hour)}:${v.minute.toString().padLeft(2, "0")}";
                                _inTimeZoneController.text =
                                    v.period.name.toUpperCase();
                                selectedTime = v;
                                setState(() {});
                              }
                            } else {
                              _inTimeController.text =
                                  "${_formatHour(v.hour)}:${v.minute.toString().padLeft(2, "0")}";
                              _inTimeZoneController.text =
                                  v.period.name.toUpperCase();
                              selectedTime = v;
                              setState(() {});
                            }
                          }
                        });
                      }),
                ),
                const SizedBox(
                  width: 16,
                ),
                SizedBox(
                  width: MediaQuery.of(context).size.width / 8,
                  height: 35,
                  child: CustomTextField(
                    controller: _inTimeZoneController,
                    readOnly: true,
                    labelText: "",
                    hintText: "AM",
                    textAlign: TextAlign.end,
                  ),
                )
              ]),
              const SizedBox(
                height: 20,
              ),
              const Text(
                "To",
                textAlign: TextAlign.start,
                style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: Colors.black),
              ),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                SizedBox(
                    height: 35,
                    width: MediaQuery.of(context).size.width / 2,
                    child: CustomTextField(
                        controller: _outTimeController,
                        readOnly: true,
                        labelText: "",
                        hintText: "7:55",
                        onTap: () {
                          TimeOfDay initialTime = TimeOfDay.now();

                          showTimePicker(
                              context: context,
                              initialTime: initialTime,
                              builder: (context, childWidget) {
                                return MediaQuery(
                                    data: MediaQuery.of(context).copyWith(
                                        // Using 24-Hour format
                                        alwaysUse24HourFormat: false),
                                    // If you want 12-Hour format, just change alwaysUse24HourFormat to false or remove all the builder argument
                                    child: childWidget!);
                              }).then(
                            (v) {
                              if (v != null) {
                                if (selectedTime != null) {
                                  // if (selectedTime.hour < v.hour) {
                                  if (selectedTime!.period == v.period) {
                                    if (v.hour < selectedTime!.hour) {
                                      EasyLoading.showError(
                                          "End time can not be less than start time");
                                    } else if (v.minute <
                                        selectedTime!.minute) {
                                      EasyLoading.showError(
                                          "End time can not be less than start time");
                                    } else {
                                      _outTimeController.text =
                                          "${_formatHour(v.hour)}:${v.minute.toString().padLeft(2, "0")}";
                                      _outTimeZoneController.text =
                                          v.period.name.toUpperCase();
                                      selectedEndTime = v;
                                      setState(() {});
                                    }
                                  } else {
                                    if (selectedTime!.period.name == "am") {
                                      _outTimeController.text =
                                          "${_formatHour(v.hour)}:${v.minute.toString().padLeft(2, "0")}";
                                      _outTimeZoneController.text =
                                          v.period.name.toUpperCase();
                                      selectedEndTime = v;
                                      setState(() {});
                                    } else {
                                      if (v.period.name == "am") {
                                        EasyLoading.showError(
                                            "End time can not be less than start time");
                                      } else {
                                        _outTimeController.text =
                                            "${_formatHour(v.hour)}:${v.minute.toString().padLeft(2, "0")}";
                                        _outTimeZoneController.text =
                                            v.period.name.toUpperCase();
                                        selectedEndTime = v;
                                        setState(() {});
                                      }
                                    }
                                  }

                                  // }else{
                                  //   _outTimeController.text =
                                  //         "${v.hour.toString().padLeft(2, "0")}:${v.minute.toString().padLeft(2, "0")}";
                                  //     _outTimeZoneController.text =
                                  //         v.period.name.toUpperCase();
                                  //     setState(() {});
                                  // }
                                } else {
                                  EasyLoading.showInfo(
                                      "Please Select Start Time First");
                                }
                              }
                            },
                          );
                        })),
                const SizedBox(
                  width: 16,
                ),
                SizedBox(
                  height: 35,
                  width: MediaQuery.of(context).size.width / 8,
                  child: CustomTextField(
                    controller: _outTimeZoneController,
                    readOnly: true,
                    labelText: "",
                    hintText: "AM",
                    textAlign: TextAlign.end,
                  ),
                ),
              ]),
              const SizedBox(
                height: 40,
              ),
              CustomButton(
                  onPressed: () {
                    String dmStartTime =
                        "${_inTimeController.text} ${_inTimeZoneController.text}";
                    String dmEndTime =
                        "${_outTimeController.text} ${_outTimeZoneController.text}";
                    onDmOnOff(ref, context, value, dmDate, selectedTime!,
                        selectedEndTime!);
                    _inTimeController.text = "";
                    _outTimeController.text = "";
                    _inTimeZoneController.text = "";
                    _outTimeZoneController.text = "";
                  },
                  text: "Okay"),
              const SizedBox(
                height: 8,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                      _inTimeController.text = "";
                      _outTimeController.text = "";
                      _inTimeZoneController.text = "";
                      _outTimeZoneController.text = "";
                    },
                    child: const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Text(
                        "cancel",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.w700,
                            fontSize: 12),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(
                height: 24,
              ),
            ]),
          ),
        ));
  }

  onDeleteUser(String password) async {
    // Add delete Request!
    final currentUserRef = ref.read(currentUserStateProvider);
    if (currentUserRef != null) {
      User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        AuthCredential credential = EmailAuthProvider.credential(
          email: user.email ?? "",
          password: password, // Replace with the user's actual password
        );
        try {
          await user.reauthenticateWithCredential(credential);
          await user.delete();
        } catch (e) {
          EasyLoading.showToast("Incorrect password.");
          return;
        }
      }
      CustomAppLoader.showCustomLoader("Deleting Account, please wait...");
      final userId = currentUserRef.uid;

      FirebaseFirestore.instance
          .collection(FirebaseConstants.userProfileCollection)
          .doc(userId)
          .delete();

      CollectionReference userInteractionCollection = FirebaseFirestore.instance
          .collection(FirebaseConstants.userInteractionCollection);

      QuerySnapshot querySnapshotUserId = await userInteractionCollection
          .where('userId', isEqualTo: userId)
          .get();

      for (var document in querySnapshotUserId.docs) {
        document.reference.delete();
      }
      _prefs.setBool(AppConstants.coupleAccount, false);

      QuerySnapshot querySnapshotIntractToUserId =
          await userInteractionCollection
              .where('intractToUserId', isEqualTo: userId)
              .get();

      for (var document in querySnapshotIntractToUserId.docs) {
        document.reference.delete();
      }

      CollectionReference notificationCollection = FirebaseFirestore.instance
          .collection(FirebaseConstants.notificationsCollection);

      QuerySnapshot qsUserId =
          await notificationCollection.where('userId', isEqualTo: userId).get();

      for (var document in qsUserId.docs) {
        document.reference.delete();
      }

      QuerySnapshot qsreceiverId = await notificationCollection
          .where('receiverId', isEqualTo: userId)
          .get();

      for (var document in qsreceiverId.docs) {
        document.reference.delete();
      }

      CollectionReference deviceTokenCollection = FirebaseFirestore.instance
          .collection(FirebaseConstants.deviceTokensCollection);

      QuerySnapshot qsDeviceUserId =
          await deviceTokenCollection.where('userId', isEqualTo: userId).get();

      for (var document in qsDeviceUserId.docs) {
        document.reference.delete();
      }

      CollectionReference matchesCollection = FirebaseFirestore.instance
          .collection(FirebaseConstants.matchCollection);

      QuerySnapshot qsMatchUserId =
          await matchesCollection.where('userIds', arrayContains: userId).get();

      qsMatchUserId.docs.forEach((document) async {
        CollectionReference chatCollection =
            document.reference.collection(FirebaseConstants.chatCollection);
        QuerySnapshot qsChat = await chatCollection.get();

        for (var chatdocument in qsChat.docs) {
          chatdocument.reference.delete();
        }

        document.reference.delete();
      });
      FirebaseFirestore.instance
          .collection(FirebaseConstants.coupleAccount)
          .doc(userId)
          .delete();
      //ref.read(authProvider).deleteAccount;
      //ref.watch(currentUserStateProvider)!.delete();

      Navigator.pop(context);

      Navigator.pushAndRemoveUntil(context,
          MaterialPageRoute(builder: (context) {
        return const LoginPage();
      }), (r) {
        return false;
      });

      EasyLoading.dismiss();

      // await ref.read(authProvider).signOut().then((value) async {

      // });
    } else {
      EasyLoading.showToast("Something wants wrong...");
    }
  }
}

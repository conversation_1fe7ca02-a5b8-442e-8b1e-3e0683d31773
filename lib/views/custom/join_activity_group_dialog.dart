import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../helpers/constants.dart';
import '../../helpers/custom_cache_image.dart';
import '../../models/irl_activity_model.dart';
import 'custom_button.dart';

class JoinActivityGroupDialog extends StatelessWidget {
  final IrlActivityModel activity;
  final VoidCallback onJoin;

  const JoinActivityGroupDialog({
    super.key,
    required this.activity,
    required this.onJoin,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 37),
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
                child: CustomCacheImage(
                  imageUrl: activity.imageUrl,
                  height: 190,
                  width: double.infinity,
                ),
              ),
              Positioned(
                top: 12,
                right: 12,
                child: Container(
                  height: 24,
                  width: 24,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    padding: EdgeInsets.zero,
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.close_rounded,
                      color: Colors.black,
                      size: 16,
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 20),
          Text(
            "Join the activity Group",
            style: GoogleFonts.manrope(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          if (activity.description != null && activity.description!.trim().isNotEmpty) ...[
            SizedBox(height: 10),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultNumericValue),
              child: Text(
                activity.description!,
                style: GoogleFonts.manrope(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF6A6A6A),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 40,
              vertical: 24,
            ),
            child: CustomButton(
              onPressed: () => onJoin.call(),
              text: "Join",
            ),
          ),
        ],
      ),
    );
  }
}

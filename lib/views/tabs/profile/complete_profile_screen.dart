import 'package:flutter_svg/svg.dart';
import 'package:fringle_app/config/config.dart';
import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/helpers/validators.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:intl/intl.dart';
import '../../../models/custom_check_model.dart';
import '../../../models/user_account_settings_model.dart';
import '../../../models/user_profile_model_new.dart';
import '../../custom/app_toast.dart';
import '../../custom/custom_button.dart';
import '../../custom/custom_check_button.dart';
import '../../custom/custom_clip_item.dart';
import '../../custom/custom_textfield.dart';
import '../../others/set_user_location_page.dart';

class CompleteProfileScreen extends StatefulWidget {
  final UserProfileModelNew userProfileModelNew;
  final Function(UserProfileModelNew) onSelected;

  const CompleteProfileScreen({
    super.key,
    required this.userProfileModelNew,
    required this.onSelected,
  });

  @override
  State<CompleteProfileScreen> createState() => _CompleteProfileScreenState();
}

class _CompleteProfileScreenState extends State<CompleteProfileScreen> {
  List<CustomCipsModel> pronounsList =
      AppConstants.createChipsList(AppConstants.pronounsList);
  List<CustomCipsModel> interestsList =
      AppConstants.createChipsList(AppConstants.interestsList);
  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  TextEditingController bioController = TextEditingController();
  TextEditingController addressController = TextEditingController();
  TextEditingController birthdayController = TextEditingController();
  UserLocation? userLocation;
  String? selectedGender;
  DateTime? selectedBirthday;
  int pronounsCount = 0;
  int interestsCount = 0;

  final _formKey = GlobalKey<FormState>();
  List<CustomCheckModel> items =
      AppConstants.createRadioList(AppConstants.genderList);

  void _handleGenderSelectionChanged(int itemId, bool isSelected) {
    setState(() {
      items = items.map<CustomCheckModel>((item) {
        if (item.id == itemId) {
          selectedGender = item.name;
          return item.copyWith(isSelected: isSelected);
        } else {
          return item.copyWith(isSelected: false);
        }
      }).toList();
    });
  }

  @override
  void initState() {
    super.initState();

    var data = widget.userProfileModelNew;
    firstNameController.text = data.firstName ?? "";
    lastNameController.text = data.lastName ?? "";
    selectedGender = data.gender ?? "Male";
    for (var item in items) {
      if (item.name == selectedGender) {
        item.isSelected = true;
        break; // If you only expect one match, you can break the loop after updating.
      }
    }
    for (var item in data.pronounceList ?? []) {
      for (var pro in pronounsList) {
        if (pro.name == item.toString()) {
          pro.copyWith(isSelected: true);
          break;
        }
      }
    }
    bioController.text = data.bio ?? "";
    addressController.text = data.address ?? "";
    for (var item in data.interests ?? []) {
      if (interestsList.any((interest) =>
          interest.name.toLowerCase().trim() !=
          item.toString().toLowerCase().trim())) {
        interestsList.add(CustomCipsModel(
            id: (interestsList.length + 1), name: item.toString()));
      }
      for (var pro in interestsList) {
        if (pro.name == item.toString()) {
          pro.copyWith(isSelected: true);
          break;
        }
      }
    }
    birthdayController.text = data.birthDay != null
        ? DateFormat("MM/dd/yyyy").format(data.birthDay ?? DateTime.now())
        : "";
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 24,
              ),
              CustomTextField2(
                controller: firstNameController,
                labelText: "First name",
                hintText: "First name",
                validator: Validators.validateName,
                autovalidateMode: AutovalidateMode.onUserInteraction,
              ),
              const SizedBox(
                height: 24,
              ),
              CustomTextField2(
                controller: lastNameController,
                labelText: "Last name (Optional)",
                hintText: "Last name",
                //validator: Validators.validateName,
              ),
              const SizedBox(
                height: 24,
              ),
              CustomTextField2(
                controller: birthdayController,
                labelText: 'DOB',
                hintText: "MM/DD/YYYY",
                readOnly: true,
                validator: (String? value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select your DOB';
                  }
                  return null;
                },
                suffixIcon: Padding(
                  padding: const EdgeInsets.only(right: 20),
                  child: SvgPicture.asset(
                    AppConstants.calendarIcon,
                  ),
                ),
                // suffixIconConstraints:
                //     BoxConstraints(maxHeight: 24, maxWidth: 24),
                onTap: () {
                  const duration = Duration(days: 365 * AppConfig.minimumAgeRequired);
                  showDatePicker(
                    context: context,
                    firstDate: DateTime(1900),
                    lastDate: DateTime.now().subtract(duration),
                    initialDate: selectedBirthday ?? DateTime.now().subtract(duration),
                    builder: (context, child) {
                      return Theme(
                        data: Theme.of(context).copyWith(
                          colorScheme: ColorScheme.light(
                            primary: AppConstants.primaryColor,
                          ),
                        ),
                        child: child!,
                      );
                    },
                  ).then((value) {
                    if (value != null) {
                      //onBirthdaySelected(value);
                      setState(() {
                        selectedBirthday = value;
                        birthdayController.text = DateFormat("MM/dd/yyyy").format(value);
                      });
                    }
                  });
                },
              autovalidateMode: AutovalidateMode.onUserInteraction,
              ),
              const SizedBox(
                height: 24,
              ),
              const Text(
                "Gender",
                style: TextStyle(
                  fontFamily: AppConstants.fontStyleName,
                  color: Color(0xff6A6A6A),
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  // Additional InputDecoration properties...
                ),
              ),
              const SizedBox(
                height: 7,
              ),
              Wrap(
                spacing: 7,
                runSpacing: 19,
                children: items.map((item) {
                  return SizedBox(
                    width: (MediaQuery.of(context).size.width - 55) / 2,
                    child: GenderItemWidget(
                      item: item,
                      onSelectionChanged: (isSelected) {
                        _handleGenderSelectionChanged(item.id, isSelected);
                      },
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(
                height: 24,
              ),
              const Text(
                "Pronouns (Optional)",
                style: TextStyle(
                  fontFamily: AppConstants.fontStyleName,
                  color: Color(0xff6A6A6A),
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  // Additional InputDecoration properties...
                ),
              ),
              const SizedBox(
                height: 7,
              ),
              Wrap(
                runSpacing: 8,
                spacing: 8,
                children: List.generate(pronounsList.length, (index) {
                  return ClipItemWidget(
                    item: pronounsList[index],
                    onSelectionChanged: (isSelected) {
                      setState(() {
                        if (pronounsCount < 3 && isSelected) {
                          pronounsList[index].isSelected = isSelected;

                          pronounsCount++;
                        } else if (!isSelected) {
                          pronounsCount--;
                          pronounsList[index].isSelected = isSelected;
                        } else {
                          EasyLoading.showToast(
                              "You can only select 3 pronouns.");
                        }
                      });
                    },
                  );
                }),
              ),
              const SizedBox(
                height: 24,
              ),
              CustomTextField2(
                controller: bioController,
                labelText: 'Bio',
                hintText: 'Bio',
                validator: Validators.validateBio,
                minLines: 3,
                maxLength: 250,
                autovalidateMode: AutovalidateMode.onUserInteraction,
              ),
              const SizedBox(
                height: 24,
              ),
              CustomTextField2(
                readOnly: false,
                onTap: () async {
                  UserLocation? location = await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const SetUserLocation(),
                      fullscreenDialog: true,
                    ),
                  );
                  addressController.text = "";

                  if (location != null) {
                    userLocation = location;
                    addressController.text = location.addressText;
                    widget.userProfileModelNew.address = location.addressText;
                    widget.userProfileModelNew.latitude = location.latitude;
                    widget.userProfileModelNew.longitude = location.longitude;
                    widget.userProfileModelNew.userAccountSettingsModel = UserAccountSettingsModel(
                      location: userLocation ??
                          UserLocation(
                            addressText: location.addressText,
                            latitude: location.latitude,
                            longitude: location.longitude,
                          ),
                      maximumAge: AppConfig.maximumUserAge,
                      minimumAge: AppConfig.minimumAgeRequired,
                    );
                    setState(() {});
                  }
                },
                controller: addressController,
                hintText: 'Location',
                labelText: 'Location',
                hintColor: const Color(0xFF303030),
                validator: Validators.validateAddress,
                suffixIcon: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  child: SvgPicture.asset(AppConstants.currentLocation),
                ),
                autovalidateMode: AutovalidateMode.onUserInteraction,
              ),
              const SizedBox(
                height: 24,
              ),
            ],
          ),
          Container(
              margin: const EdgeInsets.only(
                top: 32,
              ),
              child: CustomButton(
                text: "Continue",
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    var userData = widget.userProfileModelNew;
                    userData.firstName = firstNameController.text;
                    userData.lastName = lastNameController.text;
                    userData.gender = selectedGender;
                    userData.pronounceList = pronounsList
                        .where((pronoun) =>
                            pronoun.isSelected) // Filter selected pronouns
                        .map((pronoun) => pronoun.name) // Extract names
                        .toList();
                    // userData.interests = interestsList
                    //     .where((interests) =>
                    //         interests.isSelected) // Filter selected interests
                    //     .map((interests) => interests.name) // Extract names
                    //     .toList();
                    userData.bio = bioController.text;
                    userData.birthDay = selectedBirthday;
                    userData.address = addressController.text;
                    // TODO: Add Real Location
                    userData.latitude = 28.8;
                    userData.longitude = 77.1;
                    widget.onSelected(userData);
                  } else {
                    CustomToast.showToast(message: "Please fill in all the mandatory fields to continue");
                  }
                },
              )),
        ],
      ),
    );
  }
}

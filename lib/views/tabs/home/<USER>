import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../helpers/constants.dart';
import '../../../providers/user_profile_provider.dart';
import '../../custom/custom_button.dart';

class TutorialDialog extends StatelessWidget {
  final Function() onTapAction;
  const TutorialDialog({super.key, required this.onTapAction});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      backgroundColor: Colors.white,
      insetPadding: const EdgeInsets.symmetric(horizontal: 36),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
        width: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              tutorialTextOne,
              textAlign: TextAlign.center,
              style: GoogleFonts.manrope(
                color: Colors.black,
                fontSize: 14,
                fontWeight: FontWeight.normal,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              tutorialTextTwo,
              textAlign: TextAlign.center,
              style: GoogleFonts.manrope(
                color: Colors.black,
                fontSize: 14,
                fontWeight: FontWeight.normal,
              ),
            ),
            const SizedBox(height: 14),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Consumer(builder: (context, ref, _) {
                  final user = ref.watch(userProfileFutureProvider);
                  return user.when(
                    data: (data) {
                      return CustomButton(
                        width: 100,
                        height: 28,
                        padding: const EdgeInsets.symmetric(vertical: 5),
                        borderRadius: 8,
                        onPressed: () {
                          onTapAction.call();
                          Navigator.pop(context);
                        },
                        child: const Center(
                          child: Text(
                            "Ok",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ),
                      );
                    },
                    error: (_, __) => const SizedBox(),
                    loading: () => const SizedBox(),
                  );
                }),
              ],
            )
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';

class CustomAppButtonBorder extends StatefulWidget {
  final Function() onTap;
  final String buttonTxt;

  const CustomAppButtonBorder(
      {super.key, required this.onTap, required this.buttonTxt});

  @override
  State<CustomAppButtonBorder> createState() => _CustomAppButtonBorderState();
}

class _CustomAppButtonBorderState extends State<CustomAppButtonBorder> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        widget.onTap();
      },
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
            border: Border.all(color: Color(0xff359D2C)),
            borderRadius: BorderRadius.circular(16),
          ),

        // onPressed: _login,
        child: Center(
          child: Text(
            widget.buttonTxt,
            style: const TextStyle(
                color: Color(0xff359D2C), fontSize: 14, fontWeight: FontWeight.w600),
          ),
        ),
      ),
    );
  }
}

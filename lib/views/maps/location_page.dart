// import 'package:fringle_app/helpers/constants.dart';
// import 'package:fringle_app/models/user_account_settings_model.dart';
// import 'package:fringle_app/views/custom/custom_button.dart';
// import 'package:fringle_app/views/others/set_user_location_page.dart';
// import 'package:flutter/material.dart';

// class UserLocationScreen extends StatelessWidget {
//   // final Function(UserLocation location) onLocationChanged;
//   // final UserLocation? location;
//   // final VoidCallback onNext;
//   // final VoidCallback onBack;
//   const UserLocationScreen({
//     Key? key,
//     // required this.onLocationChanged,
//     // this.location,
//     // required this.onNext,
//     // required this.onBack,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return SingleChildScrollView(
//       padding: const EdgeInsets.all(AppConstants.defaultNumericValue * 2),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.stretch,
//         children: [
//           Text(
//             "My location is",
//             style: Theme.of(context)
//                 .textTheme
//                 .headlineSmall!
//                 .copyWith(fontWeight: FontWeight.bold),
//           ),
//           const SizedBox(height: AppConstants.defaultNumericValue),
//           // const SizedBox(
//           //   height: 300,
//           //   child: Center(
//           //       child: Text(
//           //     "Not Yet Implemented!\nYou can move on!",
//           //     textAlign: TextAlign.center,
//           //   )),
//           // ),
//           GestureDetector(
//             onTap: () async {
//               final location = await Navigator.push(
//                 context,
//                 MaterialPageRoute(
//                   builder: (context) => const SetUserLocation(),
//                   fullscreenDialog: true,
//                 ),
//               );

//               if (location != null) {
//                 // onLocationChanged(location);
//               }
//             },
//             child: Container(
//               padding: const EdgeInsets.all(AppConstants.defaultNumericValue),
//               decoration: BoxDecoration(
//                 border: Border.all(
//                   color: Theme.of(context).primaryColor,
//                 ),
//                 borderRadius:
//                     BorderRadius.circular(AppConstants.defaultNumericValue),
//               ),
//               child: Row(
//                 children: [
//                   Icon(
//                     Icons.location_on,
//                     color: Theme.of(context).primaryColor,
//                   ),
//                   const SizedBox(width: AppConstants.defaultNumericValue),
//                   Expanded(
//                     child: Text(
//                       location?.addressText ?? "Tap to set location",
//                       style: Theme.of(context)
//                           .textTheme
//                           .bodyLarge!
//                           .copyWith(fontWeight: FontWeight.bold),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//           const SizedBox(height: AppConstants.defaultNumericValue),
//           Text(
//             "You must set your location to use this app!\nOther users need to know the distance between you and them to use the app.",
//             style: Theme.of(context)
//                 .textTheme
//                 .bodySmall!
//                 .copyWith(fontWeight: FontWeight.bold),
//           ),
//           const SizedBox(height: AppConstants.defaultNumericValue * 2),
//           // Row(
//           //   children: [
//           //     Expanded(
//           //         child: CustomButton(
//           //             onPressed: onBack, text: "Back".toUpperCase()
//           //             )),
//           //     const SizedBox(width: AppConstants.defaultNumericValue),
//           //     Expanded(
//           //         child: CustomButton(
//           //             onPressed: onNext, text: "Continue".toUpperCase())),
//           //   ],
//           // ),
          
//           const SizedBox(height: AppConstants.defaultNumericValue * 2),
//         ],
//       ),
//     );
//   }
// }

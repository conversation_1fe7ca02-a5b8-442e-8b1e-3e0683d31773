import 'package:fringle_app/helpers/constants.dart';
import 'package:fringle_app/models/user_profile_model.dart';
import 'package:fringle_app/providers/user_profile_provider.dart';
import 'package:fringle_app/views/custom/custom_dropdown_new.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../custom/custom_button.dart';
import '../../custom/custom_new_app_bar.dart';

class PreferencePage extends ConsumerStatefulWidget {
  final bool isFromFilter;
  const PreferencePage({super.key, this.isFromFilter = false});

  @override
  ConsumerState<PreferencePage> createState() => _PreferencePageState();
}

class _PreferencePageState extends ConsumerState<PreferencePage> {
  String? selectedIam;
  String? selectedWhoIs;
  String? selectedSeeking;
  String? selectedWhoIs2;
  String? selectedRelation;
  String? selectedKink;
  String? selectedContentCreator;
  UserProfileModel? currentUser;
  bool isPrimeUser = false;
  bool isPrimePlusUser = false;

  @override
  void initState() {
    setPreferences(ref);
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color(0xFFFFFCF5),
        appBar: GradientAppBar(
          title: "PREFERENCES",
        ),
        body: Column(children: [
          Expanded(
            child: SingleChildScrollView(
              child: Container(
                padding: const EdgeInsets.fromLTRB(20, 17, 20, 20),
                width: double.infinity,
                child: Column(
                  children: [
                    CustomDropdownNew(
                      items: listGender,
                      selectedValue: selectedIam,
                      onChanged: (newValue) {
                        //Update the selected value
                        setState(() {
                          selectedIam = newValue;
                        });
                      },
                      labelText: 'I am',
                      hintText: 'Select',
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    CustomDropdownNew(
                      items: listWhoIs,
                      selectedValue: selectedWhoIs,
                      onChanged: (newValue) {
                        // Update the selected value
                        setState(() {
                          selectedWhoIs = newValue;
                        });
                      },
                      labelText: 'Who is',
                      hintText: 'Select',
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    CustomDropdownNew(
                      items: (isPrimeUser || isPrimePlusUser)
                          ? listGenderCouple
                          : listGender,
                      selectedValue: selectedSeeking,
                      onChanged: (newValue) {
                        // Update the selected value
                        setState(() {
                          selectedSeeking = newValue;
                        });
                      },
                      labelText: 'Seeking',
                      hintText: 'Select',
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    CustomDropdownNew(
                      items: listWhoIs,
                      selectedValue: selectedWhoIs2,
                      onChanged: (newValue) {
                        // Update the selected value
                        setState(() {
                          selectedWhoIs2 = newValue;
                        });
                      },
                      labelText: 'Who is',
                      hintText: 'Select',
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    if (!widget.isFromFilter)
                      CustomDropdownNew(
                        items: listRelationship,
                        selectedValue: selectedRelation,
                        onChanged: (newValue) {
                          // Update the selected value
                          setState(() {
                            selectedRelation = newValue;
                          });
                        },
                        labelText: 'Type of Relationship',
                        hintText: 'Select',
                      ),
                    const SizedBox(
                      height: 16,
                    ),
                    if (!widget.isFromFilter)
                      Visibility(
                        visible: (isPrimeUser || isPrimePlusUser),
                        child: CustomDropdownNew(
                          items: listKink,
                          selectedValue: selectedKink,
                          onChanged: (newValue) {
                            // Update the selected value
                            setState(() {
                              selectedKink = newValue;
                            });
                          },
                          labelText: 'Kink',
                          hintText: 'Select',
                        ),
                      ),
                    const SizedBox(
                      height: 16,
                    ),
                    if (!widget.isFromFilter)
                      Visibility(
                        visible: (isPrimePlusUser),
                        child: CustomDropdownNew(
                          items: listContentCreator,
                          selectedValue: selectedContentCreator,
                          onChanged: (newValue) {
                            // Update the selected value
                            setState(() {
                              selectedContentCreator = newValue;
                            });
                          },
                          labelText: 'Content Creator Collaboration',
                          hintText: 'Select',
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
          Row(
            children: [
              Flexible(
                child: Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: 24, horizontal: 8),
                    child: SizedBox(
                        height: 52,
                        child: CustomButton(
                            onPressed: () {
                              selectedContentCreator = null;
                              selectedIam = null;
                              selectedKink = null;
                              selectedRelation = null;
                              selectedSeeking = null;
                              selectedWhoIs = null;
                              selectedWhoIs2 = null;
                              onReset(ref);
                            },
                            text: "Reset"))),
              ),
              Flexible(
                child: Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: 24, horizontal: 8),
                    child: SizedBox(
                        height: 52,
                        child: CustomButton(
                            onPressed: () {
                              onApply(ref);
                            },
                            text: "Apply"))),
              )
            ],
          )
        ]));
  }

  setPreferences(WidgetRef ref) {
    final user = ref.read(userProfileFutureProvider);

    return user.when(
        data: (data) {
          if (data != null) {
            setState(() {
              isPrimeUser = data.isPremiumUser ?? false;
              isPrimePlusUser = data.isPremiumPlusUser ?? false;
            });

            Map<String, dynamic>? selectedPreferences;
            selectedPreferences = data.preferences;

            selectedPreferences?.forEach((key, value) {
              if (key == "iAm") selectedIam = value;
              if (key == "whoIs") selectedWhoIs = value;
              if (key == "seeking") selectedSeeking = value;
              if (key == "whoIs2") selectedWhoIs2 = value;
              if (key == "typeOfRelationship") selectedRelation = value;
              if (key == "kink") selectedKink = value;
              if (key == "contentCreator") selectedContentCreator = value;
            });
          }
        },
        error: (_, __) => const SizedBox(),
        loading: () => const SizedBox());
  }

  onApply(WidgetRef ref) {
    final user = ref.read(userProfileFutureProvider);

    return user.when(
        data: (data) async {
          if (data != null) {
            Map<String, dynamic>? selectedPreferences;
            selectedPreferences = {
              if (selectedIam != null) "iAm": selectedIam,
              if (selectedWhoIs != null) "whoIs": selectedWhoIs,
              if (selectedSeeking != null) "seeking": selectedSeeking,
              if (selectedWhoIs2 != null) "whoIs2": selectedWhoIs2,
              if (selectedRelation != null)
                "typeOfRelationship": selectedRelation,
              if (selectedKink != null) "kink": selectedKink,
              if (selectedContentCreator != null)
                "contentCreator": selectedContentCreator
            };
            setState(() {});

            if (selectedPreferences.isNotEmpty) {
              // ref.read(userProfileNotifier).updateUserProfile(
              //     data.copyWith(preferences: selectedPreferences));
              // final currentUserId = ref.read(currentUserStateProvider)?.uid;
              ref
                  .read(userProfileNotifier)
                  .updateUserProfile(
                      data.copyWith(preferences: selectedPreferences))
                  .then((value) {
                if (value) {
                  EasyLoading.showInfo("Preferences applied successfully");

                  ref.invalidate(userProfileFutureProvider);
                  Navigator.pop(context);
                } else {
                  EasyLoading.showInfo("Please wait");
                }
              });
            } else {
              EasyLoading.showInfo("Please select preferences");
            }
          }
        },
        error: (_, __) => const SizedBox(),
        loading: () => const SizedBox());
  }

  onReset(WidgetRef ref) {
    final user = ref.read(userProfileFutureProvider);

    return user.when(
        data: (data) async {
          if (data != null) {
            Map<String, dynamic>? selectedPreferences;
            selectedPreferences = {};
            setState(() {});
            ref
                .read(userProfileNotifier)
                .updateUserProfile(
                    data.copyWith(preferences: selectedPreferences))
                .then((value) {
              if (value) {
                EasyLoading.showInfo("Preferences reset successfully!");
                ref.invalidate(userProfileFutureProvider);
                // Navigator.pop(context);
              } else {
                EasyLoading.showInfo("Please wait");
              }
            });
          }
        },
        error: (_, __) => const SizedBox(),
        loading: () => const SizedBox());
  }
}

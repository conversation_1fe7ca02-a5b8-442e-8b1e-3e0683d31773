import 'package:cloud_firestore/cloud_firestore.dart';

class IrlActivityModel {
  final String id;
  final String name;
  final String? subtitle;
  final String? description;
  final String imageUrl;
  final int? order;

  IrlActivityModel({
    required this.id,
    required this.name,
    this.subtitle,
    this.description,
    required this.imageUrl,
    this.order,
  });

  factory IrlActivityModel.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return IrlActivityModel(
      id: doc.id,
      name: data['name'] ?? 'Unnamed Activity',
      subtitle: data['subtitle'] ?? '',
      description: data['description'] ?? '',
      imageUrl: data['imageUrl'] ?? '',
      order: data['order'] as int?,
    );
  }
}

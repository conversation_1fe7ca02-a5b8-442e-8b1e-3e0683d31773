import 'dart:ui';
import 'package:fringle_app/models/user_profile_model.dart';
import 'package:fringle_app/providers/interaction_provider.dart';
import 'package:fringle_app/providers/other_users_provider.dart';
import 'package:fringle_app/providers/user_profile_provider.dart';
import 'package:fringle_app/views/others/user_details_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import '../../../helpers/constants.dart';

class TabLikeMe extends ConsumerStatefulWidget {
  const TabLikeMe({super.key});

  @override
  ConsumerState<TabLikeMe> createState() => _TabLikeMeState();
}

class _TabLikeMeState extends ConsumerState<TabLikeMe>
    with WidgetsBindingObserver {
  List<UserProfileModel> filteredUsersList = [];
  var isProgress = true;
  bool isPrimeUser = false;
  bool isPrimePlusUser = false;

  void getLikedData() {
    filteredUsersList.clear();
    final myProfile = ref.read(userProfileFutureProvider);
    return myProfile.when(
        data: (currentUserdata) {
          isPrimeUser = currentUserdata?.isPremiumUser ?? false;
          isPrimePlusUser = currentUserdata?.isPremiumPlusUser ?? false;

          final filteredUsers = ref.watch(otherUsersProvider);

          return filteredUsers.when(
              data: (users) {
                final interactionProvider =
                    ref.watch(currentUserInteractionProvider);

                return interactionProvider.when(
                  data: (data) {
                    for (final user in users) {
                      for (final interectedUser in data) {
                        if (interectedUser.intractToUserId ==
                            currentUserdata?.userId) {
                          if (interectedUser.userId == user.userId) {
                            if (interectedUser.isLike &&
                                !interectedUser.isMatch) {
                              filteredUsersList.add(user);
                            }
                          }
                        }
                      }
                    }
                  },
                  error: (_, __) => const Center(
                    child: Text("Something Went Wrong!"),
                  ),
                  loading: () => const Center(
                    child: CircularProgressIndicator.adaptive(),
                  ),
                );
              },
              error: (_, __) => const Center(
                    child: Text("Something Went Wrong!"),
                  ),
              loading: () => const Center(
                    child: CircularProgressIndicator.adaptive(),
                  ));
        },
        error: (_, __) => const Center(
              child: Text("Something Went Wrong!"),
            ),
        loading: () => const Center(
              child: CircularProgressIndicator.adaptive(),
            ));
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.invalidate(userProfileFutureProvider);
      ref.invalidate(otherUsersProvider);
      ref.invalidate(currentUserInteractionProvider);
    });
  }

  @override
  Widget build(BuildContext context) {
    //getLikedData();
    // Future.delayed(const Duration(seconds: 20), () {
    //   if (filteredUsersList != null) isProgress = false;
    //   setState(() {});
    // });

    // return isProgress
    //     ? const ShimmerListWithImg()
    //     :

    return Consumer(
      builder: (context, ref, child) {
        filteredUsersList.clear();
        final myProfile = ref.read(userProfileFutureProvider);
        return myProfile.when(
            data: (currentUserdata) {
              isPrimeUser = currentUserdata?.isPremiumUser ?? false;
              isPrimePlusUser = currentUserdata?.isPremiumPlusUser ?? false;

              final filteredUsers = ref.watch(otherUsersProvider);

              return filteredUsers.when(
                  data: (users) {
                    final interactionProvider =
                        ref.watch(currentUserInteractionProvider);

                    return interactionProvider.when(
                      data: (data) {
                        for (final user in users) {
                          for (final interectedUser in data) {
                            if (interectedUser.intractToUserId ==
                                currentUserdata?.userId) {
                              if (interectedUser.userId == user.userId) {
                                if (interectedUser.isLike &&
                                    !interectedUser.isMatch) {
                                  filteredUsersList.add(user);
                                }
                              }
                            }
                          }
                        }

                        return Container(
                            color: Colors.white,
                            child: PageView(
                              physics: const NeverScrollableScrollPhysics(),
                              children: [
                                SingleChildScrollView(
                                    // padding: const EdgeInsets.all(
                                    //     AppConstants.defaultNumericValue * 2),
                                    child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                      // Padding(
                                      // padding: const EdgeInsets.symmetric(
                                      //  horizontal: AppConstants.defaultNumericValue),
                                      const SizedBox(
                                        height: 24,
                                      ),
                                      // Visibility(
                                      //   visible:
                                      //       !(isPrimeUser || isPrimePlusUser),
                                      //   child: InkWell(
                                      //     onTap: () {
                                      //       Navigator.of(context).push(
                                      //           MaterialPageRoute(
                                      //               builder: (context) {
                                      //         return MembershipScreen(
                                      //           isPremiumUser: isPrimeUser,
                                      //           isPremiumPlusUser:
                                      //               isPrimePlusUser,
                                      //         );
                                      //       }));
                                      //     },
                                      //     child: Container(
                                      //       margin: const EdgeInsets.symmetric(
                                      //           horizontal: 24, vertical: 16),
                                      //       padding: const EdgeInsets.all(10),
                                      //       decoration: BoxDecoration(
                                      //         borderRadius:
                                      //             BorderRadius.circular(20),
                                      //         gradient: const LinearGradient(
                                      //           begin: Alignment.topCenter,
                                      //           end: Alignment.bottomCenter,
                                      //           colors: [
                                      //             Color(0xFFC70973),
                                      //             Color(0xFF46239F)
                                      //           ],
                                      //         ),
                                      //       ),
                                      //       child: const Center(
                                      //           child: Text(
                                      //         "Upgrade to view profile",
                                      //         style: TextStyle(
                                      //             color: Colors.white,
                                      //             fontWeight: FontWeight.w400,
                                      //             fontSize: 12),
                                      //       )),
                                      //     ),
                                      //   ),
                                      // ),
                                      
                                      filteredUsersList.isEmpty
                                          ? const Center(
                                              child: Text(
                                                  "No one Liked your profile yet !!"))
                                          : Container(
                                              margin: const EdgeInsets.only(
                                                  top: 24, left: 24, right: 24),
                                              child: Wrap(
                                                spacing: 12,
                                                runSpacing: 12,
                                                alignment: WrapAlignment.start,
                                                children: List.generate(
                                                    filteredUsersList.length,
                                                    (index) {
                                                  String image = '';
                                                  String name = '';
                                                  String age = '';
                                                  //String userId = '';
                                                  UserProfileModel user =
                                                      filteredUsersList[index];

                                                  if (filteredUsersList
                                                          .isNotEmpty &&
                                                      filteredUsersList.length >
                                                          index) {
                                                    user = filteredUsersList[
                                                        index];
                                                    //userId = FavouriteusersList[index].userId;
                                                    if (filteredUsersList[index]
                                                            .profilePicture !=
                                                        null) {
                                                      image = filteredUsersList[
                                                              index]
                                                          .profilePicture!;
                                                    }

                                                    name =
                                                        filteredUsersList[index]
                                                            .firstName ?? '';

                                                    if (filteredUsersList[index]
                                                            .birthDay !=
                                                        null) {
                                                      age = (DateTime.now()
                                                                  .difference(filteredUsersList[
                                                                          index]
                                                                      .birthDay!)
                                                                  .inDays ~/
                                                              365)
                                                          .toString();
                                                    }
                                                  }

                                                  return Container(
                                                    child: SizedBox(
                                                        width: MediaQuery.of(
                                                                    context)
                                                                .size
                                                                .width *
                                                            0.4,
                                                        height: 220,
                                                        child: Container(
                                                          decoration:
                                                              const BoxDecoration(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .all(Radius
                                                                        .circular(
                                                                            10)),
                                                          ),
                                                          child: Center(
                                                            child: Stack(
                                                                children: [
                                                                  Container(
                                                                    child:
                                                                        ClipRRect(
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              10),
                                                                      child:
                                                                          InkWell(
                                                                        onTap:
                                                                            () {
                                                                          if ((isPrimeUser == true) ||
                                                                              (isPrimePlusUser == true)) {
                                                                            Navigator.of(context).push(
                                                                              MaterialPageRoute(
                                                                                  builder: (context) => UserDetailsPage(
                                                                                        user: user,
                                                                                        isPrimeUser: isPrimeUser,
                                                                                        isPrimePlusUser: isPrimePlusUser,
                                                                                      )),
                                                                            );
                                                                          } else {
                                                                            // showDialog(
                                                                            //     context: context,
                                                                            //     builder: (BuildContext mContext) {
                                                                            //       return CustomConfirmationDialog(
                                                                            //         title: "Unlock Premium Features",
                                                                            //         desciption: AppConstants.subscriptionDesc,
                                                                            //         onButtonClick: () {
                                                                            //           Navigator.of(context).push(MaterialPageRoute(builder: (context) {
                                                                            //             return MembershipScreen(
                                                                            //               isPremiumUser: isPrimeUser,
                                                                            //               isPremiumPlusUser: isPrimePlusUser,
                                                                            //             );
                                                                            //           }));
                                                                            //         },
                                                                            //       );
                                                                            //     });
                                                                          }
                                                                        },
                                                                        child: Image
                                                                            .network(
                                                                          image,
                                                                          width:
                                                                              166,
                                                                          height:
                                                                              220,
                                                                          fit: BoxFit
                                                                              .cover,
                                                                          loadingBuilder: (context,
                                                                              child,
                                                                              loadingProgress) {
                                                                            if (loadingProgress ==
                                                                                null)
                                                                              return child;
                                                                            return const Center(child: CircularProgressIndicator.adaptive());
                                                                          },
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                  Positioned(
                                                                    top: 12,
                                                                    right: 12,
                                                                    child: SvgPicture
                                                                        .asset(
                                                                      filteredUsersList[index].isDMOn ??
                                                                              false
                                                                          ? AppConstants
                                                                              .dmOffIcon
                                                                          : AppConstants
                                                                              .dmOnIcon,
                                                                      // height: 15,
                                                                    ),
                                                                  ),
                                                                  Positioned(
                                                                      //<-- SEE HERE
                                                                      left: 10,
                                                                      right: 10,
                                                                      bottom:
                                                                          15,
                                                                      child: Container(
                                                                          alignment: Alignment.center,
                                                                          height: 40,
                                                                          width: 136,
                                                                          decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: Colors.transparent),
                                                                          child: ClipRRect(
                                                                            borderRadius:
                                                                                const BorderRadius.all(
                                                                              Radius.circular(8),
                                                                            ),
                                                                            child: BackdropFilter(
                                                                                filter: ImageFilter.blur(sigmaX: 8.0, sigmaY: 8.0),
                                                                                child: Container(
                                                                                    width: double.infinity,
                                                                                    decoration: const BoxDecoration(
                                                                                        gradient: LinearGradient(
                                                                                      colors: [
                                                                                        Colors.transparent,
                                                                                        Colors.transparent,
                                                                                        Colors.transparent,
                                                                                      ],
                                                                                      begin: Alignment.centerLeft,
                                                                                      end: Alignment.centerRight,
                                                                                    )),
                                                                                    child: Padding(
                                                                                      padding: const EdgeInsets.all(8.0),
                                                                                      child: Row(
                                                                                        children: [
                                                                                          Text(
                                                                                            name.split(" ")[0],
                                                                                            style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 14, color: Colors.white),
                                                                                          ),
                                                                                          const SizedBox(width: 5),
                                                                                          Text(
                                                                                            age,
                                                                                            style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 14, color: Colors.white),
                                                                                          ),
                                                                                        ],
                                                                                      ),
                                                                                    ))),
                                                                          )))
                                                                ]),
                                                          ),
                                                        )),
                                                  );
                                                }),
                                              ),
                                            ),
                                    ])),
                              ],
                            ));
                      },
                      error: (_, __) => const Center(
                        child: Text("Something Went Wrong!"),
                      ),
                      loading: () => const Center(
                        child: CircularProgressIndicator.adaptive(),
                      ),
                    );
                  },
                  error: (_, __) => const Center(
                        child: Text("Something Went Wrong!"),
                      ),
                  loading: () => const Center(
                        child: CircularProgressIndicator.adaptive(),
                      ));
            },
            error: (_, __) => const Center(
                  child: Text("Something Went Wrong!"),
                ),
            loading: () => const Center(
                  child: CircularProgressIndicator.adaptive(),
                ));
      },
    );

    // return Container(
    //     color: Colors.white,
    //     child: PageView(
    //       physics: const NeverScrollableScrollPhysics(),
    //       children: [
    //         SingleChildScrollView(
    //             // padding: const EdgeInsets.all(
    //             //     AppConstants.defaultNumericValue * 2),
    //             child: Column(
    //                 crossAxisAlignment: CrossAxisAlignment.start,
    //                 children: [
    //               // Padding(
    //               // padding: const EdgeInsets.symmetric(
    //               //  horizontal: AppConstants.defaultNumericValue),
    //               const SizedBox(
    //                 height: 24,
    //               ),
    //               InkWell(
    //                 onTap: () {
    //                   Navigator.of(context)
    //                       .push(MaterialPageRoute(builder: (context) {
    //                     return const MembershipScreen();
    //                   }));
    //                 },
    //                 child: Container(
    //                   margin: const EdgeInsets.symmetric(
    //                       horizontal: 24, vertical: 16),
    //                   padding: const EdgeInsets.all(10),
    //                   decoration: BoxDecoration(
    //                     borderRadius: BorderRadius.circular(20),
    //                     gradient: const LinearGradient(
    //                       begin: Alignment.topCenter,
    //                       end: Alignment.bottomCenter,
    //                       colors: [Color(0xFFC70973), Color(0xFF46239F)],
    //                     ),
    //                   ),
    //                   child: const Center(
    //                       child: Text(
    //                     "Upgrade to view profile",
    //                     style: TextStyle(
    //                         color: Colors.white,
    //                         fontWeight: FontWeight.w400,
    //                         fontSize: 12),
    //                   )),
    //                 ),
    //               ),
    //               filteredUsersList.isEmpty
    //                   ? const Center(
    //                       child: Text("No one Liked your profile yet !!"))
    //                   : Container(
    //                       margin: const EdgeInsets.only(
    //                           top: 24, left: 24, right: 24),
    //                       child: Wrap(
    //                         spacing: 12,
    //                         runSpacing: 12,
    //                         alignment: WrapAlignment.start,
    //                         children: List.generate(filteredUsersList.length,
    //                             (index) {
    //                           String image = '';
    //                           String name = '';
    //                           String age = '';
    //                           //String userId = '';
    //                           UserProfileModel user = filteredUsersList[index];

    //                           if (filteredUsersList.isNotEmpty &&
    //                               filteredUsersList.length > index) {
    //                             user = filteredUsersList[index];
    //                             //userId = FavouriteusersList[index].userId;
    //                             if (filteredUsersList[index].profilePicture !=
    //                                 null) {
    //                               image =
    //                                   filteredUsersList[index].profilePicture!;
    //                               debugPrint("imageProfile$image");
    //                             }

    //                             if (filteredUsersList[index].fullName != null) {
    //                               name = filteredUsersList[index].fullName;
    //                             }

    //                             if (filteredUsersList[index].birthDay != null) {
    //                               age = (DateTime.now()
    //                                           .difference(
    //                                               filteredUsersList[index]
    //                                                   .birthDay!)
    //                                           .inDays ~/
    //                                       365)
    //                                   .toString();
    //                             }
    //                           }

    //                           return Container(
    //                             child: SizedBox(
    //                                 width:
    //                                     MediaQuery.of(context).size.width * 0.4,
    //                                 height: 220,
    //                                 child: Container(
    //                                   decoration: const BoxDecoration(
    //                                     borderRadius: BorderRadius.all(
    //                                         Radius.circular(10)),
    //                                   ),
    //                                   child: Center(
    //                                     child: Stack(children: [
    //                                       Container(
    //                                         child: ClipRRect(
    //                                           borderRadius:
    //                                               BorderRadius.circular(10),
    //                                           child: InkWell(
    //                                             onTap: () {
    //                                               if (isPrimeUser == true) {
    //                                                 Navigator.of(context).push(
    //                                                   MaterialPageRoute(
    //                                                       builder: (context) =>
    //                                                           UserDetailsPage(
    //                                                               user: user,
    //                                                               isPrimeUser: isPrimeUser
    //                                                              )),
    //                                                 );
    //                                               } else {
    //                                                 showDialog(
    //                                                     context: context,
    //                                                     builder: (BuildContext
    //                                                         mContext) {
    //                                                       return CustomConfirmationDialog(
    //                                                         title:
    //                                                             "Unlock Premium Features",
    //                                                         desciption: AppConstants
    //                                                             .subscriptionDesc,
    //                                                         onButtonClick: () {
    //                                                           Navigator.of(
    //                                                                   context)
    //                                                               .push(MaterialPageRoute(
    //                                                                   builder:
    //                                                                       (context) {
    //                                                             return const MembershipScreen();
    //                                                           }));
    //                                                         },
    //                                                       );
    //                                                     });
    //                                               }
    //                                             },
    //                                             child: Image.network(
    //                                               image,
    //                                               width: 166,
    //                                               height: 220,
    //                                               fit: BoxFit.cover,
    //                                               loadingBuilder: (context,
    //                                                   child, loadingProgress) {
    //                                                 if (loadingProgress == null)
    //                                                   return child;
    //                                                 return const Center(
    //                                                     child:
    //                                                         CircularProgressIndicator
    //                                                             .adaptive());
    //                                               },
    //                                             ),
    //                                           ),
    //                                         ),
    //                                       ),
    //                                       Positioned(
    //                                           //<-- SEE HERE
    //                                           left: 10,
    //                                           right: 10,
    //                                           bottom: 15,
    //                                           child: Container(
    //                                               alignment: Alignment.center,
    //                                               height: 40,
    //                                               width: 136,
    //                                               decoration: BoxDecoration(
    //                                                   borderRadius:
    //                                                       BorderRadius.circular(
    //                                                           10),
    //                                                   color:
    //                                                       Colors.transparent),
    //                                               child: ClipRRect(
    //                                                 borderRadius:
    //                                                     const BorderRadius.all(
    //                                                   Radius.circular(8),
    //                                                 ),
    //                                                 child: BackdropFilter(
    //                                                     filter:
    //                                                         ImageFilter.blur(
    //                                                             sigmaX: 8.0,
    //                                                             sigmaY: 8.0),
    //                                                     child: Container(
    //                                                         width:
    //                                                             double.infinity,
    //                                                         decoration:
    //                                                             const BoxDecoration(
    //                                                                 gradient:
    //                                                                     LinearGradient(
    //                                                           colors: [
    //                                                             Colors
    //                                                                 .transparent,
    //                                                             Colors
    //                                                                 .transparent,
    //                                                             Colors
    //                                                                 .transparent,
    //                                                           ],
    //                                                           begin: Alignment
    //                                                               .centerLeft,
    //                                                           end: Alignment
    //                                                               .centerRight,
    //                                                         )),
    //                                                         child: Padding(
    //                                                           padding:
    //                                                               const EdgeInsets
    //                                                                   .all(8.0),
    //                                                           child: Row(
    //                                                             children: [
    //                                                               Text(
    //                                                                 name.split(
    //                                                                     " ")[0],
    //                                                                 style: const TextStyle(
    //                                                                     fontWeight:
    //                                                                         FontWeight
    //                                                                             .w600,
    //                                                                     fontSize:
    //                                                                         14,
    //                                                                     color: Colors
    //                                                                         .white),
    //                                                               ),
    //                                                               const SizedBox(
    //                                                                   width: 5),
    //                                                               Text(
    //                                                                 age,
    //                                                                 style: const TextStyle(
    //                                                                     fontWeight:
    //                                                                         FontWeight
    //                                                                             .w600,
    //                                                                     fontSize:
    //                                                                         14,
    //                                                                     color: Colors
    //                                                                         .white),
    //                                                               ),
    //                                                             ],
    //                                                           ),
    //                                                         ))),
    //                                               )))
    //                                     ]),
    //                                   ),
    //                                 )),
    //                           );
    //                         }),
    //                       ),
    //                     ),
    //             ])),
    //       ],
    //     ));
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../helpers/constants.dart';
import '../../helpers/email_verifier.dart';
import '../../providers/auth_providers.dart';
import '../custom/custom_button.dart';
import '../custom/custom_textfield.dart';
import 'reset_password_success_screen.dart';


class PasswordResetScreen extends ConsumerStatefulWidget {
  const PasswordResetScreen({super.key});

  @override
  ConsumerState<PasswordResetScreen> createState() => _PasswordResetScreenState();
}

class _PasswordResetScreenState extends ConsumerState<PasswordResetScreen> {
  final TextEditingController _emailController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return SafeArea(
          child: Scaffold(
            backgroundColor: Colors.transparent,
            appBar: AppBar(
              backgroundColor: AppConstants.primaryColor,
              title: const Text(
                'RESET PASSWORD',
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w400),
              ),
            ),
            body: Container(
              padding: const EdgeInsets.all(20.0),
              color: Colors.transparent,
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          const SizedBox(
                            height: 26,
                          ),
                          const Text(
                            "Enter an email associated with your account and we will send an email with instructions to reset your account password.",
                            style: TextStyle(
                                fontFamily: "Inter",
                                fontWeight: FontWeight.w400,
                                fontSize: 14,
                                color: Colors.white),
                          ),
                          const SizedBox(
                            height: 49,
                          ),
                          CustomTextField(
                            controller: _emailController,
                            labelText: 'Email',
                            hintText: '',
                            keyboardType: TextInputType.emailAddress,
                            inputFormatters: [
                              FilteringTextInputFormatter.deny(RegExp(r'\s')),
                            ],
                            validator: (value) {
                              if (value!.isEmpty) {
                                return 'Please enter your email';
                              } else if (emailVerifier().hasMatch(value) ==
                                  false) {
                                return 'Please enter a valid email';
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                    ),
                    CustomButton(
                      text: 'Send Instructions',
                      onPressed: () {
                        String email = _emailController.text;
                        _resetPassword(email);
                      },
                    ),
                    const SizedBox(height: 36),
                  ],
                ),
              ),
            ),
          ),
    );
  }

  void _resetPassword(String email) async {
    if (_formKey.currentState!.validate()) {
      await AuthProvider.forgotPassword(email: _emailController.text.trim())
          .then((value) {
        if (value) {
          Navigator.of(context).push(MaterialPageRoute(builder: (context) {
            return ResetPasswordSuccessScreen();
          }));
        }
      });
    }
  }
}

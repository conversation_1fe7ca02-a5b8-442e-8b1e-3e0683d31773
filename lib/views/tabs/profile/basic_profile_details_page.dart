import 'package:flutter/material.dart';
import 'package:fringle_app/models/user_profile_model_new.dart';
import '../../../helpers/constants.dart';
import 'complete_profile_screen.dart';

class BasicProfileDetailsPage extends StatelessWidget {
  final UserProfileModelNew userDetailsModel;
  final Function(UserProfileModelNew) onSelected;
  const BasicProfileDetailsPage({super.key, required this.userDetailsModel, required this.onSelected});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                CompleteProfileScreen(
                    userProfileModelNew: userDetailsModel,
                    onSelected: onSelected),
              ],
            ),
          ),
          const SizedBox(height: AppConstants.defaultNumericValue * 2),
        ],
      ),
    );
  }
}

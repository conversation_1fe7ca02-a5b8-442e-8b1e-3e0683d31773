import 'package:flutter/material.dart';
import 'package:fringle_app/views/custom/app_toast.dart';

import '../../../helpers/constants.dart';
import '../../../models/custom_check_model.dart';
import '../../../models/user_profile_model_new.dart';
import '../../custom/custom_button.dart';
import '../../custom/custom_clip_item.dart';

class InterestsDetailPage extends StatefulWidget {
  final UserProfileModelNew userProfileModelNew;
  final Function(UserProfileModelNew) onInterestsSelected;

  const InterestsDetailPage({
    super.key,
    required this.userProfileModelNew,
    required this.onInterestsSelected,
  });

  @override
  State<InterestsDetailPage> createState() => _InterestsDetailPageState();
}

class _InterestsDetailPageState extends State<InterestsDetailPage> {
  List<CustomCipsModel> interestsList = AppConstants.createChipsList(AppConstants.interestsList);

  int interestsCount = 0;

  @override
  void initState() {
    super.initState();
    var data = widget.userProfileModelNew;

    for (var item in data.interests ?? []) {
      final interestName = item.toString().toLowerCase().trim();

      final existingIndex = interestsList.indexWhere(
        (interest) => interest.name.toLowerCase().trim() == interestName,
      );

      if (existingIndex == -1) {
        interestsList.add(
          CustomCipsModel(
            id: interestsList.length + 1,
            name: item.toString(),
            isSelected: true,
          ),
        );
      } else {
        final updated = interestsList[existingIndex].copyWith(isSelected: true);
        interestsList[existingIndex] = updated;
      }
    }

    interestsCount = interestsList.where((interest) => interest.isSelected).length;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          children: [
            Wrap(
              runSpacing: 8,
              spacing: 8,
              children: List.generate(interestsList.length, (index) {
                return ClipItemWidget(
                  item: interestsList[index],
                  onSelectionChanged: (isSelected) {
                    setState(() {
                      if (interestsCount < 3 && isSelected) {
                        interestsList[index].isSelected = isSelected;
                        interestsCount++;
                      } else if (!isSelected) {
                        interestsCount--;
                        interestsList[index].isSelected = isSelected;
                      } else {
                        CustomToast.showToast(message: "You can only select 3 interests.");
                      }
                    });
                  },
                );
              }),
            ),
            Container(
              margin: const EdgeInsets.only(top: 32),
              child: CustomButton(
                text: "Continue",
                onPressed: () {
                  var userData = widget.userProfileModelNew;
                  final interests = interestsList
                        .where((interests) => interests.isSelected)
                        .map((interests) => interests.name)
                        .toList();
                  final updatedUserData = userData.copyWith(interests: interests);
                  widget.onInterestsSelected(updatedUserData);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
